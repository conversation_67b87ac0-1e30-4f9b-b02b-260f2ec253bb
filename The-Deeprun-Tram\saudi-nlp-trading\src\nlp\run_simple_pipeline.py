"""
Main execution script for Simple NLP Pipeline

Focus: Process scraped data and generate trading features.
Measure everything, optimize based on results.
"""

import json
import argparse
import sys
from pathlib import Path
from datetime import datetime
import pandas as pd

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from nlp.simple_pipeline import SimpleNLPPipeline

def load_scraped_data(data_path: str) -> list:
    """
    Load scraped data from various formats
    """
    data_file = Path(data_path)
    
    if not data_file.exists():
        raise FileNotFoundError(f"Data file not found: {data_path}")
    
    print(f"Loading data from {data_path}...")
    
    if data_file.suffix == '.json':
        with open(data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
    elif data_file.suffix == '.parquet':
        df = pd.read_parquet(data_file)
        data = df.to_dict('records')
    else:
        raise ValueError(f"Unsupported file format: {data_file.suffix}")
    
    # Extract posts from scraped data structure
    all_posts = []
    
    if isinstance(data, list):
        for item in data:
            if isinstance(item, dict):
                # Handle scraped thread structure
                if 'posts' in item:
                    posts = item['posts']
                    for post in posts:
                        post['source'] = 'hawamer'
                        post['url'] = item.get('url', '')
                        post['thread_title'] = item.get('title', '')
                        all_posts.append(post)
                else:
                    # Handle direct post structure
                    all_posts.append(item)
    
    print(f"Loaded {len(all_posts)} posts")
    return all_posts

def print_summary_stats(df: pd.DataFrame, features_df: pd.DataFrame):
    """
    Print comprehensive summary statistics
    """
    print("\n" + "="*80)
    print("PIPELINE EXECUTION SUMMARY")
    print("="*80)
    
    # Basic stats
    print(f"\nPOST PROCESSING:")
    print(f"  Total posts processed: {len(df)}")
    print(f"  Financial posts: {df['has_financial_content'].sum()}")
    print(f"  Financial ratio: {df['has_financial_content'].mean():.2%}")
    
    # Sentiment distribution
    print(f"\nSENTIMENT DISTRIBUTION:")
    sentiment_counts = df['sentiment'].value_counts()
    for sentiment, count in sentiment_counts.items():
        percentage = count / len(df) * 100
        print(f"  {sentiment.title()}: {count} ({percentage:.1f}%)")
    
    # Top companies/tickers
    print(f"\nTOP MENTIONED COMPANIES:")
    all_companies = []
    for companies in df['companies']:
        all_companies.extend(companies)
    
    if all_companies:
        company_counts = pd.Series(all_companies).value_counts().head(10)
        for company, count in company_counts.items():
            print(f"  {company}: {count} mentions")
    
    # Features summary
    if not features_df.empty:
        print(f"\nTRADING FEATURES:")
        print(f"  Feature rows generated: {len(features_df)}")
        print(f"  Unique tickers: {features_df['ticker'].nunique()}")
        print(f"  Time range: {features_df['timestamp'].min()} to {features_df['timestamp'].max()}")
        
        # Average sentiment by ticker
        print(f"\nAVERAGE SENTIMENT BY TICKER (Top 10):")
        ticker_sentiment = features_df.groupby('ticker')['sentiment_mean'].mean().sort_values(ascending=False).head(10)
        for ticker, sentiment in ticker_sentiment.items():
            print(f"  {ticker}: {sentiment:.3f}")
    
    # Quality metrics
    print(f"\nQUALITY METRICS:")
    print(f"  Average sentiment confidence: {df['sentiment_confidence'].mean():.3f}")
    print(f"  High confidence predictions: {(df['sentiment_confidence'] > 0.7).mean():.2%}")
    print(f"  Average text length: {df['text_length'].mean():.1f} characters")
    print(f"  Posts with percentages: {df['percentages'].apply(len).gt(0).mean():.2%}")

def main():
    """
    Main execution function
    """
    parser = argparse.ArgumentParser(description="Run Simple NLP Pipeline")
    parser.add_argument("--data", required=True, help="Path to scraped data file")
    parser.add_argument("--output", default="data/processed", help="Output directory")
    parser.add_argument("--time-window", default="1H", help="Time window for feature aggregation")
    parser.add_argument("--batch-size", type=int, default=100, help="Batch size for processing")
    parser.add_argument("--no-embeddings", action="store_true", help="Disable embedding generation")
    parser.add_argument("--no-model", action="store_true", help="Disable transformer model")
    parser.add_argument("--evaluate", action="store_true", help="Run evaluation metrics")
    
    args = parser.parse_args()
    
    try:
        # Load data
        posts = load_scraped_data(args.data)
        
        if not posts:
            print("No posts found in data file")
            return
        
        # Initialize pipeline
        print("\nInitializing NLP Pipeline...")
        pipeline = SimpleNLPPipeline(
            enable_embeddings=not args.no_embeddings,
            enable_model_sentiment=not args.no_model
        )
        
        # Process posts
        print("\nProcessing posts through NLP pipeline...")
        df = pipeline.process_batch(posts, batch_size=args.batch_size)
        
        if df.empty:
            print("No valid posts processed")
            return
        
        # Generate trading features
        print("\nGenerating trading features...")
        features_df = pipeline.generate_trading_features(df, time_window=args.time_window)
        
        # Save results
        print("\nSaving results...")
        saved_files = pipeline.save_results(df, features_df, args.output)
        
        # Print summary
        print_summary_stats(df, features_df)
        
        # Print saved files
        print(f"\nSAVED FILES:")
        for file_type, file_path in saved_files.items():
            print(f"  {file_type}: {file_path}")
        
        # Evaluation
        if args.evaluate:
            print("\nRunning evaluation...")
            eval_metrics = pipeline.evaluate_pipeline(posts[:100])  # Evaluate on subset
            
            print(f"\nEVALUATION METRICS:")
            print(f"  Processing success rate: {eval_metrics['processing_success_rate']:.2%}")
            print(f"  Avg processing time per post: {eval_metrics['avg_processing_time_per_post']:.3f}s")
            print(f"  Financial posts ratio: {eval_metrics['financial_posts_ratio']:.2%}")
            print(f"  Avg sentiment confidence: {eval_metrics['avg_sentiment_confidence']:.3f}")
        
        # Pipeline stats
        print(f"\nPIPELINE STATISTICS:")
        stats = pipeline.get_stats()
        for key, value in stats.items():
            if isinstance(value, float):
                print(f"  {key}: {value:.3f}")
            else:
                print(f"  {key}: {value}")
        
        print("\n" + "="*80)
        print("PIPELINE EXECUTION COMPLETED SUCCESSFULLY")
        print("="*80)
        
    except Exception as e:
        print(f"\nError: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
