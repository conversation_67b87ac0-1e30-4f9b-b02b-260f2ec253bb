{"schema_version": "1.1", "runtime": {"run_id": "real_soak_20250810_155658_9adcbc31", "started_at": "2025-08-10T15:56:58.843555+03:00", "ended_at": "2025-08-10T15:57:02.052523+03:00", "duration_seconds": 384.83811378479004, "python_version": "3.12.10", "platform": "win32", "git_commit": "a3a6344b5a5fc3145f513999c3dd2dd8a344ac8d", "config_snapshot": {"requests_per_minute": 10, "rate_limit": {"capacity": 7, "refill_rate": 0.16666666666666666}}}, "soak_metrics": {"total_requests": 2160, "total_records": 5040, "throughput_rec_per_sec": 13.096415920015861}}