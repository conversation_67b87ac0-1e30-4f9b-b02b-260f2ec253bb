# Scraper configuration for Hawamer.com

user_agents:
  - Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
  - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36

rate_limit:
  requests_per_minute: 10
  max_retries: 5
  sleep_between_requests: [5, 10]  # random range in seconds

proxy:
  enabled: false
  rotation_interval: 50  # rotate every N requests
  list: []  # list of proxies to rotate through

captcha:
  service: 2captcha
  api_key_env: CAPTCHA_API_KEY

cloudflare:
  retries: 5
  wait_time_ms: 10000
  detection_strings:
    - "Just a moment"
    - "لحظة"
    - "Checking your browser"
    - "يتم التحقق من متصفحك"


# Scrape controls and selectors
scrape:
  navigation_timeout_ms: 60000
  wait_selector_timeout_ms: 30000
  inter_post_delay_sec: [2, 4]
  max_retries: 3
  post_selectors:
    container: 'div[id^="post"], div[class*="post"]'
    content: 'div[id^="post_message_"], div[class*="postcontent"]'
    author: 'a[href*="member.php?u="], span[class*="username"]'
    timestamp: 'span[class*="date"], td.thead ~ td'
    title: 'title, h1, .breadcrumb > span:last-child'

# CAPTCHA service
captcha:
  service: 2captcha
  api_key_env: CAPTCHA_API_KEY
  timeout_seconds: 120

# Proxy rotation
proxy:
  enabled: false
  rotation_interval: 50
  list: []
