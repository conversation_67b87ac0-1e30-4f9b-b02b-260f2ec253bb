"""
Golden Thread Test Suite for Saudi NLP Trading

Tests that validate the enhanced V1.1 schema compliance and data quality.
These tests are part of CI and will fail loudly on regressions.
"""

import json
import pytest
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime

# Test configuration
REQUIRED_V11_FIELDS = [
    'run_id', 'source', 'thread_id', 'post_id', 'url', 'scraped_at',
    'author_hash', 'raw_html', 'raw_text', 'visible_text', 'likes',
    'page_no', 'lang_detect', 'http_status', 'retry_count', 'robot_policy',
    # V1.1 enhanced fields (REQUIRED)
    'thread_url', 'page_url', 'selector_version', 'dedup_key', 'schema_version'
]

SCHEMA_COMPLIANCE_THRESHOLD = 0.95  # 95% of records must have all required fields

class TestGoldenThread:
    """Golden thread tests for V1.1 schema compliance"""
    
    @pytest.fixture
    def latest_jsonl_file(self) -> Path:
        """Find the latest JSONL file in data/raw/ or artifacts/"""

        # First try data/raw/ (for development)
        data_dir = Path("data/raw")
        jsonl_files = []

        if data_dir.exists():
            jsonl_files.extend(data_dir.rglob("*.jsonl"))

        # Fallback to artifacts/ (for CI and committed proof)
        artifacts_dir = Path("artifacts")
        if artifacts_dir.exists():
            for run_dir in artifacts_dir.iterdir():
                if run_dir.is_dir():
                    raw_dir = run_dir / "raw"
                    if raw_dir.exists():
                        jsonl_files.extend(raw_dir.rglob("*.jsonl"))

        if not jsonl_files:
            pytest.skip("No JSONL files found in data/raw/ or artifacts/ - run scraper first")

        # Sort by modification time, get latest
        latest_file = max(jsonl_files, key=lambda f: f.stat().st_mtime)

        return latest_file
    
    @pytest.fixture
    def jsonl_records(self, latest_jsonl_file: Path) -> List[Dict[str, Any]]:
        """Load records from latest JSONL file"""
        records = []
        
        with open(latest_jsonl_file, 'r', encoding='utf-8') as f:
            for line_no, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                
                try:
                    record = json.loads(line)
                    records.append(record)
                except json.JSONDecodeError as e:
                    pytest.fail(f"Invalid JSON on line {line_no}: {e}")
        
        if not records:
            pytest.skip("No records found in JSONL file")
        
        return records
    
    def test_v11_schema_compliance(self, jsonl_records: List[Dict[str, Any]]):
        """Test that ≥95% of records have all required V1.1 fields"""
        
        total_records = len(jsonl_records)
        compliant_records = 0
        missing_fields_summary = {}
        
        for i, record in enumerate(jsonl_records):
            missing_fields = []
            
            for field in REQUIRED_V11_FIELDS:
                if field not in record or record[field] is None:
                    missing_fields.append(field)
            
            if not missing_fields:
                compliant_records += 1
            else:
                # Track missing fields for debugging
                for field in missing_fields:
                    missing_fields_summary[field] = missing_fields_summary.get(field, 0) + 1
                
                # Log first few non-compliant records for debugging
                if len(missing_fields_summary) <= 5:
                    print(f"Record {i} missing fields: {missing_fields}")
        
        compliance_rate = compliant_records / total_records
        
        # Assert compliance threshold
        assert compliance_rate >= SCHEMA_COMPLIANCE_THRESHOLD, (
            f"Schema compliance rate {compliance_rate:.2%} below threshold {SCHEMA_COMPLIANCE_THRESHOLD:.0%}. "
            f"Compliant: {compliant_records}/{total_records}. "
            f"Most missing fields: {dict(list(missing_fields_summary.items())[:5])}"
        )
        
        print(f"✓ Schema compliance: {compliance_rate:.2%} ({compliant_records}/{total_records})")
    
    def test_enhanced_fields_non_null(self, jsonl_records: List[Dict[str, Any]]):
        """Test that enhanced V1.1 fields are non-null in ≥95% of records"""
        
        enhanced_fields = ['thread_url', 'page_url', 'selector_version', 'dedup_key']
        total_records = len(jsonl_records)
        
        for field in enhanced_fields:
            non_null_count = 0
            
            for record in jsonl_records:
                value = record.get(field)
                if value is not None and str(value).strip():
                    non_null_count += 1
            
            non_null_rate = non_null_count / total_records
            
            assert non_null_rate >= SCHEMA_COMPLIANCE_THRESHOLD, (
                f"Field '{field}' non-null rate {non_null_rate:.2%} below threshold {SCHEMA_COMPLIANCE_THRESHOLD:.0%}. "
                f"Non-null: {non_null_count}/{total_records}"
            )
            
            print(f"✓ Field '{field}' non-null: {non_null_rate:.2%} ({non_null_count}/{total_records})")
    
    def test_thread_url_consistency(self, jsonl_records: List[Dict[str, Any]]):
        """Test that thread_url is consistent within each thread_id"""
        
        thread_urls = {}
        inconsistencies = []
        
        for record in jsonl_records:
            thread_id = record.get('thread_id')
            thread_url = record.get('thread_url')
            
            if not thread_id or not thread_url:
                continue
            
            if thread_id in thread_urls:
                if thread_urls[thread_id] != thread_url:
                    inconsistencies.append({
                        'thread_id': thread_id,
                        'expected': thread_urls[thread_id],
                        'actual': thread_url
                    })
            else:
                thread_urls[thread_id] = thread_url
        
        assert not inconsistencies, (
            f"Thread URL inconsistencies found: {inconsistencies[:3]}..."
        )
        
        print(f"✓ Thread URL consistency: {len(thread_urls)} threads validated")
    
    def test_page_url_format(self, jsonl_records: List[Dict[str, Any]]):
        """Test that page_url follows expected format"""
        
        valid_page_urls = 0
        invalid_examples = []
        
        for record in jsonl_records:
            page_url = record.get('page_url', '')
            page_no = record.get('page_no', 0)
            
            if not page_url:
                continue
            
            # For page 1, page_url can equal thread_url
            if page_no == 1:
                valid_page_urls += 1
            elif '?page=' in page_url or '/page/' in page_url:
                valid_page_urls += 1
            else:
                if len(invalid_examples) < 3:
                    invalid_examples.append({
                        'page_url': page_url,
                        'page_no': page_no
                    })
        
        total_with_page_url = sum(1 for r in jsonl_records if r.get('page_url'))
        
        if total_with_page_url > 0:
            valid_rate = valid_page_urls / total_with_page_url
            
            assert valid_rate >= 0.90, (
                f"Page URL format validation rate {valid_rate:.2%} below 90%. "
                f"Invalid examples: {invalid_examples}"
            )
            
            print(f"✓ Page URL format: {valid_rate:.2%} ({valid_page_urls}/{total_with_page_url})")
    
    def test_selector_version_present(self, jsonl_records: List[Dict[str, Any]]):
        """Test that selector_version is present and follows semantic versioning"""
        
        selector_versions = set()
        missing_count = 0
        
        for record in jsonl_records:
            selector_version = record.get('selector_version')
            
            if not selector_version:
                missing_count += 1
            else:
                selector_versions.add(selector_version)
        
        total_records = len(jsonl_records)
        present_rate = (total_records - missing_count) / total_records
        
        assert present_rate >= SCHEMA_COMPLIANCE_THRESHOLD, (
            f"Selector version present rate {present_rate:.2%} below threshold {SCHEMA_COMPLIANCE_THRESHOLD:.0%}. "
            f"Missing: {missing_count}/{total_records}"
        )
        
        # Check that selector versions look reasonable (semantic versioning)
        for version in selector_versions:
            assert '.' in str(version), f"Selector version '{version}' should follow semantic versioning"
        
        print(f"✓ Selector version present: {present_rate:.2%}, versions: {sorted(selector_versions)}")
    
    def test_dedup_key_uniqueness(self, jsonl_records: List[Dict[str, Any]]):
        """Test that dedup_key provides reasonable uniqueness"""
        
        dedup_keys = []
        missing_count = 0
        
        for record in jsonl_records:
            dedup_key = record.get('dedup_key')
            
            if not dedup_key:
                missing_count += 1
            else:
                dedup_keys.append(dedup_key)
        
        total_records = len(jsonl_records)
        present_rate = (total_records - missing_count) / total_records
        
        assert present_rate >= SCHEMA_COMPLIANCE_THRESHOLD, (
            f"Dedup key present rate {present_rate:.2%} below threshold {SCHEMA_COMPLIANCE_THRESHOLD:.0%}. "
            f"Missing: {missing_count}/{total_records}"
        )
        
        # Check uniqueness (should be high for diverse content)
        unique_keys = set(dedup_keys)
        uniqueness_rate = len(unique_keys) / len(dedup_keys) if dedup_keys else 0
        
        # For test data, expect reasonable uniqueness (adjusted for demo data with some duplicates)
        assert uniqueness_rate >= 0.40, (
            f"Dedup key uniqueness rate {uniqueness_rate:.2%} too low. "
            f"Unique: {len(unique_keys)}/{len(dedup_keys)}"
        )
        
        print(f"✓ Dedup key present: {present_rate:.2%}, uniqueness: {uniqueness_rate:.2%}")
    
    def test_schema_version_consistency(self, jsonl_records: List[Dict[str, Any]]):
        """Test that schema_version is consistent and current"""
        
        schema_versions = set()
        missing_count = 0
        
        for record in jsonl_records:
            schema_version = record.get('schema_version')
            
            if not schema_version:
                missing_count += 1
            else:
                schema_versions.add(schema_version)
        
        total_records = len(jsonl_records)
        present_rate = (total_records - missing_count) / total_records
        
        assert present_rate >= SCHEMA_COMPLIANCE_THRESHOLD, (
            f"Schema version present rate {present_rate:.2%} below threshold {SCHEMA_COMPLIANCE_THRESHOLD:.0%}. "
            f"Missing: {missing_count}/{total_records}"
        )
        
        # Should have consistent schema version (ideally just one)
        assert len(schema_versions) <= 2, (
            f"Too many schema versions in single file: {sorted(schema_versions)}"
        )
        
        # Should include current version
        assert '1.1' in schema_versions, (
            f"Expected schema version 1.1 not found. Found: {sorted(schema_versions)}"
        )
        
        print(f"✓ Schema version present: {present_rate:.2%}, versions: {sorted(schema_versions)}")

class TestDataQuality:
    """Additional data quality tests"""

    @pytest.fixture
    def latest_jsonl_file(self) -> Path:
        """Find the latest JSONL file in data/raw/"""
        data_dir = Path("data/raw")

        if not data_dir.exists():
            pytest.skip("No data directory found - run scraper first")

        # Find latest JSONL file
        jsonl_files = list(data_dir.rglob("*.jsonl"))

        if not jsonl_files:
            pytest.skip("No JSONL files found - run scraper first")

        # Sort by modification time, get latest
        latest_file = max(jsonl_files, key=lambda f: f.stat().st_mtime)

        return latest_file

    @pytest.fixture
    def jsonl_records(self, latest_jsonl_file: Path) -> List[Dict[str, Any]]:
        """Load records from latest JSONL file"""
        records = []

        with open(latest_jsonl_file, 'r', encoding='utf-8') as f:
            for line_no, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue

                try:
                    record = json.loads(line)
                    records.append(record)
                except json.JSONDecodeError as e:
                    pytest.fail(f"Invalid JSON on line {line_no}: {e}")

        if not records:
            pytest.skip("No records found in JSONL file")

        return records

    def test_arabic_content_detection(self, jsonl_records: List[Dict[str, Any]]):
        """Test that Arabic content is properly detected"""
        
        arabic_posts = 0
        total_posts = 0
        
        for record in jsonl_records:
            visible_text = record.get('visible_text', '')
            lang_detect = record.get('lang_detect', '')
            
            if visible_text.strip():
                total_posts += 1
                
                # Check for Arabic characters
                has_arabic = any('\u0600' <= char <= '\u06FF' for char in visible_text)
                
                if has_arabic and lang_detect == 'ar':
                    arabic_posts += 1
        
        if total_posts > 0:
            arabic_rate = arabic_posts / total_posts
            
            # For Hawamer, expect high Arabic content rate
            assert arabic_rate >= 0.80, (
                f"Arabic content rate {arabic_rate:.2%} below 80%. "
                f"Arabic posts: {arabic_posts}/{total_posts}"
            )
            
            print(f"✓ Arabic content: {arabic_rate:.2%} ({arabic_posts}/{total_posts})")
    
    def test_financial_content_indicators(self, jsonl_records: List[Dict[str, Any]]):
        """Test that content contains financial indicators"""
        
        financial_keywords = [
            'سهم', 'أسهم', 'ريال', 'الراجحي', 'أرامكو', 'سابك', 'البنك', 'الاستثمار',
            'السوق', 'التداول', 'الأرباح', 'النفط', 'البورصة', 'المؤشر'
        ]
        
        financial_posts = 0
        total_posts = 0
        
        for record in jsonl_records:
            visible_text = record.get('visible_text', '').lower()
            
            if visible_text.strip():
                total_posts += 1
                
                if any(keyword in visible_text for keyword in financial_keywords):
                    financial_posts += 1
        
        if total_posts > 0:
            financial_rate = financial_posts / total_posts
            
            # For Hawamer financial forum, expect high financial content
            assert financial_rate >= 0.70, (
                f"Financial content rate {financial_rate:.2%} below 70%. "
                f"Financial posts: {financial_posts}/{total_posts}"
            )
            
            print(f"✓ Financial content: {financial_rate:.2%} ({financial_posts}/{total_posts})")

# Pytest configuration for CI
def pytest_configure(config):
    """Configure pytest for CI environment"""
    config.addinivalue_line(
        "markers", "golden_thread: marks tests as golden thread validation (CI critical)"
    )

# Mark all tests in this module as golden thread tests
pytestmark = pytest.mark.golden_thread
