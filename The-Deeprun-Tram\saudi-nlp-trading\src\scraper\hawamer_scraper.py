"""
Hawamer Forum Scraper for Saudi NLP Trading

Specialized scraper for Hawamer financial forum with proper
thread and post extraction, pagination handling, and data contracts.
"""

import re
import hashlib
from typing import Dict, List, Any, Optional, Tuple
from urllib.parse import urljoin, urlparse, parse_qs
from datetime import datetime
from bs4 import BeautifulSoup, Tag
import langdetect

from .base_scraper import BaseScraper, ParseError
from ..config.settings import RIYADH_TZ
from ..utils.logging import get_logger

class HawamerScraper(BaseScraper):
    """
    Hawamer forum scraper with thread and post extraction
    """
    
    def __init__(self):
        super().__init__("hawamer")
        self.base_url = "https://hawamer.com"
        
        # Hawamer-specific patterns
        self.thread_url_pattern = re.compile(r'/vb/hawamer(\d+)')
        self.post_id_pattern = re.compile(r'post(\d+)')
        self.user_profile_pattern = re.compile(r'/vb/member\.php\?u=(\d+)')
        
        # Arabic text patterns
        self.arabic_pattern = re.compile(r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+')
        
        self.logger.info("Initialized Hawamer scraper")
    
    def extract_content(self, html: str, url: str) -> Dict[str, Any]:
        """
        Extract thread and posts from Hawamer page
        
        Returns:
            Dictionary with thread metadata and posts
        """
        
        try:
            soup = BeautifulSoup(html, 'html.parser')
            
            # Extract thread metadata
            thread_metadata = self._extract_thread_metadata(soup, url)
            
            # Extract posts
            posts = self._extract_posts(soup, url)
            
            # Extract pagination info
            pagination = self._extract_pagination(soup, url)
            
            return {
                'thread_metadata': thread_metadata,
                'posts': posts,
                'pagination': pagination,
                'total_posts': len(posts),
                'page_url': url
            }
            
        except Exception as e:
            self.logger.error(
                "Failed to extract content from Hawamer page",
                url=url,
                exception_class=e.__class__.__name__,
                stage="extract",
                exc_info=True
            )
            raise ParseError(f"Failed to parse Hawamer page: {e}") from e
    
    def _extract_thread_metadata(self, soup: BeautifulSoup, url: str) -> Dict[str, Any]:
        """Extract thread-level metadata"""
        
        metadata = {
            'thread_id': self._extract_thread_id(url),
            'title': '',
            'forum_section': '',
            'thread_url': url,
            'total_pages': 1,
            'current_page': 1
        }
        
        try:
            # Extract thread title
            title_elem = soup.find('title')
            if title_elem:
                metadata['title'] = title_elem.get_text().strip()
            
            # Extract forum breadcrumb
            breadcrumb = soup.find('div', class_='navbar')
            if breadcrumb:
                links = breadcrumb.find_all('a')
                if len(links) > 1:
                    metadata['forum_section'] = links[-2].get_text().strip()
            
            # Extract pagination info
            page_nav = soup.find('div', class_='pagenav')
            if page_nav:
                # Current page
                current_page_elem = page_nav.find('span', class_='curpage')
                if current_page_elem:
                    try:
                        metadata['current_page'] = int(current_page_elem.get_text().strip())
                    except ValueError:
                        pass
                
                # Total pages
                page_links = page_nav.find_all('a', href=True)
                max_page = 1
                for link in page_links:
                    href = link.get('href', '')
                    page_match = re.search(r'page=(\d+)', href)
                    if page_match:
                        page_num = int(page_match.group(1))
                        max_page = max(max_page, page_num)
                
                metadata['total_pages'] = max_page
            
        except Exception as e:
            self.logger.warning(
                "Failed to extract some thread metadata",
                url=url,
                exception_class=e.__class__.__name__
            )
        
        return metadata
    
    def _extract_posts(self, soup: BeautifulSoup, url: str) -> List[Dict[str, Any]]:
        """Extract individual posts from page"""
        
        posts = []
        
        # Find all post containers
        post_containers = soup.find_all('div', id=self.post_id_pattern)
        
        for container in post_containers:
            try:
                post_data = self._extract_single_post(container, url)
                if post_data:
                    posts.append(post_data)
            except Exception as e:
                self.logger.warning(
                    "Failed to extract individual post",
                    url=url,
                    exception_class=e.__class__.__name__
                )
        
        return posts
    
    def _extract_single_post(self, container: Tag, page_url: str) -> Optional[Dict[str, Any]]:
        """Extract data from a single post container"""
        
        try:
            # Extract post ID
            post_id = self._extract_post_id(container)
            if not post_id:
                return None
            
            # Extract author information
            author_info = self._extract_author_info(container)
            
            # Extract post content
            content_info = self._extract_post_content(container)
            
            # Extract metadata
            metadata = self._extract_post_metadata(container)
            
            # Detect language
            lang_detect = self._detect_language(content_info['visible_text'])
            
            # Create post data according to data contract
            post_data = {
                'run_id': self.config.run_id,
                'source': 'hawamer',
                'thread_id': self._extract_thread_id(page_url),
                'post_id': post_id,
                'url': page_url,
                'scraped_at': datetime.now(RIYADH_TZ).isoformat(),
                'author_hash': self._hash_author(author_info['username']),
                'raw_html': str(container),
                'raw_text': content_info['raw_text'],
                'visible_text': content_info['visible_text'],
                'likes': metadata.get('likes', 0),
                'reply_to_id': metadata.get('reply_to_id'),
                'page_no': metadata.get('page_no', 1),
                'lang_detect': lang_detect,
                'http_status': 200,
                'retry_count': 0,
                'robot_policy': 'allowed'
            }
            
            return post_data
            
        except Exception as e:
            self.logger.warning(
                "Failed to extract post data",
                exception_class=e.__class__.__name__
            )
            return None
    
    def _extract_post_id(self, container: Tag) -> Optional[str]:
        """Extract post ID from container"""
        post_id_attr = container.get('id', '')
        match = self.post_id_pattern.match(post_id_attr)
        return match.group(1) if match else None
    
    def _extract_thread_id(self, url: str) -> Optional[str]:
        """Extract thread ID from URL"""
        match = self.thread_url_pattern.search(url)
        return match.group(1) if match else None
    
    def _extract_author_info(self, container: Tag) -> Dict[str, Any]:
        """Extract author information"""
        
        author_info = {
            'username': 'unknown',
            'user_id': None,
            'profile_url': None
        }
        
        try:
            # Find username link
            username_link = container.find('a', class_='username')
            if username_link:
                author_info['username'] = username_link.get_text().strip()
                
                # Extract user ID from profile URL
                profile_url = username_link.get('href', '')
                if profile_url:
                    author_info['profile_url'] = urljoin(self.base_url, profile_url)
                    
                    user_match = self.user_profile_pattern.search(profile_url)
                    if user_match:
                        author_info['user_id'] = user_match.group(1)
            
        except Exception as e:
            self.logger.debug(
                "Failed to extract author info",
                exception_class=e.__class__.__name__
            )
        
        return author_info
    
    def _extract_post_content(self, container: Tag) -> Dict[str, str]:
        """Extract post content (raw and visible text)"""
        
        content_info = {
            'raw_text': '',
            'visible_text': ''
        }
        
        try:
            # Find post content div
            content_div = container.find('div', class_='postcontent')
            if not content_div:
                content_div = container.find('div', id=re.compile(r'post_message_\d+'))
            
            if content_div:
                # Raw text (with HTML)
                content_info['raw_text'] = str(content_div)
                
                # Visible text (cleaned)
                content_info['visible_text'] = self._clean_post_text(content_div)
        
        except Exception as e:
            self.logger.debug(
                "Failed to extract post content",
                exception_class=e.__class__.__name__
            )
        
        return content_info
    
    def _clean_post_text(self, content_div: Tag) -> str:
        """Clean post text for analysis"""
        
        # Remove quote blocks
        for quote in content_div.find_all('div', class_='quote'):
            quote.decompose()
        
        # Remove signatures
        for sig in content_div.find_all('div', class_='signature'):
            sig.decompose()
        
        # Get clean text
        text = content_div.get_text(separator=' ', strip=True)
        
        # Clean up whitespace
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip()
    
    def _extract_post_metadata(self, container: Tag) -> Dict[str, Any]:
        """Extract post metadata (likes, timestamps, etc.)"""
        
        metadata = {
            'likes': 0,
            'reply_to_id': None,
            'page_no': 1,
            'timestamp': None
        }
        
        try:
            # Extract likes/thanks
            thanks_elem = container.find('span', class_='thanks')
            if thanks_elem:
                thanks_text = thanks_elem.get_text()
                likes_match = re.search(r'(\d+)', thanks_text)
                if likes_match:
                    metadata['likes'] = int(likes_match.group(1))
            
            # Extract timestamp
            date_elem = container.find('span', class_='date')
            if date_elem:
                metadata['timestamp'] = date_elem.get_text().strip()
        
        except Exception as e:
            self.logger.debug(
                "Failed to extract post metadata",
                exception_class=e.__class__.__name__
            )
        
        return metadata
    
    def _hash_author(self, username: str) -> str:
        """Hash username for privacy (PDPL compliance)"""
        if not username or username == 'unknown':
            return 'anonymous'
        
        # Normalize username
        normalized = username.lower().strip()
        
        # SHA256 hash
        return hashlib.sha256(normalized.encode('utf-8')).hexdigest()[:16]
    
    def _detect_language(self, text: str) -> str:
        """Detect language of post content"""
        
        if not text or len(text.strip()) < 10:
            return 'unknown'
        
        try:
            # Check if text contains Arabic
            if self.arabic_pattern.search(text):
                return 'ar'
            
            # Use langdetect for other languages
            if self.config.mode.language_detection_enabled:
                detected = langdetect.detect(text)
                return detected
            
            return 'unknown'
            
        except Exception:
            return 'unknown'
    
    def _extract_pagination(self, soup: BeautifulSoup, url: str) -> Dict[str, Any]:
        """Extract pagination information"""
        
        pagination = {
            'current_page': 1,
            'total_pages': 1,
            'next_page_url': None,
            'prev_page_url': None,
            'page_urls': []
        }
        
        try:
            page_nav = soup.find('div', class_='pagenav')
            if page_nav:
                # Current page
                current_page_elem = page_nav.find('span', class_='curpage')
                if current_page_elem:
                    try:
                        pagination['current_page'] = int(current_page_elem.get_text().strip())
                    except ValueError:
                        pass
                
                # All page links
                page_links = page_nav.find_all('a', href=True)
                page_urls = []
                max_page = 1
                
                for link in page_links:
                    href = link.get('href', '')
                    full_url = urljoin(self.base_url, href)
                    
                    # Extract page number
                    page_match = re.search(r'page=(\d+)', href)
                    if page_match:
                        page_num = int(page_match.group(1))
                        max_page = max(max_page, page_num)
                        page_urls.append((page_num, full_url))
                
                pagination['total_pages'] = max_page
                pagination['page_urls'] = sorted(page_urls)
                
                # Next/prev page URLs
                current_page = pagination['current_page']
                for page_num, page_url in page_urls:
                    if page_num == current_page + 1:
                        pagination['next_page_url'] = page_url
                    elif page_num == current_page - 1:
                        pagination['prev_page_url'] = page_url
        
        except Exception as e:
            self.logger.debug(
                "Failed to extract pagination",
                url=url,
                exception_class=e.__class__.__name__
            )
        
        return pagination
    
    def get_pagination_urls(self, html: str, base_url: str) -> List[str]:
        """Extract pagination URLs for thread traversal"""
        
        try:
            soup = BeautifulSoup(html, 'html.parser')
            pagination = self._extract_pagination(soup, base_url)
            
            # Return all page URLs
            return [url for page_num, url in pagination['page_urls']]
            
        except Exception as e:
            self.logger.warning(
                "Failed to extract pagination URLs",
                base_url=base_url,
                exception_class=e.__class__.__name__
            )
            return []
    
    def is_hawamer_url(self, url: str) -> bool:
        """Check if URL is a valid Hawamer thread URL"""
        parsed = urlparse(url)
        return (parsed.netloc == 'hawamer.com' and 
                self.thread_url_pattern.search(url) is not None)
