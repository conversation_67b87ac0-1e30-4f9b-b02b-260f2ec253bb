"""
Test Suite for Scrape-Only Foundation

Tests all core components: configuration, logging, robots.txt,
retry logic, manifest creation, and storage backends.
"""

import pytest
import tempfile
import json
import time
from pathlib import Path
from unittest.mock import Mock, patch
import uuid

# Import components to test
import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from config.settings import Config<PERSON>oader, get_config, set_run_id
from utils.logging import get_logger, StructuredLogger, PerformanceTracker
from utils.robots import <PERSON>s<PERSON><PERSON><PERSON>, CrawlDelayManager
from utils.retry import RetryManager, RetryableError, NonRetryableError
from utils.manifest import ManifestManager, FileManifest
from storage.storage_manager import LocalStorageBackend, StorageManager
from core.mode_manager import get_mode_manager, require_nlp_enabled

class TestConfiguration:
    """Test configuration management"""
    
    def test_config_loading_from_env(self, monkeypatch):
        """Test loading configuration from environment variables"""
        
        # Set environment variables
        monkeypatch.setenv("SCRAPER_MAX_RPM", "60")
        monkeypatch.setenv("STORAGE_BACKEND", "local")
        monkeypatch.setenv("MODE_SCRAPE_ONLY", "true")
        monkeypatch.setenv("NLP_ENABLE", "false")
        
        # Load configuration
        loader = ConfigLoader()
        config = loader.load()
        
        assert config.scraper.max_rpm == 60
        assert config.storage.backend == "local"
        assert config.mode.scrape_only == True
        assert config.mode.nlp_enable == False
    
    def test_config_validation(self, monkeypatch):
        """Test configuration validation"""
        
        # Test invalid RPM
        monkeypatch.setenv("SCRAPER_MAX_RPM", "-1")
        
        loader = ConfigLoader()
        with pytest.raises(ValueError, match="SCRAPER_MAX_RPM must be positive"):
            loader.load()
    
    def test_gcs_config_validation(self, monkeypatch):
        """Test GCS configuration validation"""
        
        monkeypatch.setenv("STORAGE_BACKEND", "gcs")
        # Missing GCS_BUCKET
        
        loader = ConfigLoader()
        with pytest.raises(ValueError, match="GCS_BUCKET required"):
            loader.load()

class TestStructuredLogging:
    """Test structured logging system"""
    
    def test_logger_creation(self):
        """Test logger creation and basic functionality"""
        
        logger = get_logger("test_logger")
        assert isinstance(logger, StructuredLogger)
        
        # Test structured logging
        logger.info("Test message", test_field="test_value", stage="test")
    
    def test_performance_tracker(self):
        """Test performance tracking"""
        
        logger = get_logger("test_performance")
        tracker = PerformanceTracker(logger)
        
        # Test operation tracking
        with tracker.track_operation("test_operation", url="http://example.com"):
            time.sleep(0.1)  # Simulate work
        
        # Check metrics
        metrics = tracker.get_metrics_summary()
        assert "test_operation" in metrics
        assert metrics["test_operation"]["total_calls"] == 1
        assert metrics["test_operation"]["successful_calls"] == 1

class TestRobotsHandling:
    """Test robots.txt handling"""
    
    def test_robots_checker_creation(self):
        """Test robots checker initialization"""
        
        checker = RobotsChecker()
        assert checker is not None
    
    @patch('urllib.robotparser.RobotFileParser.read')
    def test_robots_can_fetch(self, mock_read):
        """Test robots.txt can_fetch functionality"""
        
        # Mock robots.txt parser
        mock_rp = Mock()
        mock_rp.can_fetch.return_value = True
        
        with patch('urllib.robotparser.RobotFileParser') as mock_parser_class:
            mock_parser_class.return_value = mock_rp
            
            checker = RobotsChecker()
            can_fetch, policy, crawl_delay = checker.can_fetch("http://example.com/test")
            
            assert can_fetch == True
            assert policy in ["allowed", "robots_disabled"]
    
    def test_crawl_delay_manager(self):
        """Test crawl delay management"""
        
        checker = RobotsChecker()
        manager = CrawlDelayManager(checker)
        
        # Test wait calculation
        wait_time = manager.wait_if_needed("http://example.com/test1")
        assert wait_time >= 0

class TestRetryLogic:
    """Test retry and backoff system"""
    
    def test_retry_manager_creation(self):
        """Test retry manager initialization"""
        
        manager = RetryManager()
        assert manager is not None
    
    def test_should_retry_logic(self):
        """Test retry decision logic"""
        
        manager = RetryManager()
        
        # Test retryable errors
        assert manager.should_retry(RetryableError("Test"), 0) == True
        assert manager.should_retry(ConnectionError("Test"), 0) == True
        
        # Test non-retryable errors
        assert manager.should_retry(NonRetryableError("Test"), 0) == False
        
        # Test attempt limit
        assert manager.should_retry(RetryableError("Test"), 5) == False
    
    def test_retry_execution(self):
        """Test retry execution with mock function"""
        
        manager = RetryManager()
        
        # Test successful function
        def success_func():
            return "success"
        
        result = manager.execute_with_retry(success_func)
        assert result == "success"
        
        # Test function that fails then succeeds
        call_count = 0
        def fail_then_succeed():
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                raise RetryableError("First attempt fails")
            return "success"
        
        result = manager.execute_with_retry(fail_then_succeed)
        assert result == "success"
        assert call_count == 2

class TestManifestSystem:
    """Test manifest and checksum system"""
    
    def test_file_manifest(self):
        """Test file manifest creation"""
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
            f.write("test content")
            temp_path = Path(f.name)
        
        try:
            manifest = FileManifest(temp_path)
            metadata = manifest.get_metadata()
            
            assert 'checksum_sha256' in metadata
            assert 'size_bytes' in metadata
            assert metadata['size_bytes'] > 0
            
        finally:
            temp_path.unlink()
    
    def test_manifest_manager(self):
        """Test manifest manager functionality"""
        
        run_id = str(uuid.uuid4())
        manager = ManifestManager(run_id)
        
        # Create test file
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
            f.write("test content for manifest")
            temp_path = Path(f.name)
        
        try:
            # Add file to manifest
            manager.add_file(temp_path, "test_data")
            
            # Check manifest content
            assert manager.manifest['stats']['total_files'] == 1
            assert temp_path.name in manager.manifest['files']
            
            # Save manifest
            manifest_file = manager.save_manifest()
            assert manifest_file.exists()
            
            # Validate manifest
            is_valid = manager.validate_manifest()
            assert is_valid == True
            
        finally:
            temp_path.unlink()
            if manager.manifest_file.exists():
                manager.manifest_file.unlink()

class TestStorageSystem:
    """Test storage backends and manager"""
    
    def test_local_storage_backend(self):
        """Test local storage backend"""
        
        with tempfile.TemporaryDirectory() as temp_dir:
            backend = LocalStorageBackend(temp_dir)
            
            # Test data
            test_data = [
                {"id": 1, "content": "test content 1"},
                {"id": 2, "content": "test content 2"}
            ]
            
            # Write JSONL
            file_path = "test/data.jsonl"
            bytes_written = backend.write_jsonl(test_data, file_path)
            assert bytes_written > 0
            
            # Check file exists
            assert backend.exists(file_path) == True
            
            # Read JSONL
            read_data = list(backend.read_jsonl(file_path))
            assert len(read_data) == 2
            assert read_data[0]["id"] == 1
            assert read_data[1]["id"] == 2
            
            # Test file listing
            files = backend.list_files("test/")
            assert file_path in files
    
    def test_storage_manager(self):
        """Test storage manager with partitioning"""
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Set up configuration
            run_id = str(uuid.uuid4())
            set_run_id(run_id)
            
            # Mock config to use temp directory
            with patch('storage.storage_manager.get_config') as mock_config:
                mock_config.return_value.storage.backend = 'local'
                mock_config.return_value.storage.local_base_path = temp_dir
                
                manager = StorageManager(run_id)
                
                # Test raw document storage
                raw_docs = [
                    {
                        "run_id": run_id,
                        "source": "hawamer",
                        "post_id": "123",
                        "content": "Test post content"
                    }
                ]
                
                file_path = manager.store_raw_documents(raw_docs, "hawamer")
                assert file_path.startswith("raw/source=hawamer/")
                
                # Test reading back
                read_docs = list(manager.read_raw_documents("hawamer", "2024-08-10"))
                # Note: might be empty if date doesn't match current date

class TestModeManager:
    """Test mode management and feature flags"""
    
    def test_mode_manager_creation(self):
        """Test mode manager initialization"""
        
        manager = get_mode_manager()
        assert manager is not None
    
    def test_scrape_only_mode(self, monkeypatch):
        """Test scrape-only mode functionality"""
        
        monkeypatch.setenv("MODE_SCRAPE_ONLY", "true")
        monkeypatch.setenv("NLP_ENABLE", "false")
        
        # Reload config
        from config.settings import _config
        _config = None
        
        manager = get_mode_manager()
        assert manager.is_scrape_only() == True
        assert manager.is_nlp_enabled() == False
    
    def test_nlp_decorator(self, monkeypatch):
        """Test NLP requirement decorator"""
        
        monkeypatch.setenv("MODE_SCRAPE_ONLY", "true")
        monkeypatch.setenv("NLP_ENABLE", "false")
        
        # Reload config
        from config.settings import _config
        _config = None
        
        @require_nlp_enabled
        def nlp_function():
            return "nlp_result"
        
        # Should return None in scrape-only mode
        result = nlp_function()
        assert result is None

class TestIntegration:
    """Integration tests for complete system"""
    
    def test_smoke_scrape_workflow(self):
        """Test complete scraping workflow (smoke test)"""
        
        run_id = str(uuid.uuid4())
        set_run_id(run_id)
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Mock configuration
            with patch('storage.storage_manager.get_config') as mock_config:
                mock_config.return_value.storage.backend = 'local'
                mock_config.return_value.storage.local_base_path = temp_dir
                mock_config.return_value.run_id = run_id
                
                # Initialize components
                logger = get_logger("integration_test")
                storage_manager = StorageManager(run_id)
                
                # Simulate scraped data
                scraped_data = [
                    {
                        "run_id": run_id,
                        "source": "hawamer",
                        "thread_id": "12345",
                        "post_id": "67890",
                        "url": "https://hawamer.com/vb/hawamer12345",
                        "scraped_at": "2024-08-10T10:00:00+03:00",
                        "author_hash": "abc123",
                        "raw_html": "<div>Test post</div>",
                        "raw_text": "Test post",
                        "visible_text": "Test post",
                        "likes": 5,
                        "reply_to_id": None,
                        "page_no": 1,
                        "lang_detect": "ar",
                        "http_status": 200,
                        "retry_count": 0,
                        "robot_policy": "allowed"
                    }
                ]
                
                # Store data
                file_path = storage_manager.store_raw_documents(scraped_data, "hawamer")
                assert file_path != ""
                
                # Finalize storage
                manifest_file = storage_manager.finalize_storage()
                assert manifest_file.exists()
                
                # Log success
                logger.info(
                    "Smoke test completed successfully",
                    run_id=run_id,
                    file_path=file_path,
                    manifest_file=str(manifest_file)
                )

def test_idempotence():
    """Test idempotent runs (same input produces same output)"""
    
    run_id = str(uuid.uuid4())
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # First run
        with patch('storage.storage_manager.get_config') as mock_config:
            mock_config.return_value.storage.backend = 'local'
            mock_config.return_value.storage.local_base_path = temp_dir
            mock_config.return_value.run_id = run_id
            
            manager1 = StorageManager(run_id)
            test_data = [{"id": 1, "content": "test"}]
            
            file_path1 = manager1.store_raw_documents(test_data, "test_source")
            manifest1 = manager1.finalize_storage()
            
            # Read manifest checksum
            with open(manifest1, 'r') as f:
                manifest_data1 = json.load(f)
            
            checksum1 = manifest_data1['manifest_checksum']
        
        # Second run with same data
        run_id2 = str(uuid.uuid4())  # Different run ID but same data
        
        with patch('storage.storage_manager.get_config') as mock_config:
            mock_config.return_value.storage.backend = 'local'
            mock_config.return_value.storage.local_base_path = temp_dir
            mock_config.return_value.run_id = run_id2
            
            manager2 = StorageManager(run_id2)
            
            file_path2 = manager2.store_raw_documents(test_data, "test_source")
            manifest2 = manager2.finalize_storage()
            
            # The file paths should be different (different run IDs)
            # But the content checksums should be the same for identical data
            assert file_path1 != file_path2  # Different run IDs
            
            # Content should be identical
            backend = LocalStorageBackend(temp_dir)
            data1 = list(backend.read_jsonl(file_path1))
            data2 = list(backend.read_jsonl(file_path2))
            
            # Remove run_id for comparison (since they're different)
            for item in data1:
                item.pop('run_id', None)
            for item in data2:
                item.pop('run_id', None)
            
            assert data1 == data2

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
