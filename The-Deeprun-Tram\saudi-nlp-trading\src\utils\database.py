"""Database helpers for connecting to PostgreSQL and executing queries.

This module provides functions to create SQLAlchemy engines and sessions.
"""

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from ..config.settings import DATABASE_URL

def get_engine():
    """Create a SQLAlchemy engine using the DATABASE_URL."""
    return create_engine(DATABASE_URL)

def get_session():
    """Create a new SQLAlchemy session."""
    engine = get_engine()
    Session = sessionmaker(bind=engine)
    return Session()
