#!/usr/bin/env python3
"""
Simple Demonstration of Arabic NLP Components

This script demonstrates the core Arabic NLP functionality with minimal dependencies,
focusing on preprocessing and basic analysis without requiring heavy ML libraries.
"""

import sys
from pathlib import Path
import json
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def print_section_header(title: str):
    """Print a formatted section header"""
    print("\n" + "="*80)
    print(f" {title}")
    print("="*80)

def print_subsection(title: str):
    """Print a formatted subsection header"""
    print(f"\n--- {title} ---")

def demo_basic_preprocessing():
    """Demonstrate basic Arabic text processing without heavy dependencies"""
    print_section_header("BASIC ARABIC TEXT PROCESSING")
    
    # Sample Arabic financial texts
    sample_texts = [
        "الراجحي سهم قوي للاستثمار طويل المدى! ارتفع 3.5% اليوم 📈",
        "أرامكو تراجعت 2% بسبب انخفاض أسعار النفط العالمية",
        "سابك حققت أرباح ممتازة في الربع الثاني بنمو 25%",
        "الاتصالات السعودية تعلن شراكة جديدة مع شركة تقنية عالمية",
        "السوق اليوم أحمر بالكامل. تصريف قوي من المؤسسات"
    ]
    
    print("Sample Arabic financial texts:")
    for i, text in enumerate(sample_texts, 1):
        print(f"  {i}. {text}")
    
    # Basic text analysis
    print_subsection("Basic Text Analysis")
    
    # Saudi company mapping
    saudi_companies = {
        'الراجحي': '1120',
        'أرامكو': '2222', 
        'سابك': '2010',
        'الاتصالات': '7010',
        'الأهلي': '1180',
        'الرياض': '1010'
    }
    
    # Sentiment indicators
    bullish_terms = {'صاعد', 'ارتفع', 'قوي', 'ممتاز', 'نمو', 'أرباح', 'شراكة'}
    bearish_terms = {'تراجع', 'انخفاض', 'هبط', 'أحمر', 'تصريف', 'خسارة'}
    
    for i, text in enumerate(sample_texts, 1):
        print(f"\nText {i}: {text}")
        
        # Extract companies
        found_companies = []
        for company, ticker in saudi_companies.items():
            if company in text:
                found_companies.append(f"{company} ({ticker})")
        
        # Extract percentages
        import re
        percentages = re.findall(r'\d+\.?\d*%', text)
        
        # Basic sentiment analysis
        words = set(text.split())
        bullish_count = len(words.intersection(bullish_terms))
        bearish_count = len(words.intersection(bearish_terms))
        
        if bullish_count > bearish_count:
            sentiment = "Bullish"
        elif bearish_count > bullish_count:
            sentiment = "Bearish"
        else:
            sentiment = "Neutral"
        
        print(f"  Companies: {found_companies if found_companies else 'None'}")
        print(f"  Percentages: {percentages if percentages else 'None'}")
        print(f"  Sentiment: {sentiment} (B:{bullish_count}, Be:{bearish_count})")

def demo_text_normalization():
    """Demonstrate Arabic text normalization"""
    print_section_header("ARABIC TEXT NORMALIZATION")
    
    # Sample texts with various Arabic issues
    sample_texts = [
        "الرّاجحي سهم قويّ جداً للإستثمار",  # Diacritics
        "أرامكووووو تراجعت اليوم",  # Elongation
        "سابك إرتفعت ٥٪ في التداول",  # Arabic numerals
        "الإتصالات السعوديّة شركة ممتازة",  # Various hamza forms
    ]
    
    print("Original texts with normalization issues:")
    for i, text in enumerate(sample_texts, 1):
        print(f"  {i}. {text}")
    
    print_subsection("Normalized Texts")
    
    for i, text in enumerate(sample_texts, 1):
        # Basic normalization
        normalized = text
        
        # Remove common diacritics
        diacritics = 'ًٌٍَُِّْ'
        for diacritic in diacritics:
            normalized = normalized.replace(diacritic, '')
        
        # Remove elongation (repeated characters)
        import re
        normalized = re.sub(r'(.)\1{2,}', r'\1', normalized)
        
        # Convert Arabic-Indic numerals to Western
        arabic_nums = '٠١٢٣٤٥٦٧٨٩'
        western_nums = '0123456789'
        for ar, we in zip(arabic_nums, western_nums):
            normalized = normalized.replace(ar, we)
        
        # Normalize hamza variations
        normalized = re.sub(r'[أإآ]', 'ا', normalized)
        
        print(f"  {i}. Original: {text}")
        print(f"     Normalized: {normalized}")

def demo_financial_entity_extraction():
    """Demonstrate financial entity extraction"""
    print_section_header("FINANCIAL ENTITY EXTRACTION")
    
    sample_text = "اشتريت 1000 سهم من الراجحي بسعر 85.50 ريال. أتوقع ارتفاع 15% خلال الشهر القادم."
    
    print(f"Sample text: {sample_text}")
    
    print_subsection("Extracted Entities")
    
    import re
    
    # Extract monetary amounts
    money_patterns = [
        r'\d+\.?\d*\s*ريال',
        r'\d+\.?\d*\s*ر\.س',
        r'\d+\.?\d*\s*SAR'
    ]
    
    amounts = []
    for pattern in money_patterns:
        amounts.extend(re.findall(pattern, sample_text))
    
    # Extract percentages
    percentages = re.findall(r'\d+\.?\d*%', sample_text)
    
    # Extract numbers (potential share quantities)
    numbers = re.findall(r'\d+\.?\d*', sample_text)
    
    # Extract companies
    saudi_companies = {
        'الراجحي': '1120',
        'أرامكو': '2222', 
        'سابك': '2010',
        'الاتصالات': '7010'
    }
    
    found_companies = []
    for company, ticker in saudi_companies.items():
        if company in sample_text:
            found_companies.append({'name': company, 'ticker': ticker})
    
    print(f"Monetary amounts: {amounts}")
    print(f"Percentages: {percentages}")
    print(f"Numbers: {numbers}")
    print(f"Companies: {found_companies}")

def demo_sentiment_lexicon():
    """Demonstrate sentiment analysis using Arabic financial lexicon"""
    print_section_header("ARABIC FINANCIAL SENTIMENT LEXICON")
    
    # Comprehensive Arabic financial sentiment lexicon
    sentiment_lexicon = {
        'bullish': {
            'صاعد': 2.0, 'ارتفاع': 1.5, 'قوي': 1.5, 'ممتاز': 2.0,
            'فرصة': 1.5, 'اخضر': 1.0, 'ربح': 1.5, 'نمو': 1.5,
            'إيجابي': 1.0, 'دعم': 1.0, 'اختراق': 1.5, 'هدف': 1.0,
            'شراء': 1.5, 'استثمار': 1.0, 'انطلاق': 2.0
        },
        'bearish': {
            'هابط': -2.0, 'انخفاض': -1.5, 'ضعيف': -1.5, 'خسارة': -2.0,
            'تصريف': -1.5, 'احمر': -1.0, 'سلبي': -1.0, 'مقاومة': -1.0,
            'بيع': -1.5, 'تراجع': -1.5, 'هبوط': -1.5, 'انهيار': -2.5
        }
    }
    
    sample_texts = [
        "الراجحي سهم ممتاز للاستثمار! نمو قوي وأرباح مستمرة",
        "أرامكو تراجعت بقوة اليوم. انخفاض حاد في الأسعار",
        "السوق اليوم مستقر. لا توجد حركة واضحة"
    ]
    
    print("Sentiment Analysis Results:")
    
    for i, text in enumerate(sample_texts, 1):
        print(f"\nText {i}: {text}")
        
        words = text.split()
        total_score = 0
        word_count = 0
        found_indicators = []
        
        # Check bullish terms
        for word in words:
            if word in sentiment_lexicon['bullish']:
                score = sentiment_lexicon['bullish'][word]
                total_score += score
                word_count += 1
                found_indicators.append(f"bullish:{word}({score})")
        
        # Check bearish terms
        for word in words:
            if word in sentiment_lexicon['bearish']:
                score = sentiment_lexicon['bearish'][word]
                total_score += score
                word_count += 1
                found_indicators.append(f"bearish:{word}({score})")
        
        # Calculate normalized sentiment
        if word_count > 0:
            avg_score = total_score / word_count
            # Convert to 0-1 scale where 0.5 is neutral
            normalized_score = (avg_score + 2.5) / 5.0
            normalized_score = max(0.0, min(1.0, normalized_score))
        else:
            normalized_score = 0.5  # Neutral
        
        # Determine sentiment category
        if normalized_score > 0.6:
            sentiment = "Bullish"
        elif normalized_score < 0.4:
            sentiment = "Bearish"
        else:
            sentiment = "Neutral"
        
        print(f"  Sentiment: {sentiment}")
        print(f"  Score: {normalized_score:.3f}")
        print(f"  Indicators: {found_indicators}")

def demo_feature_extraction():
    """Demonstrate basic feature extraction for trading"""
    print_section_header("TRADING FEATURE EXTRACTION")
    
    # Sample posts with timestamps
    posts = [
        {"content": "الراجحي صاعد بقوة! +3.5%", "timestamp": "2024-08-09T10:00:00", "ticker": "1120"},
        {"content": "الراجحي يواصل الارتفاع", "timestamp": "2024-08-09T11:00:00", "ticker": "1120"},
        {"content": "أرامكو تراجعت -2%", "timestamp": "2024-08-09T10:30:00", "ticker": "2222"},
        {"content": "أرامكو في انخفاض مستمر", "timestamp": "2024-08-09T11:30:00", "ticker": "2222"},
    ]
    
    print("Sample posts:")
    for post in posts:
        print(f"  {post['timestamp']}: {post['content']} (Ticker: {post['ticker']})")
    
    print_subsection("Extracted Features")
    
    # Group by ticker
    ticker_data = {}
    for post in posts:
        ticker = post['ticker']
        if ticker not in ticker_data:
            ticker_data[ticker] = []
        ticker_data[ticker].append(post)
    
    # Simple sentiment scoring
    def get_sentiment_score(text):
        bullish_terms = {'صاعد', 'ارتفاع', 'قوي', 'ممتاز'}
        bearish_terms = {'تراجع', 'انخفاض', 'هبط'}
        
        words = set(text.split())
        bullish_count = len(words.intersection(bullish_terms))
        bearish_count = len(words.intersection(bearish_terms))
        
        if bullish_count > bearish_count:
            return 0.8
        elif bearish_count > bullish_count:
            return 0.2
        else:
            return 0.5
    
    for ticker, ticker_posts in ticker_data.items():
        print(f"\nTicker {ticker}:")
        
        # Calculate features
        post_count = len(ticker_posts)
        sentiment_scores = [get_sentiment_score(post['content']) for post in ticker_posts]
        avg_sentiment = sum(sentiment_scores) / len(sentiment_scores)
        
        # Extract percentages
        import re
        all_percentages = []
        for post in ticker_posts:
            percentages = re.findall(r'[+-]?\d+\.?\d*%', post['content'])
            all_percentages.extend(percentages)
        
        print(f"  Post count: {post_count}")
        print(f"  Average sentiment: {avg_sentiment:.3f}")
        print(f"  Sentiment scores: {sentiment_scores}")
        print(f"  Mentioned percentages: {all_percentages}")

def main():
    """Main demonstration function"""
    print_section_header("ARABIC NLP FOR SAUDI FINANCIAL TRADING - BASIC DEMO")
    print("This demonstration shows core Arabic NLP functionality")
    print("without requiring heavy machine learning dependencies.")
    
    try:
        demo_basic_preprocessing()
        demo_text_normalization()
        demo_financial_entity_extraction()
        demo_sentiment_lexicon()
        demo_feature_extraction()
        
        print_section_header("BASIC DEMO COMPLETED SUCCESSFULLY")
        print("Core Arabic NLP functionality working correctly!")
        print("\nNext steps to unlock full capabilities:")
        print("1. Install ML dependencies: pip install transformers sentence-transformers")
        print("2. Install vector database: pip install qdrant-client")
        print("3. Install analysis tools: pip install scikit-learn rank-bm25")
        print("4. Run advanced demo: python demo_advanced_pipeline.py")
        
        # Save demo results
        results = {
            "demo_completed": True,
            "timestamp": datetime.now().isoformat(),
            "components_tested": [
                "basic_preprocessing",
                "text_normalization", 
                "entity_extraction",
                "sentiment_analysis",
                "feature_extraction"
            ],
            "next_steps": [
                "Install ML dependencies",
                "Set up vector database",
                "Run advanced pipeline"
            ]
        }
        
        output_file = Path("demo_basic_results.json")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\nDemo results saved to: {output_file}")
        
    except Exception as e:
        print(f"\nDemo failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
