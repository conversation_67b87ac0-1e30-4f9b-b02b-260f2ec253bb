"""
Hybrid Embedding System for Arabic Financial Text

Combines dense neural embeddings (AraBERT, AraFinBERT) with sparse retrieval (BM25, TF-IDF)
for optimal semantic understanding and retrieval of Saudi financial content.
"""

import numpy as np
from typing import List, Dict, Tuple, Optional, Union
from dataclasses import dataclass
import pickle
from pathlib import Path

try:
    from sentence_transformers import SentenceTransformer
    from transformers import AutoTokenizer, AutoModel
    import torch
    import torch.nn.functional as F
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    print("Warning: Transformers not available. Install with: pip install transformers sentence-transformers")
    SentenceTransformer = AutoTokenizer = AutoModel = torch = None
    TRANSFORMERS_AVAILABLE = False

try:
    from rank_bm25 import BM25<PERSON>kapi
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    SKLEARN_AVAILABLE = True
except ImportError:
    print("Warning: Scikit-learn or rank-bm25 not available")
    BM25Okapi = TfidfVectorizer = cosine_similarity = None
    SKLEARN_AVAILABLE = False

@dataclass
class EmbeddingResult:
    """Container for embedding results"""
    dense_embeddings: np.ndarray
    sparse_features: Optional[np.ndarray] = None
    metadata: Dict = None


class ArabicFinancialEmbedder:
    """
    Hybrid embedding system optimized for Arabic financial content
    
    Features:
    - Dense embeddings: AraBERT, AraFinBERT, multilingual models
    - Sparse retrieval: BM25, TF-IDF with Arabic preprocessing
    - Hybrid search combining both approaches
    - Financial domain adaptation
    """
    
    def __init__(self, 
                 dense_model: str = "aubmindlab/bert-base-arabertv2",
                 use_financial_model: bool = True,
                 device: str = "auto"):
        
        self.device = self._setup_device(device)
        self.dense_model_name = dense_model
        self.use_financial_model = use_financial_model
        
        # Initialize models
        self._load_dense_models()
        self._initialize_sparse_models()
        
        # Corpus storage for retrieval
        self.corpus = []
        self.corpus_embeddings = None
        self.fitted = False
        
    def _setup_device(self, device: str) -> str:
        """Setup computation device"""
        if device == "auto":
            if torch.cuda.is_available():
                return "cuda"
            elif torch.backends.mps.is_available():
                return "mps"
            else:
                return "cpu"
        return device
    
    def _load_dense_models(self):
        """Load dense embedding models"""
        print(f"Loading dense model: {self.dense_model_name}")
        
        # Primary Arabic model
        self.dense_model = SentenceTransformer(self.dense_model_name, device=self.device)
        
        # Financial-specific model (if available)
        self.financial_model = None
        if self.use_financial_model:
            try:
                # Try to load a financial-specific Arabic model
                financial_models = [
                    "aubmindlab/bert-base-arabertv2-twitter",  # Twitter variant for social media
                    "asafaya/bert-base-arabic",  # Alternative Arabic BERT
                    "CAMeL-Lab/bert-base-arabic-camelbert-mix"  # CAMeL BERT
                ]
                
                for model_name in financial_models:
                    try:
                        self.financial_model = SentenceTransformer(model_name, device=self.device)
                        print(f"Loaded financial model: {model_name}")
                        break
                    except:
                        continue
                        
            except Exception as e:
                print(f"Could not load financial model: {e}")
        
        # Get embedding dimensions
        self.embedding_dim = self.dense_model.get_sentence_embedding_dimension()
        print(f"Dense embedding dimension: {self.embedding_dim}")
    
    def _initialize_sparse_models(self):
        """Initialize sparse retrieval models"""
        
        # BM25 for Arabic (will be fitted on corpus)
        self.bm25 = None
        
        # TF-IDF with Arabic-specific settings
        self.tfidf = TfidfVectorizer(
            max_features=10000,
            ngram_range=(1, 3),  # Unigrams to trigrams
            analyzer='word',
            lowercase=False,  # Arabic doesn't have case
            token_pattern=r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+',  # Arabic Unicode ranges
            min_df=2,  # Ignore terms that appear in less than 2 documents
            max_df=0.95  # Ignore terms that appear in more than 95% of documents
        )
        
        print("Initialized sparse models (BM25, TF-IDF)")
    
    def fit_sparse(self, texts: List[str]):
        """Fit sparse models on corpus"""
        if not texts:
            print("Warning: Empty corpus provided for sparse fitting")
            return
        
        print(f"Fitting sparse models on {len(texts)} documents...")
        
        # Tokenize for BM25 (simple whitespace tokenization)
        tokenized_texts = [text.split() for text in texts]
        self.bm25 = BM25Okapi(tokenized_texts)
        
        # Fit TF-IDF
        self.tfidf.fit(texts)
        
        # Store corpus
        self.corpus = texts
        self.fitted = True
        
        print("Sparse models fitted successfully")
    
    def encode_dense(self, 
                    texts: List[str], 
                    batch_size: int = 32,
                    normalize: bool = True,
                    use_financial: bool = False) -> np.ndarray:
        """
        Generate dense embeddings
        
        Args:
            texts: List of texts to embed
            batch_size: Batch size for processing
            normalize: Whether to L2 normalize embeddings
            use_financial: Whether to use financial-specific model
            
        Returns:
            Dense embeddings array
        """
        if not texts:
            return np.array([])
        
        # Choose model
        model = self.financial_model if (use_financial and self.financial_model) else self.dense_model
        
        # Generate embeddings
        embeddings = model.encode(
            texts,
            batch_size=batch_size,
            show_progress_bar=len(texts) > 100,
            convert_to_numpy=True,
            normalize_embeddings=normalize
        )
        
        return embeddings
    
    def encode_sparse_tfidf(self, texts: List[str]) -> np.ndarray:
        """Generate TF-IDF sparse features"""
        if not self.fitted:
            raise ValueError("Sparse models not fitted. Call fit_sparse() first.")
        
        return self.tfidf.transform(texts).toarray()
    
    def search_bm25(self, query: str, top_k: int = 10) -> List[Tuple[int, float]]:
        """Search using BM25"""
        if not self.fitted or not self.bm25:
            raise ValueError("BM25 not fitted. Call fit_sparse() first.")
        
        tokenized_query = query.split()
        scores = self.bm25.get_scores(tokenized_query)
        
        # Get top-k indices
        top_indices = np.argsort(scores)[-top_k:][::-1]
        
        return [(idx, scores[idx]) for idx in top_indices]
    
    def search_dense(self, 
                    query: str, 
                    corpus_embeddings: Optional[np.ndarray] = None,
                    top_k: int = 10,
                    use_financial: bool = False) -> List[Tuple[int, float]]:
        """Search using dense embeddings"""
        
        # Generate query embedding
        query_embedding = self.encode_dense([query], use_financial=use_financial)[0]
        
        # Use provided corpus embeddings or generate them
        if corpus_embeddings is None:
            if not self.fitted:
                raise ValueError("No corpus embeddings provided and sparse models not fitted")
            corpus_embeddings = self.encode_dense(self.corpus, use_financial=use_financial)
        
        # Compute similarities
        similarities = cosine_similarity([query_embedding], corpus_embeddings)[0]
        
        # Get top-k
        top_indices = np.argsort(similarities)[-top_k:][::-1]
        
        return [(idx, similarities[idx]) for idx in top_indices]
    
    def search_hybrid(self, 
                     query: str,
                     corpus_embeddings: Optional[np.ndarray] = None,
                     top_k: int = 10,
                     alpha: float = 0.7,
                     use_financial: bool = False) -> List[Tuple[int, float, Dict]]:
        """
        Hybrid search combining dense and sparse methods
        
        Args:
            query: Search query
            corpus_embeddings: Pre-computed corpus embeddings (optional)
            top_k: Number of results to return
            alpha: Weight for dense scores (1-alpha for sparse)
            use_financial: Whether to use financial model for dense search
            
        Returns:
            List of (index, combined_score, score_breakdown)
        """
        if not self.fitted:
            raise ValueError("Models not fitted. Call fit_sparse() first.")
        
        # Dense search
        dense_results = self.search_dense(
            query, corpus_embeddings, top_k=min(top_k*2, len(self.corpus)), 
            use_financial=use_financial
        )
        
        # Sparse search (BM25)
        sparse_results = self.search_bm25(query, top_k=min(top_k*2, len(self.corpus)))
        
        # Combine scores
        combined_scores = {}
        
        # Normalize and combine dense scores
        if dense_results:
            dense_scores = [score for _, score in dense_results]
            dense_min, dense_max = min(dense_scores), max(dense_scores)
            dense_range = dense_max - dense_min if dense_max > dense_min else 1.0
            
            for idx, score in dense_results:
                normalized_dense = (score - dense_min) / dense_range
                combined_scores[idx] = {
                    'dense': normalized_dense,
                    'sparse': 0.0,
                    'combined': alpha * normalized_dense
                }
        
        # Normalize and add sparse scores
        if sparse_results:
            sparse_scores = [score for _, score in sparse_results]
            sparse_min, sparse_max = min(sparse_scores), max(sparse_scores)
            sparse_range = sparse_max - sparse_min if sparse_max > sparse_min else 1.0
            
            for idx, score in sparse_results:
                normalized_sparse = (score - sparse_min) / sparse_range
                
                if idx in combined_scores:
                    combined_scores[idx]['sparse'] = normalized_sparse
                    combined_scores[idx]['combined'] = (
                        alpha * combined_scores[idx]['dense'] + 
                        (1 - alpha) * normalized_sparse
                    )
                else:
                    combined_scores[idx] = {
                        'dense': 0.0,
                        'sparse': normalized_sparse,
                        'combined': (1 - alpha) * normalized_sparse
                    }
        
        # Sort by combined score and return top-k
        sorted_results = sorted(
            combined_scores.items(), 
            key=lambda x: x[1]['combined'], 
            reverse=True
        )[:top_k]
        
        return [
            (idx, scores['combined'], scores) 
            for idx, scores in sorted_results
        ]
    
    def fit_and_encode_corpus(self, texts: List[str]) -> np.ndarray:
        """Fit sparse models and encode corpus with dense embeddings"""
        
        # Fit sparse models
        self.fit_sparse(texts)
        
        # Generate dense embeddings for corpus
        print("Generating dense embeddings for corpus...")
        corpus_embeddings = self.encode_dense(texts)
        
        # Store for future use
        self.corpus_embeddings = corpus_embeddings
        
        return corpus_embeddings
    
    def save_models(self, save_dir: str):
        """Save fitted models and embeddings"""
        save_path = Path(save_dir)
        save_path.mkdir(parents=True, exist_ok=True)
        
        # Save sparse models
        if self.fitted:
            with open(save_path / "bm25.pkl", "wb") as f:
                pickle.dump(self.bm25, f)
            
            with open(save_path / "tfidf.pkl", "wb") as f:
                pickle.dump(self.tfidf, f)
            
            # Save corpus and embeddings
            np.save(save_path / "corpus_embeddings.npy", self.corpus_embeddings)
            
            with open(save_path / "corpus.pkl", "wb") as f:
                pickle.dump(self.corpus, f)
        
        print(f"Models saved to {save_dir}")
    
    def load_models(self, save_dir: str):
        """Load fitted models and embeddings"""
        save_path = Path(save_dir)
        
        if not save_path.exists():
            raise FileNotFoundError(f"Save directory {save_dir} not found")
        
        # Load sparse models
        with open(save_path / "bm25.pkl", "rb") as f:
            self.bm25 = pickle.load(f)
        
        with open(save_path / "tfidf.pkl", "rb") as f:
            self.tfidf = pickle.load(f)
        
        # Load corpus and embeddings
        self.corpus_embeddings = np.load(save_path / "corpus_embeddings.npy")
        
        with open(save_path / "corpus.pkl", "rb") as f:
            self.corpus = pickle.load(f)
        
        self.fitted = True
        print(f"Models loaded from {save_dir}")
