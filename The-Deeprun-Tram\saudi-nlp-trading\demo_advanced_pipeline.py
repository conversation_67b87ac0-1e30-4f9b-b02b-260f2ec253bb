#!/usr/bin/env python3
"""
Demonstration Script for Advanced Arabic NLP Pipeline

This script demonstrates the complete pipeline with sample data,
showcasing all the advanced features for Saudi financial sentiment analysis.
"""

import asyncio
import json
from datetime import datetime, timedelta
from pathlib import Path
import sys

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.nlp.pipeline import ArabicFinancialNLPPipeline
from src.nlp.preprocessor import ArabicFinancialPreprocessor
from src.nlp.embeddings import ArabicFinancialEmbedder
from src.nlp.ner import FinancialNER
from src.nlp.sentiment import FinancialSentimentAnalyzer
from src.nlp.feature_engineering import FinancialFeatureEngineer

# Sample Arabic financial posts for demonstration
SAMPLE_POSTS = [
    {
        "post_id": "demo_001",
        "content": "سهم الراجحي اليوم صاعد بقوة! ارتفع 3.5% وكسر مقاومة 85 ريال. توصية شراء قوية للمحفظة طويلة المدى.",
        "author": "trader_001",
        "timestamp": "2024-08-09T10:30:00",
        "source": "hawamer",
        "tickers": ["1120"],
        "likes": 15,
        "shares": 3
    },
    {
        "post_id": "demo_002", 
        "content": "أرامكو تراجعت 2% بسبب انخفاض أسعار النفط. السوق متخوف من الركود العالمي. احذروا من الاستثمار الآن.",
        "author": "analyst_pro",
        "timestamp": "2024-08-09T11:15:00",
        "source": "hawamer",
        "tickers": ["2222"],
        "likes": 8,
        "shares": 1
    },
    {
        "post_id": "demo_003",
        "content": "سابك حققت أرباح ممتازة في الربع الثاني! نمو 25% مقارنة بالعام الماضي. السهم هدفه 120 ريال قريباً إن شاء الله.",
        "author": "investor_sa",
        "timestamp": "2024-08-09T12:00:00", 
        "source": "hawamer",
        "tickers": ["2010"],
        "likes": 22,
        "shares": 7
    },
    {
        "post_id": "demo_004",
        "content": "الاتصالات السعودية تعلن عن شراكة جديدة مع شركة تقنية عالمية. السهم يمكن يطير للقمر! 🚀",
        "author": "tech_trader",
        "timestamp": "2024-08-09T13:30:00",
        "source": "hawamer", 
        "tickers": ["7010"],
        "likes": 31,
        "shares": 12
    },
    {
        "post_id": "demo_005",
        "content": "السوق اليوم أحمر بالكامل. تصريف قوي من المؤسسات. أنصح بالحذر وعدم الدخول في أي صفقات جديدة.",
        "author": "market_watcher",
        "timestamp": "2024-08-09T14:45:00",
        "source": "hawamer",
        "tickers": [],
        "likes": 5,
        "shares": 2
    }
]


def print_section_header(title: str):
    """Print a formatted section header"""
    print("\n" + "="*80)
    print(f" {title}")
    print("="*80)


def print_subsection(title: str):
    """Print a formatted subsection header"""
    print(f"\n--- {title} ---")


async def demo_preprocessing():
    """Demonstrate Arabic preprocessing capabilities"""
    print_section_header("ARABIC PREPROCESSING DEMONSTRATION")
    
    preprocessor = ArabicFinancialPreprocessor()
    
    sample_text = "دائماً أسمع من أخوياي يقولون الراجحي صاعد وسابك هابط... وش رأيكم؟"
    
    print(f"Original text: {sample_text}")
    
    result = preprocessor.process(sample_text)
    
    print_subsection("Preprocessing Results")
    print(f"Normalized text: {result.normalized_text}")
    print(f"Clean text: {result.clean_text}")
    print(f"Tokens: {result.tokens}")
    print(f"Financial entities: {result.entities}")
    print(f"Sentiment indicators: {result.sentiment_indicators}")
    print(f"Processing metadata: {result.metadata}")


async def demo_ner():
    """Demonstrate financial NER capabilities"""
    print_section_header("FINANCIAL NAMED ENTITY RECOGNITION")
    
    ner = FinancialNER()
    
    sample_text = "اشتريت 1000 سهم من الراجحي بسعر 85.50 ريال. أتوقع ارتفاع 15% خلال الشهر القادم."
    
    print(f"Text: {sample_text}")
    
    result = ner.extract_entities(sample_text)
    
    print_subsection("Extracted Entities")
    for entity in result.entities:
        print(f"  {entity.label}: '{entity.text}' (confidence: {entity.confidence:.2f})")
        if entity.metadata:
            print(f"    Metadata: {entity.metadata}")
    
    print_subsection("Entity Summary")
    for category, entities in result.summary.items():
        if entities:
            print(f"  {category}: {entities}")


async def demo_sentiment():
    """Demonstrate sentiment analysis capabilities"""
    print_section_header("FINANCIAL SENTIMENT ANALYSIS")
    
    analyzer = FinancialSentimentAnalyzer()
    
    texts = [
        "الراجحي سهم ممتاز للاستثمار طويل المدى! أرباح قوية ونمو مستمر 📈",
        "أرامكو تراجعت بقوة اليوم. السوق خايف من انخفاض أسعار النفط 📉",
        "السوق اليوم مستقر. لا توجد حركة واضحة في معظم الأسهم."
    ]
    
    for i, text in enumerate(texts, 1):
        print(f"\nText {i}: {text}")
        
        result = analyzer.analyze_sentiment(text)
        
        print(f"  Sentiment: {result.overall_sentiment}")
        print(f"  Confidence: {result.confidence:.2f}")
        print(f"  Ensemble Score: {result.scores.get('ensemble', 0):.2f}")
        print(f"  Lexicon Score: {result.lexicon_score:.2f}")
        
        if result.indicators:
            print(f"  Indicators: {result.indicators[:3]}...")  # Show first 3


async def demo_embeddings():
    """Demonstrate embedding generation"""
    print_section_header("HYBRID EMBEDDING SYSTEM")
    
    embedder = ArabicFinancialEmbedder()
    
    texts = [
        "الراجحي سهم قوي للاستثمار",
        "أرامكو تراجعت بسبب أسعار النفط", 
        "سابك حققت أرباح ممتازة"
    ]
    
    print("Sample texts:")
    for i, text in enumerate(texts, 1):
        print(f"  {i}. {text}")
    
    # Fit sparse models
    embedder.fit_sparse(texts)
    
    # Generate dense embeddings
    embeddings = embedder.encode_dense(texts)
    
    print_subsection("Embedding Results")
    print(f"Embedding shape: {embeddings.shape}")
    print(f"Embedding dimension: {embeddings.shape[1]}")
    
    # Demonstrate hybrid search
    query = "استثمار في البنوك"
    print(f"\nHybrid search query: '{query}'")
    
    search_results = embedder.search_hybrid(query, top_k=2)
    
    print("Search results:")
    for idx, score, breakdown in search_results:
        print(f"  Text {idx+1}: {texts[idx]}")
        print(f"    Combined score: {score:.3f}")
        print(f"    Dense: {breakdown['dense']:.3f}, Sparse: {breakdown['sparse']:.3f}")


async def demo_feature_engineering():
    """Demonstrate feature engineering"""
    print_section_header("TRADING FEATURE ENGINEERING")
    
    import pandas as pd
    
    # Convert sample posts to DataFrame
    posts_df = pd.DataFrame(SAMPLE_POSTS)
    posts_df['timestamp'] = pd.to_datetime(posts_df['timestamp'])
    
    # Add sentiment scores (normally from sentiment analysis)
    posts_df['sentiment_score'] = [0.8, 0.2, 0.9, 0.85, 0.3]
    posts_df['sentiment'] = ['bullish', 'bearish', 'bullish', 'bullish', 'bearish']
    
    engineer = FinancialFeatureEngineer()
    
    print("Input posts:")
    for _, post in posts_df.iterrows():
        print(f"  {post['post_id']}: {post['content'][:50]}... (sentiment: {post['sentiment']})")
    
    # Engineer features
    feature_set = engineer.engineer_features(posts_df, time_freq='H')
    
    print_subsection("Feature Engineering Results")
    print(f"Total features generated: {len(feature_set.feature_names)}")
    print(f"Feature categories: {feature_set.metadata.get('feature_categories', {})}")
    
    if not feature_set.features.empty:
        print(f"Features shape: {feature_set.features.shape}")
        print(f"Sample features: {feature_set.feature_names[:10]}")
        
        # Show sample feature values for first ticker
        sample_ticker = feature_set.features['ticker'].iloc[0] if 'ticker' in feature_set.features.columns else None
        if sample_ticker:
            ticker_data = feature_set.features[feature_set.features['ticker'] == sample_ticker].iloc[0]
            print(f"\nSample features for ticker {sample_ticker}:")
            for feature in feature_set.feature_names[:5]:
                if feature in ticker_data:
                    print(f"  {feature}: {ticker_data[feature]:.3f}")


async def demo_full_pipeline():
    """Demonstrate the complete NLP pipeline"""
    print_section_header("COMPLETE NLP PIPELINE DEMONSTRATION")
    
    # Initialize pipeline
    pipeline = ArabicFinancialNLPPipeline(
        config_path="config/nlp_config.json",
        cache_dir="cache/demo"
    )
    
    print(f"Processing {len(SAMPLE_POSTS)} sample posts...")
    
    # Run complete pipeline
    result = pipeline.run_pipeline(SAMPLE_POSTS)
    
    print_subsection("Pipeline Results")
    print(f"Posts processed: {len(result.processed_posts)}")
    print(f"Embeddings shape: {result.embeddings.shape if len(result.embeddings) > 0 else 'None'}")
    print(f"Features generated: {len(result.features.feature_names) if result.features else 0}")
    print(f"Vector store IDs: {len(result.vector_store_ids)}")
    
    print_subsection("Processing Metadata")
    for key, value in result.metadata.items():
        print(f"  {key}: {value}")
    
    print_subsection("Performance Metrics")
    for stage, time_taken in result.performance_metrics.get('processing_times', {}).items():
        print(f"  {stage}: {time_taken:.3f}s")
    
    # Show sample processed post
    if not result.processed_posts.empty:
        print_subsection("Sample Processed Post")
        sample_post = result.processed_posts.iloc[0]
        print(f"  Original: {sample_post.get('original_content', '')[:100]}...")
        print(f"  Clean: {sample_post.get('clean_content', '')[:100]}...")
        print(f"  Sentiment: {sample_post.get('sentiment', 'N/A')} ({sample_post.get('sentiment_score', 0):.2f})")
        print(f"  Companies: {sample_post.get('companies', [])}")
        print(f"  Tickers: {sample_post.get('tickers', [])}")
    
    # Save demo results
    output_dir = Path("demo_output")
    output_dir.mkdir(exist_ok=True)
    
    pipeline.save_results(result, output_dir)
    print(f"\nDemo results saved to: {output_dir}")
    
    return result


async def main():
    """Main demonstration function"""
    print_section_header("ADVANCED ARABIC NLP PIPELINE FOR SAUDI FINANCIAL TRADING")
    print("This demonstration showcases the complete pipeline capabilities")
    print("for processing Arabic financial social media content.")
    
    try:
        # Run individual component demos
        await demo_preprocessing()
        await demo_ner()
        await demo_sentiment()
        await demo_embeddings()
        await demo_feature_engineering()
        
        # Run complete pipeline demo
        result = await demo_full_pipeline()
        
        print_section_header("DEMONSTRATION COMPLETED SUCCESSFULLY")
        print("All components working correctly!")
        print(f"Total processing time: {result.metadata.get('total_processing_time', 0):.2f} seconds")
        print("\nNext steps:")
        print("1. Install required dependencies: pip install -r requirements.txt")
        print("2. Set up Qdrant vector database (optional)")
        print("3. Configure CAPTCHA_API_KEY for web scraping")
        print("4. Run: python -m src.pipeline.advanced_orchestrator --urls hawamer_urls.txt")
        
    except ImportError as e:
        print(f"\nError: Missing dependencies - {e}")
        print("Please install requirements: pip install -r requirements.txt")
    except Exception as e:
        print(f"\nDemo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
