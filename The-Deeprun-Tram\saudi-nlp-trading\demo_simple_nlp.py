#!/usr/bin/env python3
"""
Demo Script for Simple NLP Pipeline

Demonstrates the complete pipeline with sample Arabic financial posts.
Focus: Show how raw Arabic text becomes trading signals.
"""

import sys
from pathlib import Path
import json
from datetime import datetime, timedelta
import pandas as pd

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from nlp.simple_pipeline import SimpleNLPPipeline

def print_header(title: str):
    """Print formatted header"""
    print("\n" + "="*80)
    print(f" {title}")
    print("="*80)

def print_subheader(title: str):
    """Print formatted subheader"""
    print(f"\n--- {title} ---")

def create_sample_posts():
    """Create realistic sample posts for demonstration"""
    base_time = datetime.now()
    
    sample_posts = [
        {
            'post_id': 'demo_001',
            'content': 'الراجحي سهم قوي للاستثمار! ارتفع 3.5% اليوم وكسر مقاومة 85 ريال 📈',
            'author': 'saudi_trader',
            'timestamp': (base_time - timedelta(hours=2)).isoformat(),
            'source': 'hawamer',
            'likes': 25,
            'shares': 5
        },
        {
            'post_id': 'demo_002',
            'content': 'أرامكو تراجعت 2% بسبب انخفاض أسعار النفط العالمية. السوق متخوف من الركود',
            'author': 'oil_analyst',
            'timestamp': (base_time - timedelta(hours=1, minutes=30)).isoformat(),
            'source': 'hawamer',
            'likes': 12,
            'shares': 3
        },
        {
            'post_id': 'demo_003',
            'content': 'سابك حققت أرباح ممتازة في الربع الثاني! نمو 25% مقارنة بالعام الماضي 🚀',
            'author': 'financial_expert',
            'timestamp': (base_time - timedelta(hours=1)).isoformat(),
            'source': 'hawamer',
            'likes': 45,
            'shares': 12
        },
        {
            'post_id': 'demo_004',
            'content': 'الاتصالات السعودية تعلن شراكة جديدة مع شركة تقنية عالمية. السهم يمكن يطير!',
            'author': 'tech_investor',
            'timestamp': (base_time - timedelta(minutes=45)).isoformat(),
            'source': 'hawamer',
            'likes': 33,
            'shares': 8
        },
        {
            'post_id': 'demo_005',
            'content': 'السوق اليوم أحمر بالكامل. تصريف قوي من المؤسسات. أنصح بالحذر والانتظار',
            'author': 'market_watcher',
            'timestamp': (base_time - timedelta(minutes=30)).isoformat(),
            'source': 'hawamer',
            'likes': 18,
            'shares': 4
        },
        {
            'post_id': 'demo_006',
            'content': 'البنك الأهلي 1180 نتائج قوية هذا الربع. توقعات إيجابية للأرباح القادمة',
            'author': 'bank_specialist',
            'timestamp': (base_time - timedelta(minutes=15)).isoformat(),
            'source': 'hawamer',
            'likes': 22,
            'shares': 6
        },
        {
            'post_id': 'demo_007',
            'content': 'معادن 1211 في اتجاه صاعد. اختراق مستوى 45 ريال بحجم تداول عالي',
            'author': 'mining_trader',
            'timestamp': (base_time - timedelta(minutes=10)).isoformat(),
            'source': 'hawamer',
            'likes': 15,
            'shares': 2
        },
        {
            'post_id': 'demo_008',
            'content': 'قطاع البتروكيماويات تحت ضغط. ينبع وسابك في تراجع مستمر منذ أسبوع',
            'author': 'sector_analyst',
            'timestamp': (base_time - timedelta(minutes=5)).isoformat(),
            'source': 'hawamer',
            'likes': 8,
            'shares': 1
        }
    ]
    
    return sample_posts

def demo_preprocessing():
    """Demonstrate preprocessing capabilities"""
    print_header("ARABIC PREPROCESSING DEMONSTRATION")
    
    from nlp.simple_preprocessor import ArabicPreprocessor
    preprocessor = ArabicPreprocessor()
    
    sample_text = "الرّاجحي سهم قويّ جداً! ارتفع ٣.٥٪ اليوم 📈📈"
    
    print(f"Original text: {sample_text}")
    
    # Show normalization steps
    normalized = preprocessor.normalize_arabic(sample_text)
    print(f"Normalized: {normalized}")
    
    clean = preprocessor.clean_for_embedding(sample_text)
    print(f"Clean for embedding: {clean}")
    
    # Extract entities
    entities = preprocessor.extract_financial_entities(sample_text)
    print(f"\nExtracted entities:")
    for entity_type, values in entities.items():
        if values:
            print(f"  {entity_type}: {values}")
    
    # Sentiment scoring
    score, indicators = preprocessor.calculate_sentiment_score(sample_text)
    print(f"\nSentiment score: {score:.3f}")
    print(f"Indicators: {indicators}")

def demo_sentiment_analysis():
    """Demonstrate sentiment analysis"""
    print_header("SENTIMENT ANALYSIS DEMONSTRATION")
    
    from nlp.simple_sentiment import SimpleSentimentAnalyzer
    analyzer = SimpleSentimentAnalyzer(use_model=False)  # Lexicon only for demo
    
    test_texts = [
        "الراجحي سهم ممتاز للاستثمار! قوي جداً ونمو مستمر",
        "أرامكو تراجعت بقوة اليوم. خسائر كبيرة وضغط بيع",
        "السوق مستقر اليوم. لا توجد حركة واضحة في الأسهم",
        "سابك اختراق قوي! انطلاق نحو الأهداف العليا 🚀",
        "قطاع البنوك في انهيار. تصريف من المؤسسات"
    ]
    
    for i, text in enumerate(test_texts, 1):
        print(f"\nText {i}: {text}")
        result = analyzer.analyze_sentiment(text)
        
        print(f"  Sentiment: {result['sentiment'].upper()}")
        print(f"  Score: {result['score']:.3f}")
        print(f"  Confidence: {result['confidence']:.3f}")
        print(f"  Method: {result['method']}")
        
        # Show lexicon matches
        lexicon_result = result['lexicon_result']
        if lexicon_result['bullish_matches']:
            print(f"  Bullish terms: {[term for term, score in lexicon_result['bullish_matches']]}")
        if lexicon_result['bearish_matches']:
            print(f"  Bearish terms: {[term for term, score in lexicon_result['bearish_matches']]}")

def demo_complete_pipeline():
    """Demonstrate complete pipeline"""
    print_header("COMPLETE PIPELINE DEMONSTRATION")
    
    # Create sample posts
    posts = create_sample_posts()
    
    print(f"Processing {len(posts)} sample posts...")
    
    # Initialize pipeline (no embeddings for demo speed)
    pipeline = SimpleNLPPipeline(
        enable_embeddings=False,
        enable_model_sentiment=False
    )
    
    # Process posts
    print_subheader("Processing Posts")
    df = pipeline.process_batch(posts)
    
    print(f"Successfully processed: {len(df)} posts")
    print(f"Financial posts: {df['has_financial_content'].sum()}")
    
    # Show sample processed post
    print_subheader("Sample Processed Post")
    sample_post = df.iloc[0]
    print(f"Original: {sample_post['original_text']}")
    print(f"Clean: {sample_post['clean_text']}")
    print(f"Companies: {sample_post['companies']}")
    print(f"Tickers: {sample_post['tickers']}")
    print(f"Sentiment: {sample_post['sentiment']} ({sample_post['sentiment_score']:.3f})")
    print(f"Confidence: {sample_post['sentiment_confidence']:.3f}")
    
    # Generate trading features
    print_subheader("Generating Trading Features")
    features_df = pipeline.generate_trading_features(df, time_window='30T')  # 30-minute windows
    
    print(f"Generated {len(features_df)} feature rows")
    print(f"Unique tickers: {features_df['ticker'].nunique()}")
    
    # Show sample features
    print_subheader("Sample Trading Features")
    if not features_df.empty:
        sample_features = features_df.iloc[0]
        feature_cols = [
            'ticker', 'post_count', 'sentiment_mean', 'bullish_ratio', 
            'bearish_ratio', 'avg_confidence', 'engagement_score'
        ]
        
        for col in feature_cols:
            if col in sample_features:
                value = sample_features[col]
                if isinstance(value, float):
                    print(f"  {col}: {value:.3f}")
                else:
                    print(f"  {col}: {value}")
    
    # Summary statistics
    print_subheader("Summary Statistics")
    
    # Sentiment distribution
    sentiment_dist = df['sentiment'].value_counts()
    print("Sentiment Distribution:")
    for sentiment, count in sentiment_dist.items():
        percentage = count / len(df) * 100
        print(f"  {sentiment.title()}: {count} ({percentage:.1f}%)")
    
    # Top companies
    all_companies = []
    for companies in df['companies']:
        all_companies.extend(companies)
    
    if all_companies:
        company_counts = pd.Series(all_companies).value_counts().head(5)
        print("\nTop Mentioned Companies:")
        for company, count in company_counts.items():
            print(f"  {company}: {count} mentions")
    
    # Feature summary by ticker
    if not features_df.empty:
        print("\nAverage Sentiment by Ticker:")
        ticker_sentiment = features_df.groupby('ticker')['sentiment_mean'].mean().sort_values(ascending=False)
        for ticker, sentiment in ticker_sentiment.items():
            print(f"  {ticker}: {sentiment:.3f}")
    
    return df, features_df

def demo_performance_metrics():
    """Demonstrate performance measurement"""
    print_header("PERFORMANCE METRICS DEMONSTRATION")
    
    # Create larger dataset for performance testing
    posts = create_sample_posts() * 20  # 160 posts
    
    # Add unique IDs
    for i, post in enumerate(posts):
        post['post_id'] = f"perf_{i:03d}"
    
    print(f"Testing performance with {len(posts)} posts...")
    
    # Initialize pipeline
    pipeline = SimpleNLPPipeline(enable_embeddings=False, enable_model_sentiment=False)
    
    # Run evaluation
    metrics = pipeline.evaluate_pipeline(posts)
    
    print_subheader("Performance Metrics")
    key_metrics = [
        'total_input_posts', 'valid_processed_posts', 'processing_success_rate',
        'total_processing_time', 'avg_processing_time_per_post', 'financial_posts_ratio'
    ]
    
    for metric in key_metrics:
        if metric in metrics:
            value = metrics[metric]
            if isinstance(value, float):
                if 'time' in metric:
                    print(f"  {metric}: {value:.4f} seconds")
                elif 'ratio' in metric or 'rate' in metric:
                    print(f"  {metric}: {value:.2%}")
                else:
                    print(f"  {metric}: {value:.3f}")
            else:
                print(f"  {metric}: {value}")
    
    # Component stats
    print_subheader("Component Statistics")
    pipeline_stats = pipeline.get_stats()
    for key, value in pipeline_stats.items():
        if isinstance(value, float):
            print(f"  {key}: {value:.3f}")
        else:
            print(f"  {key}: {value}")

def save_demo_results(df, features_df):
    """Save demo results for inspection"""
    output_dir = Path("demo_output")
    output_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Save processed posts
    posts_file = output_dir / f"demo_processed_posts_{timestamp}.json"
    df.to_json(posts_file, orient='records', force_ascii=False, indent=2)
    
    # Save features
    features_file = output_dir / f"demo_features_{timestamp}.csv"
    features_df.to_csv(features_file, index=False)
    
    # Save summary
    summary = {
        'demo_timestamp': timestamp,
        'total_posts': len(df),
        'financial_posts': int(df['has_financial_content'].sum()),
        'sentiment_distribution': df['sentiment'].value_counts().to_dict(),
        'unique_tickers': int(features_df['ticker'].nunique()) if not features_df.empty else 0,
        'avg_sentiment_confidence': float(df['sentiment_confidence'].mean()),
        'files_saved': {
            'processed_posts': str(posts_file),
            'features': str(features_file)
        }
    }
    
    summary_file = output_dir / f"demo_summary_{timestamp}.json"
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, ensure_ascii=False, indent=2)
    
    print(f"\nDemo results saved to {output_dir}/")
    print(f"  Processed posts: {posts_file.name}")
    print(f"  Features: {features_file.name}")
    print(f"  Summary: {summary_file.name}")

def main():
    """Main demo function"""
    print_header("SIMPLE NLP PIPELINE FOR ARABIC FINANCIAL TRADING")
    print("This demo shows how Arabic financial social media content")
    print("is converted into numerical features for trading signals.")
    
    try:
        # Run component demos
        demo_preprocessing()
        demo_sentiment_analysis()
        
        # Run complete pipeline demo
        df, features_df = demo_complete_pipeline()
        
        # Performance metrics
        demo_performance_metrics()
        
        # Save results
        save_demo_results(df, features_df)
        
        print_header("DEMO COMPLETED SUCCESSFULLY")
        print("Key achievements:")
        print("✓ Arabic text preprocessing with normalization")
        print("✓ Financial entity extraction (companies, tickers, amounts)")
        print("✓ Sentiment analysis with confidence scoring")
        print("✓ Trading feature generation with time aggregation")
        print("✓ Performance measurement and optimization")
        
        print("\nNext steps:")
        print("1. Run with real scraped data: python src/nlp/run_simple_pipeline.py --data data/scraped/posts.json")
        print("2. Install ML dependencies for embeddings: pip install sentence-transformers")
        print("3. Add transformer models for improved sentiment accuracy")
        print("4. Measure correlation with actual stock price movements")
        print("5. Optimize based on trading performance metrics")
        
    except Exception as e:
        print(f"\nDemo failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
