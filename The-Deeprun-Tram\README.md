# The Deeprun Tram

## Core Engineering for a Multi-Strategy Quantitative System Targeting Structural Alpha in the Saudi Arabian Markets

---

## Core Philosophy: The Tram, The Gnomes, The Dwarves, and The Humans

This repository is the digital manifestation of the **Deeprun Tram**—a marvel of engineering connecting disparate kingdoms to create a unified, powerful whole. Our investment philosophy is built upon this metaphor:

### The Deeprun Tram (*The System*)
The core infrastructure. This repository contains the low-latency data pipelines, the rigorous backtesting engine, and the machine learning workflows that form the bedrock of our system. It is the cutting-edge engineering that connects all strategies.

### The Gnomes of Gnomeregan (*The Technology*)
The quants, ML engineers, and high-frequency thinkers. This represents our commitment to cutting-edge technology, leveraging GCP/Vertex AI, advanced statistical models, and a scientific, evidence-based approach to every problem.

### The Dwarves of Ironforge (*The Macro View*)
The macro strategists. They understand the "raw materials" of the market—oil prices, interest rate cycles, geopolitical shifts, and capital flows. This represents our top-down, macro-aware strategies, such as fixed-income arbitrage.

### The Humans of Stormwind (*The Fundamentals*)
The fundamental analysts. They understand the intrinsic value of individual "kingdoms" (companies), their leadership, their economic moats, and their long-term prospects. This represents our bottom-up, factor-based equity strategies.

By seamlessly connecting these three disciplines, the Tram aims to generate persistent, uncorrelated alpha.

---

## Strategic Objective

The primary objective of this project is to **research, develop, and deploy a systematic, multi-strategy quantitative fund focused on the Saudi market**. The system is designed to exploit identifiable market inefficiencies, particularly those arising from retail dominance and structural constraints.

---

## Key Research Hypotheses

Our current research efforts are focused on two primary fronts, reflecting our core philosophy:

- **[H1] Equity-Factor Mispricing (*The Work of Stormwind*):**  
  A bottom-up approach to identify mispriced equities by building a proprietary factor library. This includes classic factors (Value, Momentum, Quality) augmented with alternative data signals, such as NLP-derived sentiment from local social media.

- **[H3] Sukuk Spread & Relative Value Arbitrage (*The Work of Ironforge*):**  
  A top-down, macro-driven strategy to exploit pricing anomalies in the Saudi fixed-income market. This involves modeling the SAR yield curve and identifying relative value opportunities against both USD-denominated debt and other local assets.

---

## Gnomish Engineering: The Technology Stack

- **Cloud Platform:** Google Cloud Platform (GCP)
- **ML Workbench:** Vertex AI Notebooks & Pipelines
- **Primary Language:** Python 3.x
- **Core Libraries:** Pandas, NumPy, SciPy, Scikit-Learn, Statsmodels
- **ML/DL Frameworks:** TensorFlow, PyTorch
- **Data Sources:** Tadawul eReference Data, DirecFN Pro Terminal, scraped alternative data
- **Version Control:** Git / GitHub

---

## Repository Structure

This repository follows a standardized structure for quantitative research projects:

