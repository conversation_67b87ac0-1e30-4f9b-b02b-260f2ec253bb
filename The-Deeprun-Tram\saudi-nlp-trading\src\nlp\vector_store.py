"""
Vector Database Integration for Arabic Financial Content

High-performance vector storage and retrieval system using Qdrant for semantic search,
similarity analysis, and content recommendation in Saudi financial social media data.
"""

import numpy as np
from typing import List, Dict, Optional, Union, Tuple, Any
from dataclasses import dataclass, asdict
import uuid
from datetime import datetime
import json

try:
    from qdrant_client import QdrantClient
    from qdrant_client.models import (
        Distance, VectorParams, PointStruct, Filter, FieldCondition,
        MatchValue, MatchAny, Range, GeoBoundingBox, PayloadSchemaType,
        CreateCollection, UpdateCollection, SearchRequest, CountRequest
    )
    from qdrant_client.http.exceptions import UnexpectedResponse
    QDRANT_AVAILABLE = True
except ImportError:
    print("Warning: Qdrant client not available. Install with: pip install qdrant-client")
    # Create dummy classes for type hints
    QdrantClient = None
    Distance = VectorParams = PointStruct = Filter = FieldCondition = None
    MatchValue = MatchAny = Range = GeoBoundingBox = PayloadSchemaType = None
    CreateCollection = UpdateCollection = SearchRequest = CountRequest = None
    UnexpectedResponse = Exception
    QDRANT_AVAILABLE = False

@dataclass
class SearchResult:
    """Container for search results"""
    id: str
    score: float
    payload: Dict
    vector: Optional[np.ndarray] = None


@dataclass
class VectorDocument:
    """Container for vector document"""
    id: str
    content: str
    embedding: np.ndarray
    metadata: Dict
    timestamp: datetime


class FinancialVectorStore:
    """
    Advanced vector database for Arabic financial content
    
    Features:
    - Semantic search with hybrid scoring
    - Temporal filtering and trending analysis
    - Company/ticker-based retrieval
    - Sentiment-aware search
    - Real-time updates and streaming
    """
    
    def __init__(self, 
                 collection_name: str = "saudi_financial_posts",
                 host: str = "localhost",
                 port: int = 6333,
                 api_key: Optional[str] = None,
                 url: Optional[str] = None):
        
        self.collection_name = collection_name
        
        # Initialize Qdrant client
        if url:
            # Cloud/remote instance
            self.client = QdrantClient(url=url, api_key=api_key)
        else:
            # Local instance
            self.client = QdrantClient(host=host, port=port)
        
        # Test connection
        try:
            collections = self.client.get_collections()
            print(f"Connected to Qdrant. Found {len(collections.collections)} collections.")
        except Exception as e:
            print(f"Warning: Could not connect to Qdrant: {e}")
            self.client = None
            return
        
        # Setup collection
        self.vector_size = None  # Will be set when first documents are added
        self._setup_collection()
    
    def _setup_collection(self, vector_size: int = 768):
        """Create or update collection configuration"""
        if not self.client:
            return
        
        try:
            # Check if collection exists
            collections = self.client.get_collections().collections
            collection_names = [c.name for c in collections]
            
            if self.collection_name not in collection_names:
                # Create new collection
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(
                        size=vector_size,
                        distance=Distance.COSINE
                    )
                )
                print(f"Created collection: {self.collection_name}")
                
                # Create payload indexes for efficient filtering
                self._create_payload_indexes()
            else:
                print(f"Collection {self.collection_name} already exists")
                
            self.vector_size = vector_size
            
        except Exception as e:
            print(f"Error setting up collection: {e}")
    
    def _create_payload_indexes(self):
        """Create indexes on payload fields for efficient filtering"""
        if not self.client:
            return
        
        # Index commonly filtered fields
        index_fields = [
            ("timestamp", PayloadSchemaType.DATETIME),
            ("source", PayloadSchemaType.KEYWORD),
            ("sentiment", PayloadSchemaType.KEYWORD),
            ("tickers", PayloadSchemaType.KEYWORD),
            ("companies", PayloadSchemaType.KEYWORD),
            ("author", PayloadSchemaType.KEYWORD),
            ("post_id", PayloadSchemaType.KEYWORD)
        ]
        
        for field_name, field_type in index_fields:
            try:
                self.client.create_payload_index(
                    collection_name=self.collection_name,
                    field_name=field_name,
                    field_schema=field_type
                )
                print(f"Created index on field: {field_name}")
            except Exception as e:
                # Index might already exist
                pass
    
    def add_documents(self, 
                     documents: List[Dict],
                     embeddings: np.ndarray,
                     batch_size: int = 100) -> bool:
        """
        Add documents with embeddings to the vector store
        
        Args:
            documents: List of document dictionaries
            embeddings: Corresponding embeddings array
            batch_size: Batch size for insertion
            
        Returns:
            Success status
        """
        if not self.client:
            print("Error: Qdrant client not available")
            return False
        
        if len(documents) != len(embeddings):
            print("Error: Number of documents and embeddings must match")
            return False
        
        # Setup collection with correct vector size if needed
        if self.vector_size is None:
            self.vector_size = embeddings.shape[1]
            self._setup_collection(self.vector_size)
        
        # Prepare points for insertion
        points = []
        for i, (doc, embedding) in enumerate(zip(documents, embeddings)):
            
            # Generate unique ID if not provided
            point_id = doc.get('id', str(uuid.uuid4()))
            
            # Prepare payload with all metadata
            payload = {
                'content': doc.get('content', ''),
                'post_id': doc.get('post_id'),
                'author': doc.get('author'),
                'timestamp': doc.get('timestamp'),
                'source': doc.get('source', 'unknown'),
                'url': doc.get('url'),
                'sentiment': doc.get('sentiment'),
                'sentiment_score': doc.get('sentiment_score'),
                'entities': doc.get('entities', {}),
                'tickers': doc.get('tickers', []),
                'companies': doc.get('companies', []),
                'financial_terms': doc.get('financial_terms', []),
                'indicators': doc.get('indicators', []),
                'metadata': doc.get('metadata', {})
            }
            
            # Remove None values
            payload = {k: v for k, v in payload.items() if v is not None}
            
            point = PointStruct(
                id=point_id,
                vector=embedding.tolist(),
                payload=payload
            )
            points.append(point)
            
            # Insert in batches
            if len(points) >= batch_size:
                try:
                    self.client.upsert(
                        collection_name=self.collection_name,
                        points=points
                    )
                    points = []
                except Exception as e:
                    print(f"Error inserting batch: {e}")
                    return False
        
        # Insert remaining points
        if points:
            try:
                self.client.upsert(
                    collection_name=self.collection_name,
                    points=points
                )
            except Exception as e:
                print(f"Error inserting final batch: {e}")
                return False
        
        print(f"Successfully added {len(documents)} documents to vector store")
        return True
    
    def search_semantic(self, 
                       query_embedding: np.ndarray,
                       limit: int = 10,
                       score_threshold: float = 0.0,
                       filters: Optional[Dict] = None) -> List[SearchResult]:
        """
        Semantic search using query embedding
        
        Args:
            query_embedding: Query vector
            limit: Maximum number of results
            score_threshold: Minimum similarity score
            filters: Optional filters for metadata
            
        Returns:
            List of search results
        """
        if not self.client:
            return []
        
        # Build filter conditions
        filter_conditions = self._build_filter(filters) if filters else None
        
        try:
            search_result = self.client.search(
                collection_name=self.collection_name,
                query_vector=query_embedding.tolist(),
                query_filter=filter_conditions,
                limit=limit,
                score_threshold=score_threshold,
                with_payload=True,
                with_vectors=False
            )
            
            results = []
            for hit in search_result:
                result = SearchResult(
                    id=str(hit.id),
                    score=hit.score,
                    payload=hit.payload
                )
                results.append(result)
            
            return results
            
        except Exception as e:
            print(f"Error in semantic search: {e}")
            return []
    
    def search_by_ticker(self, 
                        ticker: str,
                        start_date: Optional[str] = None,
                        end_date: Optional[str] = None,
                        sentiment: Optional[str] = None,
                        limit: int = 100) -> List[SearchResult]:
        """
        Search posts by ticker with optional filters
        
        Args:
            ticker: Stock ticker (e.g., '1120')
            start_date: Start date filter (ISO format)
            end_date: End date filter (ISO format)
            sentiment: Sentiment filter ('bullish', 'bearish', 'neutral')
            limit: Maximum results
            
        Returns:
            List of search results
        """
        filters = {
            'tickers': ticker
        }
        
        if sentiment:
            filters['sentiment'] = sentiment
        
        if start_date and end_date:
            filters['timestamp_range'] = {'gte': start_date, 'lte': end_date}
        
        return self._search_with_filters(filters, limit)
    
    def search_by_company(self, 
                         company: str,
                         sentiment: Optional[str] = None,
                         limit: int = 100) -> List[SearchResult]:
        """Search posts mentioning specific company"""
        filters = {
            'companies': company
        }
        
        if sentiment:
            filters['sentiment'] = sentiment
        
        return self._search_with_filters(filters, limit)
    
    def get_trending_topics(self, 
                           time_window: str = "24h",
                           min_mentions: int = 5) -> List[Dict]:
        """
        Get trending topics/entities in specified time window
        
        Args:
            time_window: Time window ('1h', '24h', '7d')
            min_mentions: Minimum mentions to be considered trending
            
        Returns:
            List of trending topics with counts
        """
        if not self.client:
            return []
        
        # Calculate time range
        from datetime import datetime, timedelta
        
        now = datetime.now()
        if time_window == "1h":
            start_time = now - timedelta(hours=1)
        elif time_window == "24h":
            start_time = now - timedelta(days=1)
        elif time_window == "7d":
            start_time = now - timedelta(days=7)
        else:
            start_time = now - timedelta(days=1)
        
        # This would require aggregation queries - simplified version
        # In practice, you'd use Qdrant's aggregation features or external processing
        
        filters = {
            'timestamp_range': {
                'gte': start_time.isoformat(),
                'lte': now.isoformat()
            }
        }
        
        results = self._search_with_filters(filters, limit=1000)
        
        # Count entity mentions
        entity_counts = {}
        for result in results:
            payload = result.payload
            
            # Count tickers
            for ticker in payload.get('tickers', []):
                entity_counts[f"ticker:{ticker}"] = entity_counts.get(f"ticker:{ticker}", 0) + 1
            
            # Count companies
            for company in payload.get('companies', []):
                entity_counts[f"company:{company}"] = entity_counts.get(f"company:{company}", 0) + 1
        
        # Filter by minimum mentions and sort
        trending = [
            {'entity': entity, 'count': count, 'type': entity.split(':')[0]}
            for entity, count in entity_counts.items()
            if count >= min_mentions
        ]
        
        trending.sort(key=lambda x: x['count'], reverse=True)
        
        return trending[:20]  # Top 20 trending
    
    def _build_filter(self, filters: Dict):
        """Build Qdrant filter from filter dictionary"""
        if not QDRANT_AVAILABLE:
            return None

        conditions = []

        for key, value in filters.items():
            if key == 'timestamp_range':
                # Date range filter
                conditions.append(
                    FieldCondition(
                        key="timestamp",
                        range=Range(
                            gte=value.get('gte'),
                            lte=value.get('lte')
                        )
                    )
                )
            elif isinstance(value, list):
                # Multiple values (OR condition)
                conditions.append(
                    FieldCondition(
                        key=key,
                        match=MatchAny(any=value)
                    )
                )
            else:
                # Single value
                conditions.append(
                    FieldCondition(
                        key=key,
                        match=MatchValue(value=value)
                    )
                )
        
        return Filter(must=conditions) if conditions else None
    
    def _search_with_filters(self, filters: Dict, limit: int = 100) -> List[SearchResult]:
        """Internal method for filtered search"""
        if not self.client:
            return []
        
        filter_conditions = self._build_filter(filters)
        
        try:
            # Use scroll for large result sets
            results, _ = self.client.scroll(
                collection_name=self.collection_name,
                scroll_filter=filter_conditions,
                limit=limit,
                with_payload=True,
                with_vectors=False
            )
            
            search_results = []
            for point in results:
                result = SearchResult(
                    id=str(point.id),
                    score=1.0,  # No similarity score for filtered search
                    payload=point.payload
                )
                search_results.append(result)
            
            return search_results
            
        except Exception as e:
            print(f"Error in filtered search: {e}")
            return []
    
    def get_collection_stats(self) -> Dict:
        """Get collection statistics"""
        if not self.client:
            return {}
        
        try:
            info = self.client.get_collection(self.collection_name)
            
            # Get count
            count_result = self.client.count(
                collection_name=self.collection_name
            )
            
            return {
                'total_documents': count_result.count,
                'vector_size': info.config.params.vectors.size,
                'distance_metric': info.config.params.vectors.distance,
                'status': info.status,
                'indexed_vectors': info.vectors_count,
                'points_count': info.points_count
            }
            
        except Exception as e:
            print(f"Error getting collection stats: {e}")
            return {}
    
    def delete_documents(self, document_ids: List[str]) -> bool:
        """Delete documents by IDs"""
        if not self.client:
            return False
        
        try:
            self.client.delete(
                collection_name=self.collection_name,
                points_selector=document_ids
            )
            print(f"Deleted {len(document_ids)} documents")
            return True
            
        except Exception as e:
            print(f"Error deleting documents: {e}")
            return False
    
    def update_document(self, document_id: str, payload: Dict) -> bool:
        """Update document payload"""
        if not self.client:
            return False
        
        try:
            self.client.set_payload(
                collection_name=self.collection_name,
                payload=payload,
                points=[document_id]
            )
            return True
            
        except Exception as e:
            print(f"Error updating document: {e}")
            return False
