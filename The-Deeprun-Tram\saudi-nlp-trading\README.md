# Saudi NLP Trading System

This repository contains a complete implementation of a natural‑language processing (NLP)‑driven trading intelligence system targeting the Saudi stock market (Tadawul).  The project scrapes Arabic discourse from the Hawamer.com forums, cleans and normalises the text for Saudi dialects, embeds posts using dialect‑aware models (e.g., Swan), performs topic modelling and sentiment analysis, and generates market features that can be coupled with volatility models.

## Structure

The project is organised into several top‑level directories:

* `docs/` — architecture descriptions, compliance checklist, literature review and implementation logs.
* `config/` — configuration files for the scraper and NLP components.
* `src/` — Python modules implementing scraping, preprocessing, embedding, analytics, pipelines and utilities.
* `tests/` — pytest suites to verify core functionality.
* `notebooks/` — interactive Jupyter notebooks for experimentation.
* `data/` — locations to store raw, processed and embedded data as well as models.

See the individual module documentation for details.

## Quick Start

Install Python ≥ 3.9 and run the setup script:

```bash
python setup.py
```

The script will create necessary directories, install dependencies from `requirements.txt`, download baseline NLP models, and execute the test suite.  After setup you can execute the main pipeline by specifying a list of Hawamer thread URLs:

```bash
python -m src.pipeline.orchestrator --urls hawamer_urls.txt --mode test
```

For production runs, adjust the configuration in `config/scraper_config.yaml` and `config/nlp_config.yaml` and set the appropriate environment variables in `.env`.

## Legal and Ethical Considerations

This project is designed for research purposes.  All scraping activities must comply with the Saudi Personal Data Protection Law (PDPL) and Hawamer’s terms of service.  Personally identifiable information (PII) is excluded by hashing usernames and removing any profile data.  Data retention policies limit how long scraped content is stored.  See `docs/compliance_checklist.md` for full details.

## License

This repository is provided for educational and research use.  Ensure you have the right to scrape and process the data and abide by all applicable regulations when deploying this system.
