"""
Advanced Pipeline Orchestrator for Saudi Financial NLP Trading System

This module coordinates the complete pipeline using the modern Arabic NLP components:
- Web scraping (Ha<PERSON>mer, Twitter, Telegram)
- Advanced Arabic preprocessing with CAMeL Tools
- Hybrid embeddings (AraBERT + BM25)
- Financial NER with Saudi company gazetteers
- Ensemble sentiment analysis
- Vector database integration
- Feature engineering for trading signals
"""

import asyncio
import json
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Union
import argparse

import pandas as pd
import numpy as np
from loguru import logger

from ..scrapers.hawamer_scraper import HawamerScraper
from ..nlp.pipeline import ArabicFinancialNLPPipeline, PipelineResult


class AdvancedTradingPipeline:
    """
    Advanced pipeline orchestrator using state-of-the-art Arabic NLP
    
    Features:
    - Modern Arabic preprocessing with dialect normalization
    - Hybrid embedding system (dense + sparse)
    - Financial entity recognition with Saudi gazetteers
    - Multi-model sentiment ensemble
    - Vector database for semantic search
    - Advanced feature engineering for trading signals
    """
    
    def __init__(self, 
                 headful: bool = False,
                 nlp_config_path: Optional[str] = None,
                 vector_store_config: Optional[Dict] = None,
                 output_dir: str = "data/processed"):
        
        self.headful = headful
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize scraper
        self.scraper = HawamerScraper()
        
        # Initialize advanced NLP pipeline
        self.nlp_pipeline = ArabicFinancialNLPPipeline(
            config_path=nlp_config_path or "config/nlp_config.json",
            cache_dir="cache/nlp",
            vector_store_config=vector_store_config
        )
        
        # Setup logging
        self._setup_logging()
        
        logger.info("Advanced Trading Pipeline initialized")
        logger.info(f"Output directory: {self.output_dir}")
    
    def _setup_logging(self):
        """Configure comprehensive logging"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Main pipeline log
        logger.add(
            log_dir / f"advanced_pipeline_{timestamp}.log",
            rotation="50 MB",
            retention="30 days",
            level="INFO",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
        )
        
        # Performance log
        logger.add(
            log_dir / f"performance_{timestamp}.log",
            rotation="10 MB",
            retention="7 days",
            level="DEBUG",
            filter=lambda record: "performance" in record["message"].lower()
        )
    
    async def scrape_data(self, target_urls: List[str]) -> List[Dict]:
        """
        Scrape data from target URLs with enhanced error handling
        
        Args:
            target_urls: List of URLs to scrape
            
        Returns:
            List of scraped thread data
        """
        logger.info(f"Starting data scraping for {len(target_urls)} URLs")
        
        await self.scraper.initialize_browser(headless=not self.headful)
        
        scraped_data = []
        successful_scrapes = 0
        
        for i, url in enumerate(target_urls):
            try:
                logger.info(f"Scraping URL {i+1}/{len(target_urls)}: {url}")
                
                thread_data = await self.scraper.scrape_thread(url)
                
                if thread_data and thread_data.get('posts'):
                    scraped_data.append(thread_data)
                    successful_scrapes += 1
                    logger.info(f"Successfully scraped {len(thread_data['posts'])} posts from {url}")
                else:
                    logger.warning(f"No posts found for URL: {url}")
                
                # Rate limiting
                if i < len(target_urls) - 1:
                    await asyncio.sleep(np.random.uniform(3, 7))
                    
            except Exception as e:
                logger.error(f"Failed to scrape {url}: {e}")
                continue
        
        await self.scraper.close_browser()
        
        logger.info(f"Scraping completed: {successful_scrapes}/{len(target_urls)} URLs successful")
        
        return scraped_data
    
    def prepare_posts_for_nlp(self, scraped_data: List[Dict]) -> List[Dict]:
        """
        Convert scraped data to format expected by NLP pipeline
        
        Args:
            scraped_data: Raw scraped thread data
            
        Returns:
            List of post dictionaries ready for NLP processing
        """
        posts = []
        
        for thread in scraped_data:
            thread_url = thread.get('url', '')
            thread_title = thread.get('title', '')
            
            for post in thread.get('posts', []):
                # Skip empty posts
                content = post.get('content', '').strip()
                if not content or len(content) < 10:
                    continue
                
                post_dict = {
                    'post_id': post.get('post_id'),
                    'content': content,
                    'author': post.get('author', 'unknown'),
                    'timestamp': post.get('timestamp'),
                    'likes': post.get('likes', 0),
                    'shares': post.get('shares', 0),
                    'source': 'hawamer',
                    'url': thread_url,
                    'thread_title': thread_title,
                    'metadata': {
                        'thread_url': thread_url,
                        'scraped_at': datetime.now().isoformat()
                    }
                }
                
                posts.append(post_dict)
        
        logger.info(f"Prepared {len(posts)} posts for NLP processing")
        
        return posts
    
    def save_results(self, 
                    pipeline_result: PipelineResult,
                    scraped_data: List[Dict],
                    mode: str = "full") -> Dict[str, str]:
        """
        Save pipeline results to various formats
        
        Args:
            pipeline_result: Complete NLP pipeline results
            scraped_data: Original scraped data
            mode: Save mode ('full', 'features_only', 'summary')
            
        Returns:
            Dictionary of saved file paths
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        saved_files = {}
        
        try:
            # Save raw scraped data
            raw_data_file = self.output_dir / f"raw_scraped_data_{timestamp}.json"
            with open(raw_data_file, 'w', encoding='utf-8') as f:
                json.dump(scraped_data, f, ensure_ascii=False, indent=2, default=str)
            saved_files['raw_data'] = str(raw_data_file)
            
            # Save processed posts
            if not pipeline_result.processed_posts.empty:
                processed_file = self.output_dir / f"processed_posts_{timestamp}.parquet"
                pipeline_result.processed_posts.to_parquet(processed_file, compression='gzip')
                saved_files['processed_posts'] = str(processed_file)
                
                # Also save as JSON for readability
                json_file = self.output_dir / f"processed_posts_{timestamp}.json"
                pipeline_result.processed_posts.to_json(
                    json_file, orient='records', force_ascii=False, indent=2
                )
                saved_files['processed_posts_json'] = str(json_file)
            
            # Save embeddings
            if len(pipeline_result.embeddings) > 0:
                embeddings_file = self.output_dir / f"embeddings_{timestamp}.npy"
                np.save(embeddings_file, pipeline_result.embeddings)
                saved_files['embeddings'] = str(embeddings_file)
            
            # Save features
            if not pipeline_result.features.features.empty:
                features_file = self.output_dir / f"trading_features_{timestamp}.parquet"
                pipeline_result.features.features.to_parquet(features_file, compression='gzip')
                saved_files['features'] = str(features_file)
                
                # Save feature metadata
                feature_meta_file = self.output_dir / f"feature_metadata_{timestamp}.json"
                with open(feature_meta_file, 'w', encoding='utf-8') as f:
                    json.dump(pipeline_result.features.metadata, f, indent=2, default=str)
                saved_files['feature_metadata'] = str(feature_meta_file)
            
            # Save comprehensive metadata
            metadata_file = self.output_dir / f"pipeline_metadata_{timestamp}.json"
            comprehensive_metadata = {
                'pipeline_metadata': pipeline_result.metadata,
                'performance_metrics': pipeline_result.performance_metrics,
                'nlp_performance': self.nlp_pipeline.get_performance_summary(),
                'saved_files': saved_files,
                'timestamp': timestamp
            }
            
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(comprehensive_metadata, f, indent=2, default=str)
            saved_files['metadata'] = str(metadata_file)
            
            # Create summary report
            summary_file = self.output_dir / f"pipeline_summary_{timestamp}.txt"
            self._create_summary_report(pipeline_result, summary_file)
            saved_files['summary'] = str(summary_file)
            
            logger.info(f"Results saved to {len(saved_files)} files in {self.output_dir}")
            
        except Exception as e:
            logger.error(f"Error saving results: {e}")
            
        return saved_files
    
    def _create_summary_report(self, result: PipelineResult, output_file: Path):
        """Create a human-readable summary report"""
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("SAUDI FINANCIAL NLP TRADING PIPELINE - EXECUTION SUMMARY\n")
            f.write("=" * 80 + "\n\n")
            
            f.write(f"Execution Time: {result.metadata.get('timestamp', 'Unknown')}\n")
            f.write(f"Total Processing Time: {result.metadata.get('total_processing_time', 0):.2f} seconds\n\n")
            
            # Data Processing Summary
            f.write("DATA PROCESSING SUMMARY:\n")
            f.write("-" * 40 + "\n")
            f.write(f"Posts Input: {result.metadata.get('total_posts_input', 0)}\n")
            f.write(f"Posts Processed: {result.metadata.get('total_posts_processed', 0)}\n")
            f.write(f"Success Rate: {result.metadata.get('processing_success_rate', 0):.2%}\n")
            f.write(f"Embeddings Generated: {result.metadata.get('embeddings_shape', (0, 0))[0]}\n")
            f.write(f"Features Engineered: {result.metadata.get('features_generated', 0)}\n\n")
            
            # Feature Summary
            if result.features.metadata:
                f.write("FEATURE ENGINEERING SUMMARY:\n")
                f.write("-" * 40 + "\n")
                f.write(f"Total Features: {result.features.metadata.get('total_features', 0)}\n")
                f.write(f"Unique Tickers: {result.features.metadata.get('unique_tickers', 0)}\n")
                
                date_range = result.features.metadata.get('date_range', {})
                if date_range.get('start') and date_range.get('end'):
                    f.write(f"Date Range: {date_range['start']} to {date_range['end']}\n")
                
                feature_cats = result.features.metadata.get('feature_categories', {})
                if feature_cats:
                    f.write("\nFeature Categories:\n")
                    for category, count in feature_cats.items():
                        f.write(f"  {category.title()}: {count}\n")
                f.write("\n")
            
            # Performance Metrics
            f.write("PERFORMANCE METRICS:\n")
            f.write("-" * 40 + "\n")
            perf_times = result.performance_metrics.get('processing_times', {})
            for stage, time_taken in perf_times.items():
                f.write(f"{stage.replace('_', ' ').title()}: {time_taken:.2f}s\n")
            
            error_counts = result.performance_metrics.get('error_counts', {})
            if error_counts:
                f.write("\nError Counts:\n")
                for error_type, count in error_counts.items():
                    f.write(f"  {error_type.replace('_', ' ').title()}: {count}\n")
            
            f.write("\n" + "=" * 80 + "\n")
    
    async def run_full_pipeline(self, 
                               target_urls: List[str],
                               mode: str = "full") -> PipelineResult:
        """
        Execute the complete advanced pipeline
        
        Args:
            target_urls: URLs to scrape
            mode: Processing mode ('full', 'nlp_only', 'features_only')
            
        Returns:
            Complete pipeline results
        """
        pipeline_start = datetime.now()
        
        logger.info(f"Starting advanced pipeline in '{mode}' mode")
        logger.info(f"Target URLs: {len(target_urls)}")
        
        try:
            # Phase 1: Data Scraping
            if mode in ['full', 'nlp_only']:
                scraped_data = await self.scrape_data(target_urls)
                
                if not scraped_data:
                    logger.error("No data scraped successfully")
                    return PipelineResult(
                        processed_posts=pd.DataFrame(),
                        embeddings=np.array([]),
                        features=None,
                        vector_store_ids=[],
                        metadata={'error': 'No data scraped'},
                        performance_metrics={}
                    )
                
                # Prepare posts for NLP
                posts = self.prepare_posts_for_nlp(scraped_data)
                
                if not posts:
                    logger.error("No valid posts found after preparation")
                    return PipelineResult(
                        processed_posts=pd.DataFrame(),
                        embeddings=np.array([]),
                        features=None,
                        vector_store_ids=[],
                        metadata={'error': 'No valid posts'},
                        performance_metrics={}
                    )
            else:
                # Load existing data for features_only mode
                scraped_data = []
                posts = []
            
            # Phase 2: Advanced NLP Processing
            logger.info("Phase 2: Advanced Arabic NLP processing")
            pipeline_result = self.nlp_pipeline.run_pipeline(posts)
            
            # Phase 3: Save Results
            logger.info("Phase 3: Saving results")
            saved_files = self.save_results(pipeline_result, scraped_data, mode)
            
            # Update metadata with file paths
            pipeline_result.metadata['saved_files'] = saved_files
            pipeline_result.metadata['total_pipeline_time'] = (
                datetime.now() - pipeline_start
            ).total_seconds()
            
            logger.info(f"Advanced pipeline completed successfully in {pipeline_result.metadata['total_pipeline_time']:.2f} seconds")
            
            return pipeline_result
            
        except Exception as e:
            logger.error(f"Pipeline failed: {e}")
            raise


def main():
    """Main entry point for the advanced pipeline"""
    parser = argparse.ArgumentParser(description="Advanced Saudi Financial NLP Trading Pipeline")
    parser.add_argument("--urls", required=True, help="File containing URLs to scrape")
    parser.add_argument("--mode", choices=["full", "nlp_only", "features_only"], 
                       default="full", help="Pipeline execution mode")
    parser.add_argument("--headful", action="store_true", help="Run browser in headful mode")
    parser.add_argument("--config", help="Path to NLP configuration file")
    parser.add_argument("--output", default="data/processed", help="Output directory")
    parser.add_argument("--vector-store", help="Vector store configuration (JSON)")
    
    args = parser.parse_args()
    
    # Load URLs
    try:
        with open(args.urls, 'r', encoding='utf-8') as f:
            urls = [line.strip() for line in f if line.strip()]
    except FileNotFoundError:
        logger.error(f"URL file not found: {args.urls}")
        sys.exit(1)
    
    # Parse vector store config
    vector_store_config = None
    if args.vector_store:
        try:
            with open(args.vector_store, 'r') as f:
                vector_store_config = json.load(f)
        except Exception as e:
            logger.warning(f"Could not load vector store config: {e}")
    
    # Initialize and run pipeline
    pipeline = AdvancedTradingPipeline(
        headful=args.headful,
        nlp_config_path=args.config,
        vector_store_config=vector_store_config,
        output_dir=args.output
    )
    
    # Run pipeline
    try:
        result = asyncio.run(pipeline.run_full_pipeline(urls, args.mode))
        
        print("\n" + "="*60)
        print("PIPELINE EXECUTION COMPLETED SUCCESSFULLY")
        print("="*60)
        print(f"Posts processed: {len(result.processed_posts)}")
        print(f"Features generated: {len(result.features.feature_names) if result.features else 0}")
        print(f"Total time: {result.metadata.get('total_pipeline_time', 0):.2f} seconds")
        print(f"Output directory: {args.output}")
        print("="*60)
        
    except KeyboardInterrupt:
        logger.info("Pipeline interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Pipeline failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
