"""
Base scraper class defining the common interface for forum scrapers.
"""

from abc import ABC, abstractmethod
from typing import Dict


class BaseScraper(ABC):
    """
    Abstract base class that all scrapers should inherit from.
    Provides the contract for initialising browsers and scraping URLs.
    """

    @abstractmethod
    async def initialize_browser(self):
        """
        Initialise the browser or HTTP client used for scraping.
        """
        raise NotImplementedError

    @abstractmethod
    async def scrape_thread(self, thread_url: str) -> Dict:
        """
        Scrape the contents of a single forum thread and return structured data.
        """
        raise NotImplementedError
