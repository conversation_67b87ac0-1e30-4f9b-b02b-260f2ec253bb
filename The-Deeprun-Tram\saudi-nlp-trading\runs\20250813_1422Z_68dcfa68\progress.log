{"timestamp":"2025-08-13T14:22:37.746044+03:00","phase":"INIT","event":"Run structure created","data":{"run_id":"68dcfa68","started_at":"2025-08-13T14:22:37.746044+03:00","seed":1337,"limiter":{"refill_rate_rps":0.5,"burst":5},"targets":{"threads":3,"est_pages":5},"env":{"python":"3.12","os":"windows","agent_version":"1.1"},"schema_version":"1.1"}}
{"timestamp":"2025-08-13T14:22:37.748554+03:00","phase":"RUN_0","event":"STARTING","data":null}
{"timestamp":"2025-08-13T14:22:37.794047+03:00","phase":"RUN_0","event":"COMPLETED","data":null}
{"timestamp":"2025-08-13T14:22:37.794047+03:00","phase":"RUN_1","event":"STARTING","data":null}
{"timestamp":"2025-08-13T14:22:49.065794+03:00","phase":"RUN_1","event":"COMPLETED","data":null}
{"timestamp":"2025-08-13T14:22:49.065794+03:00","phase":"RUN_2","event":"STARTING","data":null}
{"timestamp":"2025-08-13T14:22:49.066794+03:00","phase":"RUN_2","event":"COMPLETED","data":null}
{"timestamp":"2025-08-13T14:22:49.066794+03:00","phase":"RUN_3","event":"STARTING","data":null}
{"timestamp":"2025-08-13T14:22:49.069794+03:00","phase":"RUN_3","event":"COMPLETED","data":null}
{"timestamp":"2025-08-13T14:22:49.069794+03:00","phase":"RUN_4","event":"STARTING","data":null}
{"timestamp":"2025-08-13T14:22:49.100487+03:00","phase":"RUN_4","event":"COMPLETED","data":null}
{"timestamp":"2025-08-13T14:22:49.101488+03:00","phase":"RUN_5","event":"STARTING","data":null}
{"timestamp":"2025-08-13T14:37:49.373963+03:00","phase":"RUN_5","event":"FAILED","data":{"abort_reason":"429 rate too high: 0.9%"}}
