"""
Advanced Arabic Text Preprocessing Pipeline for Saudi Financial NLP

This module provides comprehensive Arabic text preprocessing specifically designed
for Saudi financial social media content, handling dialectal variations, 
financial terminology, and market-specific entities.
"""

import re
import unicodedata
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import json

try:
    import pyarabic.araby as araby
    from camel_tools.utils.normalize import normalize_unicode, normalize_alef_maksura_ar
    from camel_tools.utils.dediac import dediac_ar
    from camel_tools.tokenizers.word import simple_word_tokenize
    from camel_tools.morphology.database import MorphologyDB
    from camel_tools.morphology.analyzer import Analyzer
except ImportError:
    print("Warning: CAMeL Tools not available. Install with: pip install camel-tools")
    araby = None

@dataclass
class ProcessingResult:
    """Container for preprocessing results"""
    original_text: str
    clean_text: str
    normalized_text: str
    tokens: List[str]
    entities: Dict[str, List[str]]
    financial_terms: List[str]
    sentiment_indicators: List[str]
    metadata: Dict


class ArabicFinancialPreprocessor:
    """
    Advanced Arabic preprocessor for Saudi financial content
    
    Features:
    - Unicode normalization and diacritics removal
    - Saudi dialect normalization
    - Financial entity extraction (tickers, companies, amounts)
    - Sentiment indicator detection
    - Morphological analysis with CAMeL Tools
    """
    
    def __init__(self, config_path: Optional[str] = None):
        self.config = self._load_config(config_path)
        self._initialize_resources()
        
    def _load_config(self, config_path: Optional[str]) -> Dict:
        """Load preprocessing configuration"""
        default_config = {
            "normalize_unicode": True,
            "remove_diacritics": True,
            "normalize_dialect": True,
            "extract_entities": True,
            "morphological_analysis": False,  # Expensive, enable for advanced features
            "min_token_length": 2,
            "max_token_length": 50
        }
        
        if config_path and Path(config_path).exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                default_config.update(user_config)
        
        return default_config
    
    def _initialize_resources(self):
        """Initialize linguistic resources and gazetteers"""
        
        # Saudi company ticker mapping (Top 50 most traded)
        self.ticker_mapping = {
            # Banks
            'الراجحي': '1120', 'الأهلي': '1180', 'الرياض': '1010',
            'سامبا': '1090', 'البلاد': '1140', 'الجزيرة': '4200',
            
            # Petrochemicals
            'سابك': '2010', 'ينبع': '2290', 'المتقدمة': '2330',
            'كيان': '2350', 'سبكيم': '2380',
            
            # Energy
            'أرامكو': '2222', 'الكهرباء': '5110', 'أكوا باور': '2082',
            
            # Telecom
            'الاتصالات': '7010', 'موبايلي': '7020', 'زين': '7030',
            
            # Retail
            'العثيم': '4001', 'بن داود': '4008', 'جرير': '4190',
            
            # Real Estate
            'طلعت مصطفى': '4180', 'دار الأركان': '4320', 'جبل عمر': '4250',
            
            # Healthcare
            'دله': '4004', 'المواساة': '4002', 'سليمان الحبيب': '4013',
            
            # Industrial
            'معادن': '1211', 'أسمنت العربية': '3010', 'يانساب': '2001'
        }
        
        # Reverse mapping for ticker to company
        self.company_mapping = {v: k for k, v in self.ticker_mapping.items()}
        
        # Saudi dialect normalization patterns
        self.dialect_patterns = [
            # Common Saudi expressions
            (r'\bايش\b', 'ما'),
            (r'\bوش\b', 'ما'),
            (r'\bكذا\b', 'هكذا'),
            (r'\bجذي\b', 'هكذا'),
            (r'\bمدري\b', 'لا أدري'),
            (r'\bمب\b', 'لا'),
            (r'\bماني\b', 'لست'),
            (r'\bيمكن\b', 'ربما'),
            (r'\bبس\b', 'فقط'),
            (r'\bخلاص\b', 'انتهى'),
            
            # Financial slang
            (r'\bيطير\b', 'يرتفع'),
            (r'\bينزل\b', 'ينخفض'),
            (r'\bيكسر\b', 'ينخفض بقوة'),
            (r'\bيخترق\b', 'يتجاوز'),
            (r'\bمضارب\b', 'متداول'),
            (r'\bمحفظة\b', 'استثمارات')
        ]
        
        # Financial sentiment indicators
        self.bullish_indicators = {
            'صاعد', 'ارتفاع', 'قوي', 'ممتاز', 'فرصة', 'اخضر', 'ربح', 'نمو',
            'إيجابي', 'دعم', 'اختراق', 'هدف', 'شراء', 'استثمار', 'توصية شراء',
            'مقاومة مكسورة', 'ترند صاعد', 'موجة صاعدة', 'قاع', 'انطلاق'
        }
        
        self.bearish_indicators = {
            'هابط', 'انخفاض', 'ضعيف', 'خسارة', 'تصريف', 'احمر', 'سلبي',
            'مقاومة', 'توصية بيع', 'انهيار', 'فقاعة', 'تراجع', 'هبوط',
            'ترند هابط', 'موجة هابطة', 'قمة', 'تصحيح'
        }
        
        # Initialize morphological analyzer if available
        self.morphology_analyzer = None
        if araby and self.config.get("morphological_analysis"):
            try:
                self.morphology_db = MorphologyDB.builtin_db()
                self.morphology_analyzer = Analyzer(self.morphology_db)
            except Exception as e:
                print(f"Warning: Could not initialize morphological analyzer: {e}")
    
    def normalize_unicode(self, text: str) -> str:
        """Comprehensive Unicode normalization for Arabic"""
        if not text:
            return ""
        
        # NFKC normalization
        text = unicodedata.normalize('NFKC', text)
        
        # CAMeL Tools normalization if available
        if araby:
            text = normalize_unicode(text)
            text = normalize_alef_maksura_ar(text)
        
        return text
    
    def remove_diacritics(self, text: str) -> str:
        """Remove Arabic diacritics"""
        if not text or not araby:
            return text
        
        return dediac_ar(text)
    
    def normalize_arabic_text(self, text: str) -> str:
        """Core Arabic text normalization"""
        if not text:
            return ""
        
        # Remove diacritics
        if self.config.get("remove_diacritics"):
            text = self.remove_diacritics(text)
        
        # Normalize hamza variations
        text = re.sub(r'[أإآ]', 'ا', text)
        text = re.sub(r'[ؤ]', 'و', text)
        text = re.sub(r'[ئ]', 'ي', text)
        text = re.sub(r'[ة]', 'ه', text)
        
        # Remove tatweel (elongation)
        text = re.sub(r'ـ+', '', text)
        
        # Normalize Arabic-Indic numerals
        arabic_nums = '٠١٢٣٤٥٦٧٨٩'
        western_nums = '0123456789'
        trans_table = str.maketrans(arabic_nums, western_nums)
        text = text.translate(trans_table)
        
        # Normalize whitespace
        text = ' '.join(text.split())
        
        return text
    
    def normalize_dialect(self, text: str) -> str:
        """Convert Saudi dialect to Modern Standard Arabic"""
        if not self.config.get("normalize_dialect"):
            return text
        
        for pattern, replacement in self.dialect_patterns:
            text = re.sub(pattern, replacement, text, flags=re.IGNORECASE)
        
        return text
    
    def extract_financial_entities(self, text: str) -> Dict[str, List[str]]:
        """Extract financial entities from text"""
        entities = {
            'tickers': [],
            'companies': [],
            'amounts': [],
            'percentages': [],
            'dates': []
        }
        
        if not self.config.get("extract_entities"):
            return entities
        
        # Extract percentages
        percentage_pattern = r'[+-]?\d+\.?\d*\s*%'
        entities['percentages'] = re.findall(percentage_pattern, text)
        
        # Extract monetary amounts
        amount_patterns = [
            r'\d+\.?\d*\s*(?:ريال|sar|SAR|ر\.س)',
            r'\d+\.?\d*\s*(?:مليون|مليار|ألف)',
            r'\d+\.?\d*\s*(?:million|billion|thousand)'
        ]
        
        for pattern in amount_patterns:
            entities['amounts'].extend(re.findall(pattern, text, re.IGNORECASE))
        
        # Extract known companies and tickers
        for company, ticker in self.ticker_mapping.items():
            if company in text:
                entities['companies'].append(company)
                entities['tickers'].append(ticker)
        
        # Extract 4-digit ticker patterns
        ticker_pattern = r'\b\d{4}\b'
        potential_tickers = re.findall(ticker_pattern, text)
        
        # Validate against known tickers
        valid_tickers = [t for t in potential_tickers 
                        if t in self.company_mapping]
        entities['tickers'].extend(valid_tickers)
        
        # Remove duplicates
        for key in entities:
            entities[key] = list(set(entities[key]))
        
        return entities
    
    def extract_sentiment_indicators(self, text: str) -> List[str]:
        """Extract sentiment-bearing terms"""
        indicators = []
        
        words = set(text.split())
        
        # Find bullish indicators
        bullish_found = words.intersection(self.bullish_indicators)
        indicators.extend([f"bullish:{term}" for term in bullish_found])
        
        # Find bearish indicators
        bearish_found = words.intersection(self.bearish_indicators)
        indicators.extend([f"bearish:{term}" for term in bearish_found])
        
        return indicators
    
    def clean_text(self, text: str) -> str:
        """Clean text for embedding/analysis"""
        if not text:
            return ""
        
        # Remove URLs
        text = re.sub(r'http[s]?://\S+', '', text)
        text = re.sub(r'www\.\S+', '', text)
        
        # Remove mentions but keep the name
        text = re.sub(r'@(\w+)', r'\1', text)
        
        # Remove hashtags but keep the content
        text = re.sub(r'#(\w+)', r'\1', text)
        
        # Remove forum-specific emoticons
        text = re.sub(r':hawamer\.\w+:', '', text)
        text = re.sub(r':\w+:', '', text)
        
        # Remove excessive punctuation
        text = re.sub(r'([!؟?]){2,}', r'\1', text)
        text = re.sub(r'([.]){2,}', r'\1', text)
        
        # Remove extra whitespace
        text = ' '.join(text.split())
        
        return text.strip()
    
    def tokenize(self, text: str) -> List[str]:
        """Tokenize Arabic text"""
        if not text:
            return []
        
        # Use CAMeL Tools tokenizer if available
        if araby:
            tokens = simple_word_tokenize(text)
        else:
            # Fallback to simple tokenization
            tokens = text.split()
        
        # Filter tokens by length
        min_len = self.config.get("min_token_length", 2)
        max_len = self.config.get("max_token_length", 50)
        
        filtered_tokens = [
            token for token in tokens 
            if min_len <= len(token) <= max_len
        ]
        
        return filtered_tokens
    
    def process(self, text: str) -> ProcessingResult:
        """
        Complete preprocessing pipeline
        
        Args:
            text: Raw Arabic text
            
        Returns:
            ProcessingResult with all preprocessing outputs
        """
        if not text or not text.strip():
            return ProcessingResult(
                original_text=text,
                clean_text="",
                normalized_text="",
                tokens=[],
                entities={},
                financial_terms=[],
                sentiment_indicators=[],
                metadata={"processing_steps": []}
            )
        
        processing_steps = []
        
        # Step 1: Unicode normalization
        if self.config.get("normalize_unicode"):
            text = self.normalize_unicode(text)
            processing_steps.append("unicode_normalization")
        
        # Step 2: Arabic text normalization
        normalized_text = self.normalize_arabic_text(text)
        processing_steps.append("arabic_normalization")
        
        # Step 3: Dialect normalization
        if self.config.get("normalize_dialect"):
            normalized_text = self.normalize_dialect(normalized_text)
            processing_steps.append("dialect_normalization")
        
        # Step 4: Extract entities
        entities = self.extract_financial_entities(normalized_text)
        processing_steps.append("entity_extraction")
        
        # Step 5: Extract sentiment indicators
        sentiment_indicators = self.extract_sentiment_indicators(normalized_text)
        processing_steps.append("sentiment_extraction")
        
        # Step 6: Clean for embedding
        clean_text = self.clean_text(normalized_text)
        processing_steps.append("text_cleaning")
        
        # Step 7: Tokenization
        tokens = self.tokenize(clean_text)
        processing_steps.append("tokenization")
        
        # Extract financial terms (companies + tickers)
        financial_terms = entities.get('companies', []) + entities.get('tickers', [])
        
        return ProcessingResult(
            original_text=text,
            clean_text=clean_text,
            normalized_text=normalized_text,
            tokens=tokens,
            entities=entities,
            financial_terms=financial_terms,
            sentiment_indicators=sentiment_indicators,
            metadata={
                "processing_steps": processing_steps,
                "token_count": len(tokens),
                "entity_count": sum(len(v) for v in entities.values()),
                "sentiment_indicator_count": len(sentiment_indicators)
            }
        )
