"""
Simple but Effective Sentiment Classifier

Focus: Accurate sentiment classification that correlates with price movements.
Start with lexicon + simple model, measure accuracy, improve based on results.
"""

import numpy as np
from typing import Dict, List, Tuple, Optional
import re
from collections import defaultdict
import json
from pathlib import Path

# Try to import ML libraries
try:
    from transformers import pipeline, AutoTokenizer, AutoModelForSequenceClassification
    import torch
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    print("Warning: transformers not available. Using lexicon-only sentiment.")
    TRANSFORMERS_AVAILABLE = False

class SimpleSentimentAnalyzer:
    """
    Simple sentiment analyzer optimized for Arabic financial content
    
    Strategy:
    1. Start with proven financial lexicon
    2. Add pre-trained Arabic sentiment model
    3. Combine with simple ensemble
    4. Measure correlation with actual price movements
    """
    
    def __init__(self, 
                 arabic_model: str = "aubmindlab/bert-base-arabertv2-twitter-ar-sentiment",
                 use_model: bool = True):
        
        # Financial sentiment lexicon (manually curated for Saudi market)
        self.bullish_lexicon = {
            # Strong bullish signals (weight 2.0)
            'صاعد': 2.0, 'انطلاق': 2.0, 'قفزة': 2.0, 'اختراق': 2.0,
            'ممتاز': 2.0, 'قوي جداً': 2.0, 'فرصة ذهبية': 2.0,
            
            # Moderate bullish (weight 1.5)
            'ارتفاع': 1.5, 'قوي': 1.5, 'نمو': 1.5, 'أرباح': 1.5,
            'إيجابي': 1.5, 'شراء': 1.5, 'استثمار': 1.5, 'فرصة': 1.5,
            'تحسن': 1.5, 'مكاسب': 1.5, 'دعم': 1.5,
            
            # Mild bullish (weight 1.0)
            'اخضر': 1.0, 'موجب': 1.0, 'هدف': 1.0, 'انتعاش': 1.0,
            'استقرار': 1.0, 'تطور': 1.0
        }
        
        self.bearish_lexicon = {
            # Strong bearish signals (weight -2.0)
            'انهيار': -2.5, 'كارثة': -2.0, 'فقاعة': -2.0, 'أزمة': -2.0,
            'هبوط حاد': -2.0, 'خسائر فادحة': -2.0,
            
            # Moderate bearish (weight -1.5)
            'هابط': -1.5, 'انخفاض': -1.5, 'تراجع': -1.5, 'ضعيف': -1.5,
            'خسارة': -1.5, 'بيع': -1.5, 'تصريف': -1.5, 'سلبي': -1.5,
            'هبوط': -1.5, 'ضغط': -1.5,
            
            # Mild bearish (weight -1.0)
            'احمر': -1.0, 'سالب': -1.0, 'تصحيح': -1.0, 'مقاومة': -1.0,
            'حذر': -1.0, 'تحفظ': -1.0
        }
        
        # Market context modifiers
        self.amplifiers = {
            'جداً': 1.5, 'قوي': 1.3, 'حاد': 1.4, 'كبير': 1.2,
            'ضخم': 1.4, 'عالي': 1.2, 'منخفض': 0.8
        }
        
        # Initialize transformer model
        self.model_pipeline = None
        if use_model and TRANSFORMERS_AVAILABLE:
            try:
                print(f"Loading Arabic sentiment model: {arabic_model}")
                self.model_pipeline = pipeline(
                    "sentiment-analysis",
                    model=arabic_model,
                    device=0 if torch.cuda.is_available() else -1
                )
                print("Arabic sentiment model loaded successfully")
            except Exception as e:
                print(f"Failed to load sentiment model: {e}")
        
        # Performance tracking
        self.stats = {
            'total_analyzed': 0,
            'lexicon_only': 0,
            'model_predictions': 0,
            'ensemble_predictions': 0
        }
    
    def analyze_lexicon_sentiment(self, text: str) -> Dict:
        """
        Analyze sentiment using financial lexicon
        Returns detailed breakdown for interpretability
        """
        words = text.split()
        
        # Find sentiment terms
        bullish_matches = []
        bearish_matches = []
        total_score = 0.0
        
        # Check individual words and phrases
        for i, word in enumerate(words):
            # Check single words
            if word in self.bullish_lexicon:
                score = self.bullish_lexicon[word]
                
                # Check for amplifiers
                if i > 0 and words[i-1] in self.amplifiers:
                    score *= self.amplifiers[words[i-1]]
                
                bullish_matches.append((word, score))
                total_score += score
            
            elif word in self.bearish_lexicon:
                score = self.bearish_lexicon[word]
                
                # Check for amplifiers
                if i > 0 and words[i-1] in self.amplifiers:
                    score *= self.amplifiers[words[i-1]]
                
                bearish_matches.append((word, score))
                total_score += score
        
        # Check 2-word phrases
        for i in range(len(words) - 1):
            phrase = f"{words[i]} {words[i+1]}"
            if phrase in self.bullish_lexicon:
                score = self.bullish_lexicon[phrase]
                bullish_matches.append((phrase, score))
                total_score += score
            elif phrase in self.bearish_lexicon:
                score = self.bearish_lexicon[phrase]
                bearish_matches.append((phrase, score))
                total_score += score
        
        # Calculate normalized score (0-1 scale)
        if bullish_matches or bearish_matches:
            # Normalize based on number of sentiment terms found
            term_count = len(bullish_matches) + len(bearish_matches)
            normalized_score = (total_score + 2.5) / 5.0  # Shift to 0-1 range
            normalized_score = max(0.0, min(1.0, normalized_score))
        else:
            normalized_score = 0.5  # Neutral
        
        # Determine confidence based on number of matches
        confidence = min(1.0, (len(bullish_matches) + len(bearish_matches)) * 0.3)
        
        return {
            'score': normalized_score,
            'confidence': confidence,
            'bullish_matches': bullish_matches,
            'bearish_matches': bearish_matches,
            'total_raw_score': total_score,
            'method': 'lexicon'
        }
    
    def analyze_model_sentiment(self, text: str) -> Dict:
        """
        Analyze sentiment using pre-trained model
        """
        if not self.model_pipeline:
            return {'score': 0.5, 'confidence': 0.0, 'method': 'model_unavailable'}
        
        try:
            result = self.model_pipeline(text)[0]
            
            # Convert to unified format
            if result['label'].upper() in ['POSITIVE', 'POS']:
                score = result['score']
            elif result['label'].upper() in ['NEGATIVE', 'NEG']:
                score = 1 - result['score']
            else:
                score = 0.5  # Neutral or unknown
            
            return {
                'score': score,
                'confidence': result['score'],
                'label': result['label'],
                'method': 'model'
            }
        
        except Exception as e:
            print(f"Model prediction failed: {e}")
            return {'score': 0.5, 'confidence': 0.0, 'method': 'model_error'}
    
    def analyze_sentiment(self, text: str, 
                         use_ensemble: bool = True) -> Dict:
        """
        Complete sentiment analysis with ensemble approach
        """
        if not text or len(text.strip()) < 3:
            return {
                'sentiment': 'neutral',
                'score': 0.5,
                'confidence': 0.0,
                'method': 'empty_text'
            }
        
        # Get lexicon analysis
        lexicon_result = self.analyze_lexicon_sentiment(text)
        
        # Get model analysis
        model_result = self.analyze_model_sentiment(text)
        
        # Ensemble combination
        if use_ensemble and model_result['confidence'] > 0:
            # Weight combination based on confidence
            lexicon_weight = 0.6  # Lexicon is more reliable for financial terms
            model_weight = 0.4
            
            ensemble_score = (
                lexicon_weight * lexicon_result['score'] + 
                model_weight * model_result['score']
            )
            
            # Combined confidence
            ensemble_confidence = (
                lexicon_result['confidence'] * lexicon_weight +
                model_result['confidence'] * model_weight
            )
            
            final_score = ensemble_score
            final_confidence = ensemble_confidence
            method = 'ensemble'
            
            self.stats['ensemble_predictions'] += 1
        
        elif lexicon_result['confidence'] > 0:
            # Use lexicon only
            final_score = lexicon_result['score']
            final_confidence = lexicon_result['confidence']
            method = 'lexicon_only'
            
            self.stats['lexicon_only'] += 1
        
        elif model_result['confidence'] > 0:
            # Use model only
            final_score = model_result['score']
            final_confidence = model_result['confidence']
            method = 'model_only'
            
            self.stats['model_predictions'] += 1
        
        else:
            # No confident prediction
            final_score = 0.5
            final_confidence = 0.0
            method = 'no_signal'
        
        # Determine sentiment label
        if final_score > 0.6:
            sentiment_label = 'bullish'
        elif final_score < 0.4:
            sentiment_label = 'bearish'
        else:
            sentiment_label = 'neutral'
        
        self.stats['total_analyzed'] += 1
        
        return {
            'sentiment': sentiment_label,
            'score': final_score,
            'confidence': final_confidence,
            'method': method,
            'lexicon_result': lexicon_result,
            'model_result': model_result,
            'text_length': len(text)
        }
    
    def analyze_batch(self, texts: List[str]) -> List[Dict]:
        """
        Analyze sentiment for multiple texts efficiently
        """
        results = []
        
        for text in texts:
            result = self.analyze_sentiment(text)
            results.append(result)
        
        return results
    
    def evaluate_accuracy(self, texts: List[str], 
                         true_labels: List[str]) -> Dict:
        """
        Evaluate sentiment accuracy against ground truth
        
        Args:
            texts: List of text samples
            true_labels: List of true sentiment labels ('bullish', 'bearish', 'neutral')
        
        Returns:
            Accuracy metrics
        """
        if len(texts) != len(true_labels):
            raise ValueError("Texts and labels must have same length")
        
        predictions = self.analyze_batch(texts)
        predicted_labels = [p['sentiment'] for p in predictions]
        
        # Calculate accuracy
        correct = sum(1 for pred, true in zip(predicted_labels, true_labels) if pred == true)
        accuracy = correct / len(texts)
        
        # Calculate per-class metrics
        class_metrics = {}
        for label in ['bullish', 'bearish', 'neutral']:
            true_positives = sum(1 for pred, true in zip(predicted_labels, true_labels) 
                               if pred == label and true == label)
            predicted_positives = sum(1 for pred in predicted_labels if pred == label)
            actual_positives = sum(1 for true in true_labels if true == label)
            
            precision = true_positives / predicted_positives if predicted_positives > 0 else 0
            recall = true_positives / actual_positives if actual_positives > 0 else 0
            f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
            
            class_metrics[label] = {
                'precision': precision,
                'recall': recall,
                'f1': f1,
                'support': actual_positives
            }
        
        return {
            'overall_accuracy': accuracy,
            'class_metrics': class_metrics,
            'total_samples': len(texts),
            'correct_predictions': correct
        }
    
    def get_feature_importance(self) -> Dict:
        """
        Get importance of different sentiment features
        """
        # Count usage of lexicon terms
        term_usage = defaultdict(int)
        
        # This would be populated during actual analysis
        # For now, return the lexicon structure
        
        return {
            'bullish_terms': len(self.bullish_lexicon),
            'bearish_terms': len(self.bearish_lexicon),
            'amplifiers': len(self.amplifiers),
            'model_available': self.model_pipeline is not None
        }
    
    def save_lexicon(self, path: str):
        """Save custom lexicon for future use"""
        lexicon_data = {
            'bullish_lexicon': self.bullish_lexicon,
            'bearish_lexicon': self.bearish_lexicon,
            'amplifiers': self.amplifiers
        }
        
        with open(path, 'w', encoding='utf-8') as f:
            json.dump(lexicon_data, f, ensure_ascii=False, indent=2)
        
        print(f"Lexicon saved to {path}")
    
    def load_lexicon(self, path: str):
        """Load custom lexicon"""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                lexicon_data = json.load(f)
            
            self.bullish_lexicon.update(lexicon_data.get('bullish_lexicon', {}))
            self.bearish_lexicon.update(lexicon_data.get('bearish_lexicon', {}))
            self.amplifiers.update(lexicon_data.get('amplifiers', {}))
            
            print(f"Lexicon loaded from {path}")
            return True
        except Exception as e:
            print(f"Failed to load lexicon: {e}")
            return False
    
    def get_stats(self) -> Dict:
        """Get analysis statistics"""
        total = self.stats['total_analyzed']
        
        return {
            **self.stats,
            'lexicon_ratio': self.stats['lexicon_only'] / max(1, total),
            'model_ratio': self.stats['model_predictions'] / max(1, total),
            'ensemble_ratio': self.stats['ensemble_predictions'] / max(1, total),
            'model_available': self.model_pipeline is not None
        }
