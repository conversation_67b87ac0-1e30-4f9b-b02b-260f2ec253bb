"""
Global settings for the Saudi NLP Trading System.

This module centralises configuration values loaded from environment
variables.  All configuration options can be overridden by creating a
.env file at the project root.
"""

import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables from a .env file if present
env_path = Path(__file__).resolve().parents[1] / ".env"
if env_path.exists():
    load_dotenv(env_path)

# Database configuration
DATABASE_URL: str = os.getenv("DATABASE_URL", "postgresql://localhost/saudi_nlp_trading")

# AWS credentials
AWS_ACCESS_KEY_ID: str = os.getenv("AWS_ACCESS_KEY_ID", "")
AWS_SECRET_ACCESS_KEY: str = os.getenv("AWS_SECRET_ACCESS_KEY", "")
AWS_DEFAULT_REGION: str = os.getenv("AWS_DEFAULT_REGION", "me-south-1")

# Redis configuration
REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379/0")

# 2Captcha API key
CAPTCHA_API_KEY: str = os.getenv("CAPTCHA_API_KEY", "")

# Logging level
LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")

# Thread scraping settings
MAX_REQUESTS_PER_MINUTE: int = int(os.getenv("MAX_REQUESTS_PER_MINUTE", "30"))
REQUEST_RETRY_LIMIT: int = int(os.getenv("REQUEST_RETRY_LIMIT", "3"))

# Proxy configuration
PROXY_LIST: str = os.getenv("PROXY_LIST", "")  # Comma separated list of proxies
