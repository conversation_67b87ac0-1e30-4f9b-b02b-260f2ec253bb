{"timestamp":"2025-08-13T10:35:15.998036+03:00","phase":"INIT","event":"Run structure created","data":{"run_id":"8dfbc7a9","started_at":"2025-08-13T10:35:15.998036+03:00","seed":1337,"limiter":{"refill_rate_rps":0.5,"burst":5},"targets":{"threads":3,"est_pages":5},"env":{"python":"3.12","os":"windows","agent_version":"1.1"},"schema_version":"1.1"}}
{"timestamp":"2025-08-13T10:35:15.999045+03:00","phase":"RUN_0","event":"STARTING","data":null}
{"timestamp":"2025-08-13T10:35:16.000040+03:00","phase":"RUN_0","event":"COMPLETED","data":null}
{"timestamp":"2025-08-13T10:35:16.000040+03:00","phase":"RUN_1","event":"STARTING","data":null}
{"timestamp":"2025-08-13T10:35:26.661750+03:00","phase":"RUN_1","event":"COMPLETED","data":null}
{"timestamp":"2025-08-13T10:35:26.662275+03:00","phase":"RUN_2","event":"STARTING","data":null}
{"timestamp":"2025-08-13T10:35:26.662275+03:00","phase":"RUN_2","event":"COMPLETED","data":null}
{"timestamp":"2025-08-13T10:35:26.662275+03:00","phase":"RUN_3","event":"STARTING","data":null}
{"timestamp":"2025-08-13T10:35:26.664567+03:00","phase":"RUN_3","event":"COMPLETED","data":null}
{"timestamp":"2025-08-13T10:35:26.664567+03:00","phase":"RUN_4","event":"STARTING","data":null}
{"timestamp":"2025-08-13T10:35:26.693381+03:00","phase":"RUN_4","event":"COMPLETED","data":null}
{"timestamp":"2025-08-13T10:35:26.693381+03:00","phase":"RUN_5","event":"STARTING","data":null}
{"timestamp":"2025-08-13T10:50:27.760265+03:00","phase":"RUN_5","event":"FAILED","data":{"abort_reason":"Throughput below threshold: 0.31 < 0.40 RPS"}}
