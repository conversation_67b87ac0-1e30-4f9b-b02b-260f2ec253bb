{"schema_version": "1.1", "runtime": {"run_id": "v12_complete_20250810_144759_876030b7", "started_at": "2025-08-10T14:47:59.618216+03:00", "ended_at": "2025-08-10T14:47:59.624287+03:00", "duration_seconds": 0.01, "python_version": "3.12.10", "platform": "Windows-11-10.0.26100-SP0", "git_commit": "602fb5d5e49dd00ea9ffc1392300d2af29578254", "config_snapshot": {"requests_per_minute": 30, "rate_limit": {"capacity": 7, "refill_rate": 0.5}}}, "scraping_metrics": {"total_threads": 3, "total_posts": 20, "success_rate": 1.0}, "performance_metrics": {"total_processing_time_seconds": 0.006071805953979492, "posts_per_second": 3293.912906899124}, "quality_metrics": {"arabic_posts_ratio": 1.0, "financial_content_ratio": 1.0}, "v12_soak_metrics": {"synthetic_records_generated": 100000, "synthetic_throughput_rec_per_sec": 19062.1, "peak_rss_mb": 22.3, "throttle_events": 2, "drift_alerts": 0}}