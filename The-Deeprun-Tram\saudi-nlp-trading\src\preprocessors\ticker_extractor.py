"""
Ticker extraction utilities.

This module contains helper functions to identify and validate Tadawul
ticker codes from arbitrary text.  While the ArabicTextProcessor includes
a basic extraction method, this file can be extended to handle more
complex patterns (e.g. combined tickers with prefixes, or synonyms).
"""

import re
from typing import List, Set


def extract_tickers(text: str, known_tickers: Set[str]) -> List[str]:
    """
    Extract potential 4‑digit tickers from the input string and
    validate against a set of known tickers.
    """
    pattern = r'\b\d{4}\b'
    matches = re.findall(pattern, text)
    return [match for match in matches if match in known_tickers]
