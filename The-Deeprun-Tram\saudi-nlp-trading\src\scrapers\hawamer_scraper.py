import asyncio
import json
import os
from datetime import datetime
import random
from pathlib import Path
from typing import Dict, List, Optional

import re
import requests
from loguru import logger
from playwright.async_api import async_playwright

from config.settings import MAX_REQUESTS_PER_MINUTE  # Not used here but imported for potential custom logic
from .base_scraper import BaseScraper


class CaptchaService:
    """Minimal 2Captcha client for Cloudflare Turnstile tokens."""
    def __init__(self, api_key: Optional[str], timeout_seconds: int = 120):
        self.api_key = api_key
        self.base = "https://api.2captcha.com"
        self.timeout_seconds = timeout_seconds

    async def _post(self, path: str, json_body: Dict) -> Dict:
        def _req():
            return requests.post(f"{self.base}{path}", json=json_body, timeout=30).json()
        return await asyncio.to_thread(_req)

    async def solve_turnstile(self, website_url: str, sitekey: str, proxy: Optional[str] = None) -> Optional[str]:
        if not self.api_key:
            logger.warning("CAPTCHA_API_KEY not set; cannot solve Turnstile.")
            return None
        task_type = "TurnstileTask" if proxy else "TurnstileTaskProxyless"
        task: Dict = {
            "type": task_type,
            "websiteURL": website_url,
            "websiteKey": sitekey
        }
        if proxy:
            # Expected format: protocol://user:pass@host:port or http://host:port
            task["proxyType"] = "http"
            task["proxyAddress"] = proxy
        create = await self._post("/createTask", {"clientKey": self.api_key, "task": task})
        if not create or not create.get("taskId"):
            logger.error(f"2Captcha createTask failed: {create}")
            return None
        task_id = create["taskId"]
        # Poll for result up to timeout_seconds
        max_loops = max(1, int(self.timeout_seconds / 2))
        for _ in range(max_loops):
            await asyncio.sleep(2 + random.uniform(0, 1))
            res = await self._post("/getTaskResult", {"clientKey": self.api_key, "taskId": task_id})
            if res and res.get("status") == "ready":
                solution = res.get("solution", {})
                token = solution.get("token")
                if token:
                    return token
        logger.error("2Captcha Turnstile task timed out")
        return None


from ..utils.proxy_manager import ProxyManager

class HawamerScraper(BaseScraper):
    """
    Sophisticated scraper for Hawamer.com with anti‑detection measures.

    The scraper launches a stealth Playwright browser with a Saudi locale
    and timezone to reduce the likelihood of being blocked by Cloudflare.
    It records API endpoints for later analysis and anonymises usernames
    to comply with PDPL requirements.
    """

    def __init__(self, config_path: str = "config/scraper_config.yaml"):
        self.config = self._load_config(config_path)
        self.session_data: Dict[str, Optional[Dict]] = {}
        self.api_endpoints: List[Dict] = []

        # Scrape configuration
        scrape_cfg = self.config.get('scrape', {})
        self.nav_timeout = int(scrape_cfg.get('navigation_timeout_ms', 60000))
        self.sel_timeout = int(scrape_cfg.get('wait_selector_timeout_ms', 30000))
        self.post_sel = scrape_cfg.get('post_selectors', {})
        low_high = scrape_cfg.get('inter_post_delay_sec', [2, 4])
        self.delay_low, self.delay_high = (low_high[0], low_high[1]) if isinstance(low_high, list) and len(low_high) == 2 else (2, 4)
        self.max_retries = int(scrape_cfg.get('max_retries', 3))

        # Cloudflare / captcha configuration
        self.cf_cfg = self.config.get('cloudflare', {"retries": 3, "wait_time_ms": 5000})
        self.cf_detection = self.cf_cfg.get('detection_strings', ['Just a moment', 'لحظة'])
        self.captcha_cfg = self.config.get('captcha', {"service": "2captcha", "api_key_env": "CAPTCHA_API_KEY", "timeout_seconds": 120})
        self.proxy_cfg = self.config.get('proxy', {"enabled": False, "list": [], "rotation_interval": 50})

        self._captcha_api_key = os.getenv(self.captcha_cfg.get('api_key_env', 'CAPTCHA_API_KEY'))
        self._captcha = CaptchaService(self._captcha_api_key, timeout_seconds=int(self.captcha_cfg.get('timeout_seconds', 120)))
        self._debug = os.getenv('SCRAPER_DEBUG', '0') == '1'

        # Proxy manager
        self.proxy_manager = ProxyManager(self.proxy_cfg.get('list', []))

    def _load_config(self, path: str) -> Dict:
        import yaml
        with open(path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)

    async def initialize_browser(self, headless: Optional[bool] = None):
        """
        Set up a stealth browser with a Saudi Arabia fingerprint and optional proxy.
        """
        self.playwright = await async_playwright().start()

        # Browser args for stealth
        args = [
            '--disable-blink-features=AutomationControlled',
            '--disable-dev-shm-usage',
            '--no-sandbox',
            '--disable-web-security',
            '--disable-features=IsolateOrigins,site-per-process',
            '--disable-site-isolation-trials',
            f'--user-agent={self._get_saudi_user_agent()}'
        ]
        if headless is None:
            headless = False
        if not headless:
            args.append('--start-maximized')

        # Prepare proxy for Playwright
        proxy_dict = None
        if self.proxy_cfg.get('enabled'):
            proxy_str = self.proxy_manager.get_next_proxy()
            if proxy_str:
                # Parse protocol://user:pass@host:port or protocol://host:port
                m = re.match(r'(https?)://(?:(?P<user>[^:]+):(?P<pwd>[^@]+)@)?(?P<host>[^:]+):(?P<port>\d+)', proxy_str)
                if m:
                    proxy_dict = {'server': f"{m.group(1)}://{m.group('host')}:{m.group('port')}"}
                    if m.group('user') and m.group('pwd'):
                        proxy_dict['username'] = m.group('user')
                        proxy_dict['password'] = m.group('pwd')
                    logger.info(f"Using proxy: {proxy_dict.get('server')}")

        launch_kwargs = { 'headless': headless, 'args': args }
        if proxy_dict:
            launch_kwargs['proxy'] = proxy_dict

        self.browser = await self.playwright.chromium.launch(**launch_kwargs)

        # Create context with Saudi Arabia timezone and locale
        self.context = await self.browser.new_context(
            locale='ar-SA',
            timezone_id='Asia/Riyadh',
            viewport={'width': 1920, 'height': 1080},
            extra_http_headers={
                'Accept-Language': 'ar-SA,ar;q=0.9,en;q=0.8'
            }
        )

        # Inject stealth scripts
        await self._inject_stealth_scripts()

    async def _inject_stealth_scripts(self):
        """
        Inject scripts to evade detection.
        """
        stealth_js = """
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined
        });
        Object.defineProperty(navigator, 'plugins', {
            get: () => [1, 2, 3, 4, 5]
        });
        Object.defineProperty(navigator, 'languages', {
            get: () => ['ar-SA','ar','en']
        });
        window.chrome = { runtime: {} };
        const originalQuery = window.navigator.permissions.query;
        window.navigator.permissions.query = (parameters) => (
            parameters.name === 'notifications' ?
                Promise.resolve({ state: Notification.permission }) :
                originalQuery(parameters)
        );
        """
        await self.context.add_init_script(stealth_js)

    async def _handle_turnstile(self, page, url: str) -> bool:
        """Enhanced Turnstile handling with proper iframe detection"""
        try:
            await page.wait_for_timeout(3000)
            # Method 1: direct Turnstile frame
            turnstile_frame = None
            for frame in page.frames:
                if 'challenges.cloudflare.com' in (frame.url or ''):
                    turnstile_frame = frame
                    logger.info(f"Found Turnstile frame: {frame.url}")
                    break

            # Method 2: check main page widgets
            if not turnstile_frame:
                elems = await page.locator('div[id*="turnstile"], div[class*="cf-turnstile"]').all()
                for el in elems:
                    sitekey = await el.get_attribute('data-sitekey')
                    if sitekey:
                        logger.info(f"Found sitekey in main page: {sitekey}")
                        return await self._solve_and_inject_turnstile(page, url, sitekey)

            # Method 3: search inside frame
            if turnstile_frame:
                el = turnstile_frame.locator('[data-sitekey]').first
                if await el.count() > 0:
                    sitekey = await el.get_attribute('data-sitekey')
                    if sitekey:
                        logger.info(f"Found sitekey in iframe: {sitekey}")
                        return await self._solve_and_inject_turnstile(page, url, sitekey)

            # Method 4: extract from scripts
            for script in await page.locator('script').all():
                try:
                    content = await script.inner_text()
                    m = re.search(r'[\"\']?sitekey[\"\']?\s*[:=]\s*[\"\']([^\"\']+)[\"\']', content, re.IGNORECASE)
                    if m:
                        sitekey = m.group(1)
                        logger.info(f"Found sitekey in script: {sitekey}")
                        return await self._solve_and_inject_turnstile(page, url, sitekey)
                except Exception:
                    continue
            logger.warning("No Turnstile sitekey found")
            return False
        except Exception as e:
            logger.error(f"Error in Turnstile detection: {e}")
            return False

    async def _solve_and_inject_turnstile(self, page, url: str, sitekey: str) -> bool:
        try:
            proxy = None
            if self.proxy_cfg.get('enabled') and self.proxy_cfg.get('list'):
                proxy = random.choice(self.proxy_cfg['list'])
            token = await self._captcha.solve_turnstile(url, sitekey, proxy=proxy)
            if not token:
                logger.error("Failed to get Turnstile token")
                return False
            logger.info(f"Got Turnstile token: {token[:50]}...")

            injection_script = f"""
            (function() {{
                if (window.turnstile && window.turnstile.callback) {{
                    try {{ window.turnstile.callback('{token}'); }} catch(e) {{}}
                }}
                for (let k in window) {{
                    try {{ if ((k.toLowerCase().includes('turnstile') || k.toLowerCase().includes('cf')) && typeof window[k] === 'function') {{ window[k]('{token}'); }} }} catch(e) {{}}
                }}
                window.dispatchEvent(new CustomEvent('cf-turnstile-callback', {{ detail: {{ token: '{token}' }} }}));
                const responseInput = document.querySelector('input[name="cf-turnstile-response"], #cf-turnstile-response');
                if (responseInput) {{ responseInput.value = '{token}'; responseInput.dispatchEvent(new Event('input', {{ bubbles: true }})); }}
                const form = document.querySelector('form');
                if (form && responseInput) {{ setTimeout(() => form.submit(), 1000); }}
            }})();
            """
            await page.evaluate(injection_script)
            await page.wait_for_timeout(5000)
            content = await page.content()
            for s in self.cf_detection:
                if s in content:
                    return False
            logger.info("Successfully passed Turnstile challenge")
            return True
        except Exception as e:
            logger.error(f"Error solving/injecting Turnstile: {e}")
            return False

    async def capture_api_endpoints(self, url: str):
        """
        Use Playwright to navigate to the given URL and capture API calls.

        Returns:
            page: The Playwright page instance used for navigation.
        """
        page = await self.context.new_page()

        # Set up request interception
        api_calls: List[Dict] = []

        async def handle_request(request):
            if any(pattern in request.url for pattern in ['api/', 'ajax/', '.json']):
                api_calls.append({
                    'url': request.url,
                    'method': request.method,
                    'headers': dict(request.headers),
                    'timestamp': datetime.now().isoformat()
                })

        page.on('request', handle_request)

        # Navigate with Cloudflare handling
        try:
            await page.goto(url, wait_until='domcontentloaded', timeout=self.nav_timeout)

            # Handle Turnstile if present
            await self._handle_turnstile(page, url)

            # Robust wait in case of Cloudflare/Turnstile
            cf_retries = int(self.cf_cfg.get('retries', 3))
            cf_wait = int(self.cf_cfg.get('wait_time_ms', 5000))
            for _ in range(cf_retries):
                # If the turnstile or "Just a moment" text appears, wait a bit more
                if await page.locator('text=Just a moment').count() > 0 or await page.locator('#cf-challenge, iframe[src*="challenge"], div[aria-busy="true"]').count() > 0:
                    logger.info("Cloudflare challenge detected, waiting...")
                    await page.wait_for_timeout(cf_wait)
                    await page.wait_for_load_state('networkidle', timeout=self.nav_timeout)
                else:
                    break

            # Store captured endpoints
            self.api_endpoints.extend(api_calls)

            # Extract cookies for API access
            cookies = await self.context.cookies()
            self.session_data['cookies'] = cookies

        except Exception as e:
            logger.error(f"Navigation error: {e}")

        return page

    async def _extract_title(self, page) -> str:
        """Try JSON-LD DiscussionForumPosting.headline, else page.title()."""
        try:
            for script in await page.locator('script[type="application/ld+json"]').all():
                try:
                    data = json.loads((await script.text_content()) or '{}')
                    if isinstance(data, dict) and data.get('@type') == 'DiscussionForumPosting':
                        if data.get('headline'):
                            return data['headline']
                    if isinstance(data, dict) and '@graph' in data:
                        for item in data['@graph']:
                            if item.get('@type') == 'DiscussionForumPosting' and item.get('headline'):
                                return item['headline']
                except Exception:
                    continue
        except Exception:
            pass
        try:
            return await page.title()
        except Exception:
            return ''

    async def scrape_thread(self, thread_url: str) -> Dict:
        """
        Scrape a single thread with all posts.
        """
        page = await self.capture_api_endpoints(thread_url)

        thread_data: Dict = {
            'url': thread_url,
            'scraped_at': datetime.now().isoformat(),
            'posts': []
        }

        try:
            # Wait for content to load using robust selector (attached to DOM)
            content_selector = self.post_sel.get('content', 'div[id^="post_message_"]')
            await page.wait_for_selector(content_selector, timeout=self.sel_timeout, state='attached')

            # Extract thread title
            thread_data['title'] = await self._extract_title(page)

            # Extract all post content nodes
            post_nodes = await page.locator(content_selector).all()

            debug_count = 0
            for node in post_nodes:
                # post_id from id="post_message_<id>"
                eid = await node.get_attribute('id')
                post_id = eid.split('_')[-1] if (eid and '_' in eid) else None

                # ascend to nearest post container
                anc = node.locator('xpath=ancestor::*[contains(@id,"post")][1]')

                # author
                author_hash = 'unknown'
                try:
                    author_el = anc.locator(self.post_sel.get('author', 'a[href*="member.php?u="]')).first
                    author_text = (await author_el.text_content() or '').strip()
                    author_hash = await self._anonymize_username(author_text or 'unknown')
                except Exception:
                    pass

                # timestamp heuristic
                timestamp = ''
                try:
                    ts_el = anc.locator(self.post_sel.get('timestamp', 'td.thead ~ td')).first
                    timestamp = (await ts_el.text_content() or '').strip()
                except Exception:
                    pass

                # content text
                content_text = (await node.inner_text() or '').strip()

                thread_data['posts'].append({
                    'post_id': post_id,
                    'author': author_hash,
                    'content': content_text,
                    'timestamp': timestamp,
                    'likes': 0
                })

                # optional debug artifacts even on success
                if self._debug and debug_count < 2:
                    ts = datetime.now().strftime('%Y%m%d_%H%M%S')
                    slug = thread_url.rsplit('/', 1)[-1].replace(':', '_')
                    Path('logs').mkdir(parents=True, exist_ok=True)
                    try:
                        await page.screenshot(path=f"logs/success_{slug}_{ts}.png", full_page=True)
                        html = await page.content()
                        (Path('logs') / f"success_{slug}_{ts}.html").write_text(html, encoding='utf-8')
                    except Exception:
                        pass
                    debug_count += 1

                # polite delay between processing posts
                await asyncio.sleep(random.uniform(self.delay_low, self.delay_high))

        except Exception as e:
            logger.error(f"Error scraping thread: {e}")
            # Debug artifacts
            try:
                ts = datetime.now().strftime('%Y%m%d_%H%M%S')
                slug = thread_url.rsplit('/', 1)[-1].replace(':', '_')
                Path('logs').mkdir(parents=True, exist_ok=True)
                await page.screenshot(path=f"logs/debug_{slug}_{ts}.png", full_page=True)
                html = await page.content()
                (Path('logs') / f"debug_{slug}_{ts}.html").write_text(html, encoding='utf-8')
            except Exception:
                pass

        finally:
            await page.close()

        return thread_data

    def _get_saudi_user_agent(self) -> str:
        """
        Return a Saudi Arabia specific user agent.
        """
        agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
        return random.choice(agents)

    async def _anonymize_username(self, username: str) -> str:
        """
        Hash the username for PDPL compliance.
        """
        import hashlib
        return hashlib.sha256((username or '').encode()).hexdigest()[:8]

    def _extract_number(self, text: Optional[str]) -> int:
        """
        Extract an integer from a textual representation.
        """
        try:
            return int(''.join(filter(str.isdigit, text or '0')))
        except ValueError:
            return 0
