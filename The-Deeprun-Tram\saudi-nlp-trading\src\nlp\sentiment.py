"""
Advanced Financial Sentiment Analysis for Arabic Text

Multi-model ensemble approach combining transformer models, lexicon-based analysis,
and financial domain-specific sentiment indicators for Saudi market content.
"""

import numpy as np
from typing import List, Dict, Tuple, Optional, Union
from dataclasses import dataclass
import re
from pathlib import Path
import json

try:
    from transformers import pipeline, AutoTokenizer, AutoModelForSequenceClassification
    import torch
except ImportError:
    print("Warning: Transformers not available. Install with: pip install transformers")

@dataclass
class SentimentResult:
    """Container for sentiment analysis results"""
    text: str
    overall_sentiment: str  # 'bullish', 'bearish', 'neutral'
    confidence: float
    scores: Dict[str, float]  # Individual model scores
    lexicon_score: float
    aspect_sentiments: Dict[str, float]  # Sentiment for specific entities
    indicators: List[str]  # Detected sentiment indicators
    metadata: Dict


class FinancialSentimentAnalyzer:
    """
    Advanced sentiment analyzer for Arabic financial content
    
    Features:
    - Multi-model ensemble (AraBERT variants)
    - Financial lexicon with Saudi-specific terms
    - Aspect-based sentiment for companies/tickers
    - Market regime detection (bull/bear market indicators)
    - Confidence scoring and uncertainty quantification
    """
    
    def __init__(self, 
                 models: Optional[List[str]] = None,
                 lexicon_path: Optional[str] = None,
                 device: str = "auto"):
        
        self.device = self._setup_device(device)
        self.models = models or self._get_default_models()
        
        # Initialize sentiment models
        self._load_sentiment_models()
        
        # Load financial lexicon
        self._load_financial_lexicon(lexicon_path)
        
        # Initialize market regime indicators
        self._initialize_market_indicators()
        
    def _setup_device(self, device: str) -> str:
        """Setup computation device"""
        if device == "auto":
            if torch.cuda.is_available():
                return "cuda"
            elif torch.backends.mps.is_available():
                return "mps"
            else:
                return "cpu"
        return device
    
    def _get_default_models(self) -> List[str]:
        """Get default Arabic sentiment models"""
        return [
            "aubmindlab/bert-base-arabertv2-twitter-ar-sentiment",
            "CAMeL-Lab/bert-base-arabic-camelbert-sa-sentiment",
            "asafaya/bert-base-arabic-sentiment"
        ]
    
    def _load_sentiment_models(self):
        """Load multiple sentiment models for ensemble"""
        self.sentiment_pipelines = {}
        
        for model_name in self.models:
            try:
                pipeline_obj = pipeline(
                    "sentiment-analysis",
                    model=model_name,
                    device=0 if self.device == "cuda" else -1,
                    return_all_scores=True
                )
                self.sentiment_pipelines[model_name] = pipeline_obj
                print(f"Loaded sentiment model: {model_name}")
            except Exception as e:
                print(f"Warning: Could not load model {model_name}: {e}")
        
        if not self.sentiment_pipelines:
            print("Warning: No sentiment models loaded successfully")
    
    def _load_financial_lexicon(self, lexicon_path: Optional[str]):
        """Load comprehensive financial sentiment lexicon"""
        
        # Saudi financial sentiment lexicon
        self.bullish_lexicon = {
            # Strong bullish (2.0)
            'صاعد': 2.0, 'ارتفاع قوي': 2.0, 'اختراق': 2.0, 'انطلاق': 2.0,
            'قفزة': 2.0, 'طفرة': 2.0, 'نمو متسارع': 2.0, 'توصية شراء قوية': 2.0,
            
            # Moderate bullish (1.5)
            'ارتفاع': 1.5, 'قوي': 1.5, 'إيجابي': 1.5, 'نمو': 1.5,
            'تحسن': 1.5, 'مكاسب': 1.5, 'ربح': 1.5, 'فرصة': 1.5,
            'توصية شراء': 1.5, 'دعم': 1.5, 'استقرار': 1.5,
            
            # Mild bullish (1.0)
            'اخضر': 1.0, 'موجب': 1.0, 'تقدم': 1.0, 'تطور': 1.0,
            'استثمار': 1.0, 'هدف': 1.0, 'مقاومة مكسورة': 1.0,
            'ترند صاعد': 1.0, 'قاع': 1.0, 'انتعاش': 1.0
        }
        
        self.bearish_lexicon = {
            # Strong bearish (-2.0)
            'انهيار': -2.5, 'هبوط حاد': -2.0, 'تراجع قوي': -2.0, 'خسائر فادحة': -2.0,
            'فقاعة': -2.0, 'أزمة': -2.0, 'كارثة': -2.0, 'توصية بيع قوية': -2.0,
            
            # Moderate bearish (-1.5)
            'هابط': -1.5, 'انخفاض': -1.5, 'ضعيف': -1.5, 'تراجع': -1.5,
            'خسارة': -1.5, 'تصريف': -1.5, 'سلبي': -1.5, 'هبوط': -1.5,
            'توصية بيع': -1.5, 'مقاومة': -1.5, 'ضغط بيع': -1.5,
            
            # Mild bearish (-1.0)
            'احمر': -1.0, 'سالب': -1.0, 'تصحيح': -1.0, 'تذبذب': -1.0,
            'ترند هابط': -1.0, 'قمة': -1.0, 'تحفظ': -1.0, 'حذر': -1.0
        }
        
        # Market regime indicators
        self.bull_market_indicators = {
            'سوق صاعد', 'موجة صعود', 'اتجاه صاعد', 'نمو اقتصادي',
            'ثقة المستثمرين', 'سيولة عالية', 'إقبال على الأسهم'
        }
        
        self.bear_market_indicators = {
            'سوق هابط', 'موجة هبوط', 'اتجاه هابط', 'ركود اقتصادي',
            'تخوف المستثمرين', 'سيولة منخفضة', 'عزوف عن الأسهم'
        }
        
        # Load custom lexicon if provided
        if lexicon_path and Path(lexicon_path).exists():
            with open(lexicon_path, 'r', encoding='utf-8') as f:
                custom_lexicon = json.load(f)
                self.bullish_lexicon.update(custom_lexicon.get('bullish', {}))
                self.bearish_lexicon.update(custom_lexicon.get('bearish', {}))
        
        print(f"Loaded lexicon: {len(self.bullish_lexicon)} bullish, {len(self.bearish_lexicon)} bearish terms")
    
    def _initialize_market_indicators(self):
        """Initialize market regime and volatility indicators"""
        
        # Volatility indicators
        self.high_volatility_terms = {
            'تذبذب عالي', 'تقلبات شديدة', 'عدم استقرار', 'تذبذب حاد',
            'تقلبات عنيفة', 'عدم يقين', 'مخاطر عالية'
        }
        
        # Volume indicators
        self.high_volume_terms = {
            'حجم تداول عالي', 'سيولة كبيرة', 'نشاط مكثف', 'إقبال كثيف',
            'تداولات ضخمة', 'حركة نشطة'
        }
        
        self.low_volume_terms = {
            'حجم تداول منخفض', 'سيولة ضعيفة', 'نشاط محدود', 'ركود في التداول',
            'تداولات قليلة', 'حركة خاملة'
        }
    
    def calculate_lexicon_sentiment(self, text: str) -> Tuple[float, List[str]]:
        """Calculate sentiment using financial lexicon"""
        words = text.split()
        total_score = 0.0
        word_count = 0
        found_indicators = []
        
        # Check individual words
        for word in words:
            if word in self.bullish_lexicon:
                total_score += self.bullish_lexicon[word]
                word_count += 1
                found_indicators.append(f"bullish:{word}")
            elif word in self.bearish_lexicon:
                total_score += self.bearish_lexicon[word]
                word_count += 1
                found_indicators.append(f"bearish:{word}")
        
        # Check phrases (2-3 word combinations)
        for i in range(len(words) - 1):
            phrase = ' '.join(words[i:i+2])
            if phrase in self.bullish_lexicon:
                total_score += self.bullish_lexicon[phrase]
                word_count += 1
                found_indicators.append(f"bullish:{phrase}")
            elif phrase in self.bearish_lexicon:
                total_score += self.bearish_lexicon[phrase]
                word_count += 1
                found_indicators.append(f"bearish:{phrase}")
        
        # Check 3-word phrases
        for i in range(len(words) - 2):
            phrase = ' '.join(words[i:i+3])
            if phrase in self.bullish_lexicon:
                total_score += self.bullish_lexicon[phrase]
                word_count += 1
                found_indicators.append(f"bullish:{phrase}")
            elif phrase in self.bearish_lexicon:
                total_score += self.bearish_lexicon[phrase]
                word_count += 1
                found_indicators.append(f"bearish:{phrase}")
        
        # Normalize score
        if word_count > 0:
            normalized_score = total_score / word_count
            # Convert to [0, 1] scale where 0.5 is neutral
            normalized_score = (normalized_score + 2.5) / 5.0
            normalized_score = max(0.0, min(1.0, normalized_score))
        else:
            normalized_score = 0.5  # Neutral
        
        return normalized_score, found_indicators
    
    def analyze_with_models(self, text: str) -> Dict[str, float]:
        """Analyze sentiment using transformer models"""
        model_scores = {}
        
        for model_name, pipeline_obj in self.sentiment_pipelines.items():
            try:
                results = pipeline_obj(text)
                
                # Convert to unified format
                if isinstance(results[0], list):
                    # Multiple scores returned
                    score_dict = {r['label']: r['score'] for r in results[0]}
                else:
                    # Single score returned
                    score_dict = {results[0]['label']: results[0]['score']}
                
                # Normalize to bullish probability
                if 'POSITIVE' in score_dict:
                    model_scores[model_name] = score_dict['POSITIVE']
                elif 'positive' in score_dict:
                    model_scores[model_name] = score_dict['positive']
                elif 'LABEL_1' in score_dict:  # Some models use LABEL_0/LABEL_1
                    model_scores[model_name] = score_dict['LABEL_1']
                else:
                    # Take the maximum score as positive sentiment
                    model_scores[model_name] = max(score_dict.values())
                    
            except Exception as e:
                print(f"Warning: Model {model_name} failed: {e}")
                continue
        
        return model_scores
    
    def detect_market_regime(self, text: str) -> Dict[str, float]:
        """Detect market regime indicators"""
        regime_scores = {
            'bull_market': 0.0,
            'bear_market': 0.0,
            'high_volatility': 0.0,
            'high_volume': 0.0,
            'low_volume': 0.0
        }
        
        # Check for bull market indicators
        for indicator in self.bull_market_indicators:
            if indicator in text:
                regime_scores['bull_market'] += 1.0
        
        # Check for bear market indicators
        for indicator in self.bear_market_indicators:
            if indicator in text:
                regime_scores['bear_market'] += 1.0
        
        # Check volatility indicators
        for indicator in self.high_volatility_terms:
            if indicator in text:
                regime_scores['high_volatility'] += 1.0
        
        # Check volume indicators
        for indicator in self.high_volume_terms:
            if indicator in text:
                regime_scores['high_volume'] += 1.0
        
        for indicator in self.low_volume_terms:
            if indicator in text:
                regime_scores['low_volume'] += 1.0
        
        # Normalize scores
        max_possible = 3  # Assume max 3 indicators per category
        for key in regime_scores:
            regime_scores[key] = min(1.0, regime_scores[key] / max_possible)
        
        return regime_scores
    
    def aspect_based_sentiment(self, text: str, entities: List[str]) -> Dict[str, float]:
        """Calculate sentiment for specific entities (companies/tickers)"""
        aspect_sentiments = {}
        
        # Split text into sentences
        sentences = re.split(r'[.!؟]', text)
        
        for entity in entities:
            entity_sentiments = []
            
            # Find sentences mentioning this entity
            for sentence in sentences:
                if entity in sentence:
                    # Analyze sentiment of this sentence
                    lexicon_score, _ = self.calculate_lexicon_sentiment(sentence)
                    entity_sentiments.append(lexicon_score)
            
            if entity_sentiments:
                # Average sentiment for this entity
                aspect_sentiments[entity] = np.mean(entity_sentiments)
            else:
                aspect_sentiments[entity] = 0.5  # Neutral if not mentioned
        
        return aspect_sentiments
    
    def analyze_sentiment(self, 
                         text: str, 
                         entities: Optional[List[str]] = None) -> SentimentResult:
        """
        Comprehensive sentiment analysis
        
        Args:
            text: Input Arabic text
            entities: List of entities for aspect-based analysis
            
        Returns:
            SentimentResult with comprehensive analysis
        """
        if not text or not text.strip():
            return SentimentResult(
                text=text,
                overall_sentiment='neutral',
                confidence=0.0,
                scores={},
                lexicon_score=0.5,
                aspect_sentiments={},
                indicators=[],
                metadata={'error': 'Empty text'}
            )
        
        # Lexicon-based analysis
        lexicon_score, indicators = self.calculate_lexicon_sentiment(text)
        
        # Model-based analysis
        model_scores = self.analyze_with_models(text)
        
        # Market regime detection
        regime_scores = self.detect_market_regime(text)
        
        # Aspect-based sentiment
        aspect_sentiments = {}
        if entities:
            aspect_sentiments = self.aspect_based_sentiment(text, entities)
        
        # Ensemble scoring
        all_scores = list(model_scores.values()) + [lexicon_score]
        
        if all_scores:
            ensemble_score = np.mean(all_scores)
            confidence = 1.0 - np.std(all_scores)  # Lower std = higher confidence
            confidence = max(0.0, min(1.0, confidence))
        else:
            ensemble_score = lexicon_score
            confidence = 0.5
        
        # Determine overall sentiment
        if ensemble_score > 0.6:
            overall_sentiment = 'bullish'
        elif ensemble_score < 0.4:
            overall_sentiment = 'bearish'
        else:
            overall_sentiment = 'neutral'
        
        # Adjust confidence based on regime indicators
        if regime_scores['bull_market'] > 0.5 and overall_sentiment == 'bullish':
            confidence = min(1.0, confidence + 0.1)
        elif regime_scores['bear_market'] > 0.5 and overall_sentiment == 'bearish':
            confidence = min(1.0, confidence + 0.1)
        
        return SentimentResult(
            text=text[:200] + '...' if len(text) > 200 else text,
            overall_sentiment=overall_sentiment,
            confidence=confidence,
            scores={
                'ensemble': ensemble_score,
                'lexicon': lexicon_score,
                **model_scores
            },
            lexicon_score=lexicon_score,
            aspect_sentiments=aspect_sentiments,
            indicators=indicators,
            metadata={
                'model_count': len(model_scores),
                'regime_scores': regime_scores,
                'text_length': len(text),
                'indicator_count': len(indicators)
            }
        )
