{"schema_version": "1.1", "runtime": {"run_id": "v12_complete_20250810_144701_2a2d3ffd", "started_at": "2025-08-10T14:47:01.614521+03:00", "ended_at": "2025-08-10T14:47:01.619732+03:00", "duration_seconds": 0.01, "python_version": "3.12.10", "platform": "Windows-11-10.0.26100-SP0", "git_commit": "602fb5d5e49dd00ea9ffc1392300d2af29578254", "config_snapshot": {"requests_per_minute": 30, "rate_limit": {"capacity": 7, "refill_rate": 0.5}}}, "scraping_metrics": {"total_threads": 3, "total_posts": 20, "success_rate": 1.0}, "performance_metrics": {"total_processing_time_seconds": 0.00521087646484375, "posts_per_second": 3838.125915080527}, "quality_metrics": {"arabic_posts_ratio": 1.0, "financial_content_ratio": 1.0}, "v12_soak_metrics": {"synthetic_records_generated": 100000, "synthetic_throughput_rec_per_sec": 19062.1, "peak_rss_mb": 22.3, "throttle_events": 2, "drift_alerts": 0}}