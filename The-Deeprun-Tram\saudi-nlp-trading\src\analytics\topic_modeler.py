from bertopic import BERTopic
from sklearn.feature_extraction.text import CountVectorizer
from typing import List, Dict


class ArabicTopicModeler:
    """
    BERTopic implementation with Swan embeddings for Arabic text.
    """

    def __init__(self, swan_embedder):
        self.embedder = swan_embedder
        # Arabic‑specific CountVectorizer
        self.vectorizer = CountVectorizer(
            ngram_range=(1, 3),
            stop_words=self._load_arabic_stopwords(),
            max_features=10000
        )
        # Initialise BERTopic
        self.topic_model = BERTopic(
            embedding_model=self.embedder.model,
            vectorizer_model=self.vectorizer,
            language='arabic',
            calculate_probabilities=True,
            verbose=False
        )

    def fit_transform(self, documents: List[str]) -> Dict:
        """
        Fit the topic model and return results.
        """
        embeddings = self.embedder.embed_texts(documents)
        topics, probs = self.topic_model.fit_transform(documents, embeddings=embeddings)
        topic_info = self.topic_model.get_topic_info()
        topic_drift = self._calculate_topic_drift(topics)
        return {
            'topics': topics,
            'probabilities': probs,
            'topic_info': topic_info,
            'topic_drift': topic_drift,
            'embeddings': embeddings
        }

    def _calculate_topic_drift(self, topics: List[int]) -> float:
        """
        Calculate a simple topic drift metric based on changes between consecutive posts.
        """
        changes = sum(1 for i in range(1, len(topics)) if topics[i] != topics[i - 1])
        return changes / len(topics) if topics else 0.0

    def _load_arabic_stopwords(self) -> List[str]:
        """
        Load a list of Arabic stopwords.  This list should be expanded to include
        financial terms and forum‑specific filler words as needed.
        """
        return [
            'في', 'من', 'إلى', 'على', 'عن', 'مع', 'هذا', 'هذه', 'ذلك',
            'هناك', 'كما', 'وقد', 'وقد', 'ما', 'لا'
        ]
