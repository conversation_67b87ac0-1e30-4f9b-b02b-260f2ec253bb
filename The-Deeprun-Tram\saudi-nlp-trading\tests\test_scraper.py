import pytest
import async<PERSON>

# Import from the package; use relative import by adjusting sys.path if needed
from src.scrapers.hawamer_scraper import HawamerScraper


class TestHawamerScraper:
    """
    Test suite for Hawamer scraper functionality.
    """

    @pytest.mark.asyncio
    async def test_browser_initialization(self):
        """Test stealth browser setup."""
        scraper = HawamerScraper()
        await scraper.initialize_browser()

        # Verify browser context
        assert scraper.browser is not None
        assert scraper.context is not None

        # Check Saudi locale in the browser
        locale = await scraper.context.evaluate("() => navigator.language")
        assert "ar" in locale

    @pytest.mark.asyncio
    async def test_cloudflare_handling(self):
        """Test Cloudflare challenge handling."""
        scraper = HawamerScraper()
        await scraper.initialize_browser()

        # Navigate to the homepage
        page = await scraper.capture_api_endpoints("https://hawamer.com")

        # Verify we got past Cloudflare; Hawamer is Arabic. Accept Arabic title or domain presence
        title = await page.title()
        url = page.url
        assert ("هوامير" in title) or ("hawamer.com" in url.lower())

    @pytest.mark.asyncio
    async def test_api_endpoint_capture(self):
        """Test API endpoint discovery."""
        scraper = HawamerScraper()
        await scraper.initialize_browser()

        # Example thread URL (should be replaced with a real thread ID during live tests)
        test_url = "https://hawamer.com/vb/hawamer/123456/"
        await scraper.capture_api_endpoints(test_url)

        # Check if endpoints were captured
        assert len(scraper.api_endpoints) > 0
        # Verify endpoint structure
        for endpoint in scraper.api_endpoints:
            assert 'url' in endpoint
            assert 'method' in endpoint
            assert 'headers' in endpoint
