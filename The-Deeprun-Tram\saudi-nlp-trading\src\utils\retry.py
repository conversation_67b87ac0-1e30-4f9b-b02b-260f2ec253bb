"""
Retry and Backoff System for Saudi NLP Trading

Implements exponential backoff with jitter for HTTP requests.
Handles 429, 5xx errors with bounded retry counts and proper logging.
"""

import time
import random
import asyncio
from typing import Callable, Any, Optional, List, Type, Union
from functools import wraps
from dataclasses import dataclass

from .logging import get_logger
from ..config.settings import get_config

@dataclass
class RetryConfig:
    """Configuration for retry behavior"""
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 300.0
    jitter: float = 0.1
    backoff_factor: float = 2.0
    retryable_status_codes: List[int] = None
    retryable_exceptions: List[Type[Exception]] = None
    
    def __post_init__(self):
        if self.retryable_status_codes is None:
            self.retryable_status_codes = [429, 500, 502, 503, 504]
        
        if self.retryable_exceptions is None:
            self.retryable_exceptions = [
                ConnectionError,
                TimeoutError,
                OSError
            ]

class RetryableError(Exception):
    """Exception that should trigger a retry"""
    
    def __init__(self, message: str, status_code: Optional[int] = None, 
                 retry_after: Optional[float] = None):
        super().__init__(message)
        self.status_code = status_code
        self.retry_after = retry_after

class NonRetryableError(Exception):
    """Exception that should not trigger a retry"""
    pass

class BackoffCalculator:
    """Calculate backoff delays with jitter"""
    
    def __init__(self, config: RetryConfig):
        self.config = config
        self.logger = get_logger(__name__)
    
    def calculate_delay(self, attempt: int, retry_after: Optional[float] = None) -> float:
        """
        Calculate delay for given attempt number
        
        Args:
            attempt: Current attempt number (0-based)
            retry_after: Server-suggested retry delay (from Retry-After header)
        
        Returns:
            delay in seconds
        """
        
        # Use server-suggested delay if provided
        if retry_after is not None:
            base_delay = min(retry_after, self.config.max_delay)
        else:
            # Exponential backoff
            base_delay = min(
                self.config.base_delay * (self.config.backoff_factor ** attempt),
                self.config.max_delay
            )
        
        # Add jitter to avoid thundering herd
        jitter_range = base_delay * self.config.jitter
        jitter = random.uniform(-jitter_range, jitter_range)
        
        delay = max(0, base_delay + jitter)
        
        self.logger.debug(
            "Calculated backoff delay",
            attempt=attempt,
            base_delay=base_delay,
            jitter=jitter,
            final_delay=delay,
            retry_after=retry_after
        )
        
        return delay

class RetryManager:
    """Manages retry logic with proper logging and metrics"""
    
    def __init__(self, config: Optional[RetryConfig] = None):
        if config is None:
            app_config = get_config()
            config = RetryConfig(
                base_delay=app_config.rate_limit.base_delay,
                max_delay=app_config.rate_limit.max_delay,
                jitter=app_config.rate_limit.jitter
            )
        
        self.config = config
        self.backoff_calculator = BackoffCalculator(config)
        self.logger = get_logger(__name__)
    
    def should_retry(self, exception: Exception, attempt: int) -> bool:
        """
        Determine if an exception should trigger a retry
        
        Args:
            exception: The exception that occurred
            attempt: Current attempt number (0-based)
        
        Returns:
            True if should retry, False otherwise
        """
        
        # Check attempt limit
        if attempt >= self.config.max_attempts:
            return False
        
        # Check if it's a retryable error
        if isinstance(exception, RetryableError):
            return True
        
        if isinstance(exception, NonRetryableError):
            return False
        
        # Check exception type
        for exc_type in self.config.retryable_exceptions:
            if isinstance(exception, exc_type):
                return True
        
        # Check HTTP status codes if available
        if hasattr(exception, 'status_code'):
            return exception.status_code in self.config.retryable_status_codes
        
        if hasattr(exception, 'response') and hasattr(exception.response, 'status_code'):
            return exception.response.status_code in self.config.retryable_status_codes
        
        return False
    
    def execute_with_retry(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute function with retry logic
        
        Args:
            func: Function to execute
            *args: Function arguments
            **kwargs: Function keyword arguments
        
        Returns:
            Function result
        
        Raises:
            Last exception if all retries exhausted
        """
        
        last_exception = None
        
        for attempt in range(self.config.max_attempts):
            try:
                # Log attempt
                if attempt > 0:
                    self.logger.info(
                        f"Retrying function call",
                        function=func.__name__,
                        attempt=attempt,
                        max_attempts=self.config.max_attempts
                    )
                
                # Execute function
                result = func(*args, **kwargs)
                
                # Log success after retry
                if attempt > 0:
                    self.logger.info(
                        f"Function succeeded after retry",
                        function=func.__name__,
                        attempt=attempt,
                        total_attempts=attempt + 1
                    )
                
                return result
                
            except Exception as e:
                last_exception = e
                
                # Check if we should retry
                if not self.should_retry(e, attempt):
                    self.logger.error(
                        f"Non-retryable error in function",
                        function=func.__name__,
                        attempt=attempt,
                        exception_class=e.__class__.__name__,
                        exc_info=True
                    )
                    raise
                
                # Calculate delay
                retry_after = None
                if isinstance(e, RetryableError):
                    retry_after = e.retry_after
                elif hasattr(e, 'retry_after'):
                    retry_after = e.retry_after
                
                delay = self.backoff_calculator.calculate_delay(attempt, retry_after)
                
                # Log retry
                self.logger.warning(
                    f"Function failed, will retry",
                    function=func.__name__,
                    attempt=attempt,
                    exception_class=e.__class__.__name__,
                    delay=delay,
                    retry_after=retry_after
                )
                
                # Wait before retry (unless it's the last attempt)
                if attempt < self.config.max_attempts - 1:
                    time.sleep(delay)
        
        # All retries exhausted
        self.logger.error(
            f"All retries exhausted for function",
            function=func.__name__,
            total_attempts=self.config.max_attempts,
            final_exception=last_exception.__class__.__name__
        )
        
        raise last_exception

    async def execute_with_retry_async(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute async function with retry logic
        """
        
        last_exception = None
        
        for attempt in range(self.config.max_attempts):
            try:
                # Log attempt
                if attempt > 0:
                    self.logger.info(
                        f"Retrying async function call",
                        function=func.__name__,
                        attempt=attempt,
                        max_attempts=self.config.max_attempts
                    )
                
                # Execute function
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                # Log success after retry
                if attempt > 0:
                    self.logger.info(
                        f"Async function succeeded after retry",
                        function=func.__name__,
                        attempt=attempt,
                        total_attempts=attempt + 1
                    )
                
                return result
                
            except Exception as e:
                last_exception = e
                
                # Check if we should retry
                if not self.should_retry(e, attempt):
                    self.logger.error(
                        f"Non-retryable error in async function",
                        function=func.__name__,
                        attempt=attempt,
                        exception_class=e.__class__.__name__,
                        exc_info=True
                    )
                    raise
                
                # Calculate delay
                retry_after = None
                if isinstance(e, RetryableError):
                    retry_after = e.retry_after
                elif hasattr(e, 'retry_after'):
                    retry_after = e.retry_after
                
                delay = self.backoff_calculator.calculate_delay(attempt, retry_after)
                
                # Log retry
                self.logger.warning(
                    f"Async function failed, will retry",
                    function=func.__name__,
                    attempt=attempt,
                    exception_class=e.__class__.__name__,
                    delay=delay,
                    retry_after=retry_after
                )
                
                # Wait before retry (unless it's the last attempt)
                if attempt < self.config.max_attempts - 1:
                    await asyncio.sleep(delay)
        
        # All retries exhausted
        self.logger.error(
            f"All retries exhausted for async function",
            function=func.__name__,
            total_attempts=self.config.max_attempts,
            final_exception=last_exception.__class__.__name__
        )
        
        raise last_exception

def retry_on_failure(config: Optional[RetryConfig] = None):
    """
    Decorator for automatic retry on function failure
    
    Args:
        config: Retry configuration (uses default if None)
    
    Usage:
        @retry_on_failure()
        def fetch_url(url):
            # Function that might fail
            pass
    """
    
    def decorator(func: Callable) -> Callable:
        retry_manager = RetryManager(config)
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            return retry_manager.execute_with_retry(func, *args, **kwargs)
        
        return wrapper
    
    return decorator

def async_retry_on_failure(config: Optional[RetryConfig] = None):
    """
    Decorator for automatic retry on async function failure
    """
    
    def decorator(func: Callable) -> Callable:
        retry_manager = RetryManager(config)
        
        @wraps(func)
        async def wrapper(*args, **kwargs):
            return await retry_manager.execute_with_retry_async(func, *args, **kwargs)
        
        return wrapper
    
    return decorator

# Global retry manager
_retry_manager: Optional[RetryManager] = None

def get_retry_manager() -> RetryManager:
    """Get global retry manager instance"""
    global _retry_manager
    if _retry_manager is None:
        _retry_manager = RetryManager()
    return _retry_manager
