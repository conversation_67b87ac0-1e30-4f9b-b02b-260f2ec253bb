"""
Mode Manager for Saudi NLP Trading

Handles feature flags and mode toggles.
Ensures NLP stages are no-ops when scrape_only=true.
"""

from typing import Any, Callable, Optional
from functools import wraps

from ..config.settings import get_config
from ..utils.logging import get_logger

class ModeManager:
    """Manages application modes and feature flags"""
    
    def __init__(self):
        self.config = get_config()
        self.logger = get_logger(__name__)
    
    def is_scrape_only(self) -> bool:
        """Check if running in scrape-only mode"""
        return self.config.mode.scrape_only
    
    def is_nlp_enabled(self) -> bool:
        """Check if NLP processing is enabled"""
        return self.config.mode.nlp_enable and not self.config.mode.scrape_only
    
    def is_dedup_enabled(self) -> bool:
        """Check if deduplication is enabled"""
        return self.config.mode.dedup_enabled
    
    def is_language_detection_enabled(self) -> bool:
        """Check if language detection is enabled"""
        return self.config.mode.language_detection_enabled
    
    def log_mode_status(self):
        """Log current mode configuration"""
        self.logger.info(
            "Mode configuration",
            scrape_only=self.config.mode.scrape_only,
            nlp_enable=self.config.mode.nlp_enable,
            dedup_enabled=self.config.mode.dedup_enabled,
            language_detection_enabled=self.config.mode.language_detection_enabled,
            storage_backend=self.config.storage.backend
        )

def require_nlp_enabled(func: Callable) -> Callable:
    """
    Decorator to ensure function only runs when NLP is enabled
    Returns None if NLP is disabled
    """
    
    @wraps(func)
    def wrapper(*args, **kwargs):
        mode_manager = get_mode_manager()
        
        if not mode_manager.is_nlp_enabled():
            logger = get_logger(func.__module__)
            logger.debug(
                f"Skipping NLP function (scrape-only mode)",
                function=func.__name__
            )
            return None
        
        return func(*args, **kwargs)
    
    return wrapper

def require_feature_enabled(feature_name: str):
    """
    Decorator to ensure function only runs when specific feature is enabled
    """
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            mode_manager = get_mode_manager()
            
            # Check specific features
            if feature_name == 'dedup' and not mode_manager.is_dedup_enabled():
                return None
            elif feature_name == 'language_detection' and not mode_manager.is_language_detection_enabled():
                return None
            
            return func(*args, **kwargs)
        
        return wrapper
    
    return decorator

class NoOpNLPPipeline:
    """No-op NLP pipeline for scrape-only mode"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.logger.info("Initialized no-op NLP pipeline (scrape-only mode)")
    
    def process_single_post(self, post: dict) -> Optional[dict]:
        """No-op post processing"""
        return None
    
    def process_batch(self, posts: list) -> list:
        """No-op batch processing"""
        self.logger.debug(
            "Skipping NLP batch processing (scrape-only mode)",
            post_count=len(posts)
        )
        return []
    
    def generate_trading_features(self, df) -> None:
        """No-op feature generation"""
        self.logger.debug("Skipping feature generation (scrape-only mode)")
        return None
    
    def __getattr__(self, name):
        """Return no-op function for any other method calls"""
        def no_op(*args, **kwargs):
            self.logger.debug(
                f"Skipping NLP method (scrape-only mode)",
                method=name
            )
            return None
        return no_op

# Global mode manager instance
_mode_manager: Optional[ModeManager] = None

def get_mode_manager() -> ModeManager:
    """Get global mode manager instance"""
    global _mode_manager
    if _mode_manager is None:
        _mode_manager = ModeManager()
    return _mode_manager

def get_nlp_pipeline():
    """
    Get NLP pipeline based on current mode
    Returns actual pipeline if NLP enabled, no-op pipeline if scrape-only
    """
    mode_manager = get_mode_manager()
    
    if mode_manager.is_nlp_enabled():
        # Import and return actual NLP pipeline
        try:
            from ..nlp.simple_pipeline import SimpleNLPPipeline
            return SimpleNLPPipeline()
        except ImportError:
            logger = get_logger(__name__)
            logger.warning("NLP pipeline not available, using no-op pipeline")
            return NoOpNLPPipeline()
    else:
        return NoOpNLPPipeline()
