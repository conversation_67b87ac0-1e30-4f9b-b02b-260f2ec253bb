# Execution Instructions for AI Agent

## Initial Setup

1. Clone this repository.
2. Run `python setup.py` to install dependencies, create directories and download base models.
3. Copy `.env.example` to `.env` and add your credentials (database URL, AWS keys, etc.).

## Testing the Scraper

Use pytest to run the scraper tests:

```bash
# Test single thread scraping
python -m pytest tests/test_scraper.py::TestHawamerScraper::test_cloudflare_handling -v

# Test full pipeline on sample data (requires at least one real thread URL)
python -m pytest tests/test_pipeline.py -v
```

## Running the Full Pipeline

```bash
# Start the pipeline with test URLs
python -m saudi-nlp-trading.src.pipeline.orchestrator --urls hawamer_urls.txt --mode test

# Production run with multiple workers
python -m saudi-nlp-trading.src.pipeline.orchestrator --mode production --workers 4
```

## Monitoring

* Check the `logs/` directory for detailed execution logs.
* View data in `data/processed/` for intermediate results.
* A dashboard can be added using Streamlit and hosted at `http://localhost:8501` (after implementing it).

## Troubleshooting

* If Cloudflare blocks requests: increase delays, verify proxy rotation and 2Captcha key.
* If Arabic text appears garbled: verify that CAMeL Tools and pyarabic are installed correctly.
* If embeddings fail: check GPU memory availability or reduce the batch size.

---

## CRITICAL SUCCESS FACTORS

1. **Cloudflare Evasion**: The scraper must successfully bypass Cloudflare using the provided stealth techniques.
2. **Arabic Processing**: All dialectal Saudi text must be properly normalised before embedding.
3. **PDPL Compliance**: No personal data should be stored; all usernames must be hashed.
4. **API Discovery**: The system should capture and reverse‑engineer Hawamer's API endpoints.
5. **Pipeline Integration**: Each component must seamlessly feed into the next.

## VERIFICATION CHECKLIST

- [ ] Scraper successfully accesses Hawamer.com.  
- [ ] API endpoints are discovered and documented.  
- [ ] Arabic text is properly preprocessed.  
- [ ] Swan embeddings are generated correctly.  
- [ ] Topic modelling identifies financial discussions.  
- [ ] Test suite passes all checks.  
- [ ] Documentation is complete and clear.  

**The system is now ready for deployment and further development.  Another AI agent picking up this work should be able to continue immediately using the implementation log and documentation provided.**
