"""
Feature Engineering Pipeline for Saudi Financial Trading Signals

Converts processed Arabic social media content into numerical features suitable
for quantitative trading strategies on Tadawul (Saudi Stock Exchange).
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional, Union
from dataclasses import dataclass
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

try:
    from sklearn.preprocessing import StandardScaler, RobustScaler
    from sklearn.decomposition import PCA
    from sklearn.feature_selection import SelectKBest, f_regression
    import ta  # Technical analysis library
except ImportError:
    print("Warning: Some dependencies not available. Install with: pip install scikit-learn ta")

@dataclass
class FeatureSet:
    """Container for engineered features"""
    features: pd.DataFrame
    feature_names: List[str]
    metadata: Dict
    target_alignment: Optional[pd.DataFrame] = None


class FinancialFeatureEngineer:
    """
    Advanced feature engineering for Arabic financial sentiment data
    
    Converts social media signals into trading-ready features:
    - Sentiment aggregation and momentum
    - Volume and engagement metrics
    - Cross-asset correlation features
    - Temporal patterns and seasonality
    - Market regime indicators
    """
    
    def __init__(self, 
                 lookback_windows: List[int] = [1, 3, 7, 14, 30],
                 sentiment_decay: float = 0.9,
                 volume_weight: bool = True):
        
        self.lookback_windows = lookback_windows
        self.sentiment_decay = sentiment_decay
        self.volume_weight = volume_weight
        
        # Initialize scalers
        self.scaler = RobustScaler()  # More robust to outliers than StandardScaler
        self.fitted = False
        
        # Feature importance tracking
        self.feature_importance = {}
        
        print(f"Initialized feature engineer with {len(lookback_windows)} lookback windows")
    
    def aggregate_sentiment_by_ticker(self, 
                                    posts_df: pd.DataFrame,
                                    time_freq: str = 'D') -> pd.DataFrame:
        """
        Aggregate sentiment data by ticker and time period
        
        Args:
            posts_df: DataFrame with posts data
            time_freq: Time frequency for aggregation ('H', 'D', 'W')
            
        Returns:
            Aggregated sentiment DataFrame
        """
        if posts_df.empty:
            return pd.DataFrame()
        
        # Ensure timestamp is datetime
        if 'timestamp' in posts_df.columns:
            posts_df['timestamp'] = pd.to_datetime(posts_df['timestamp'])
        else:
            # Use current time if no timestamp
            posts_df['timestamp'] = datetime.now()
        
        # Explode tickers (posts can mention multiple tickers)
        if 'tickers' in posts_df.columns:
            # Handle list of tickers
            exploded_df = posts_df.explode('tickers')
            exploded_df = exploded_df[exploded_df['tickers'].notna()]
            exploded_df.rename(columns={'tickers': 'ticker'}, inplace=True)
        else:
            return pd.DataFrame()
        
        # Set timestamp as index for resampling
        exploded_df.set_index('timestamp', inplace=True)
        
        # Group by ticker and resample by time frequency
        agg_functions = {
            'sentiment_score': ['mean', 'std', 'count'],
            'content': 'count',  # Post count
            'author': 'nunique',  # Unique authors
        }
        
        # Add engagement metrics if available
        if 'likes' in exploded_df.columns:
            agg_functions['likes'] = ['sum', 'mean']
        if 'shares' in exploded_df.columns:
            agg_functions['shares'] = ['sum', 'mean']
        
        # Aggregate by ticker and time
        aggregated = exploded_df.groupby('ticker').resample(time_freq).agg(agg_functions)
        
        # Flatten column names
        aggregated.columns = ['_'.join(col).strip() for col in aggregated.columns]
        
        # Reset index to get ticker and timestamp as columns
        aggregated = aggregated.reset_index()
        
        # Rename columns for clarity
        column_mapping = {
            'sentiment_score_mean': 'avg_sentiment',
            'sentiment_score_std': 'sentiment_volatility',
            'sentiment_score_count': 'sentiment_mentions',
            'content_count': 'post_count',
            'author_nunique': 'unique_authors'
        }
        
        aggregated.rename(columns=column_mapping, inplace=True)
        
        # Fill missing values
        aggregated['sentiment_volatility'].fillna(0, inplace=True)
        aggregated['avg_sentiment'].fillna(0.5, inplace=True)  # Neutral sentiment
        
        return aggregated
    
    def create_momentum_features(self, sentiment_df: pd.DataFrame) -> pd.DataFrame:
        """Create sentiment momentum and trend features"""
        
        if sentiment_df.empty:
            return sentiment_df
        
        # Sort by ticker and timestamp
        sentiment_df = sentiment_df.sort_values(['ticker', 'timestamp'])
        
        # Calculate rolling features for each lookback window
        for window in self.lookback_windows:
            
            # Rolling sentiment statistics
            sentiment_df[f'sentiment_ma_{window}'] = sentiment_df.groupby('ticker')['avg_sentiment'].transform(
                lambda x: x.rolling(window=window, min_periods=1).mean()
            )
            
            sentiment_df[f'sentiment_std_{window}'] = sentiment_df.groupby('ticker')['avg_sentiment'].transform(
                lambda x: x.rolling(window=window, min_periods=1).std()
            )
            
            # Sentiment momentum (rate of change)
            sentiment_df[f'sentiment_momentum_{window}'] = sentiment_df.groupby('ticker')['avg_sentiment'].transform(
                lambda x: x.pct_change(periods=window)
            )
            
            # Volume momentum (post count changes)
            if 'post_count' in sentiment_df.columns:
                sentiment_df[f'volume_momentum_{window}'] = sentiment_df.groupby('ticker')['post_count'].transform(
                    lambda x: x.pct_change(periods=window)
                )
            
            # Engagement momentum
            if 'unique_authors' in sentiment_df.columns:
                sentiment_df[f'engagement_momentum_{window}'] = sentiment_df.groupby('ticker')['unique_authors'].transform(
                    lambda x: x.pct_change(periods=window)
                )
        
        # Sentiment trend strength (linear regression slope)
        for window in [7, 14, 30]:
            if window <= len(sentiment_df):
                sentiment_df[f'sentiment_trend_{window}'] = sentiment_df.groupby('ticker')['avg_sentiment'].transform(
                    lambda x: x.rolling(window=window, min_periods=3).apply(
                        lambda y: np.polyfit(range(len(y)), y, 1)[0] if len(y) >= 3 else 0
                    )
                )
        
        return sentiment_df
    
    def create_cross_asset_features(self, sentiment_df: pd.DataFrame) -> pd.DataFrame:
        """Create features based on cross-asset sentiment correlations"""
        
        if sentiment_df.empty or len(sentiment_df['ticker'].unique()) < 2:
            return sentiment_df
        
        # Pivot to get tickers as columns
        pivot_sentiment = sentiment_df.pivot_table(
            index='timestamp', 
            columns='ticker', 
            values='avg_sentiment',
            fill_value=0.5
        )
        
        # Calculate correlation matrix
        correlation_matrix = pivot_sentiment.corr()
        
        # For each ticker, calculate average correlation with other tickers
        for ticker in sentiment_df['ticker'].unique():
            if ticker in correlation_matrix.columns:
                # Average correlation with other assets
                other_correlations = correlation_matrix[ticker].drop(ticker)
                avg_correlation = other_correlations.mean()
                
                # Add as feature
                sentiment_df.loc[sentiment_df['ticker'] == ticker, 'avg_cross_correlation'] = avg_correlation
        
        # Market-wide sentiment (average across all tickers)
        market_sentiment = sentiment_df.groupby('timestamp')['avg_sentiment'].mean()
        sentiment_df = sentiment_df.merge(
            market_sentiment.rename('market_sentiment').reset_index(),
            on='timestamp',
            how='left'
        )
        
        # Relative sentiment (ticker sentiment vs market)
        sentiment_df['relative_sentiment'] = sentiment_df['avg_sentiment'] - sentiment_df['market_sentiment']
        
        return sentiment_df
    
    def create_temporal_features(self, sentiment_df: pd.DataFrame) -> pd.DataFrame:
        """Create time-based features"""
        
        if sentiment_df.empty:
            return sentiment_df
        
        # Ensure timestamp is datetime
        sentiment_df['timestamp'] = pd.to_datetime(sentiment_df['timestamp'])
        
        # Day of week (0=Monday, 6=Sunday)
        sentiment_df['day_of_week'] = sentiment_df['timestamp'].dt.dayofweek
        
        # Hour of day
        sentiment_df['hour_of_day'] = sentiment_df['timestamp'].dt.hour
        
        # Is weekend
        sentiment_df['is_weekend'] = sentiment_df['day_of_week'].isin([5, 6]).astype(int)
        
        # Is trading hours (Tadawul: 10:00-15:00 Saudi time)
        sentiment_df['is_trading_hours'] = (
            (sentiment_df['hour_of_day'] >= 10) & 
            (sentiment_df['hour_of_day'] < 15) &
            (sentiment_df['day_of_week'] < 5)  # Weekdays only
        ).astype(int)
        
        # Month (for seasonality)
        sentiment_df['month'] = sentiment_df['timestamp'].dt.month
        
        # Days since start (for trend analysis)
        min_date = sentiment_df['timestamp'].min()
        sentiment_df['days_since_start'] = (sentiment_df['timestamp'] - min_date).dt.days
        
        return sentiment_df
    
    def create_volatility_features(self, sentiment_df: pd.DataFrame) -> pd.DataFrame:
        """Create sentiment volatility and regime features"""
        
        if sentiment_df.empty:
            return sentiment_df
        
        # Sort by ticker and timestamp
        sentiment_df = sentiment_df.sort_values(['ticker', 'timestamp'])
        
        # Rolling volatility of sentiment
        for window in [7, 14, 30]:
            sentiment_df[f'sentiment_volatility_{window}'] = sentiment_df.groupby('ticker')['avg_sentiment'].transform(
                lambda x: x.rolling(window=window, min_periods=2).std()
            )
        
        # Sentiment regime indicators
        # High volatility periods
        sentiment_df['high_volatility_regime'] = (
            sentiment_df['sentiment_volatility_7'] > sentiment_df['sentiment_volatility_7'].quantile(0.75)
        ).astype(int)
        
        # Extreme sentiment periods
        sentiment_df['extreme_bullish'] = (sentiment_df['avg_sentiment'] > 0.8).astype(int)
        sentiment_df['extreme_bearish'] = (sentiment_df['avg_sentiment'] < 0.2).astype(int)
        
        # Sentiment reversals (change in direction)
        sentiment_df['sentiment_reversal'] = sentiment_df.groupby('ticker')['avg_sentiment'].transform(
            lambda x: ((x.diff() > 0) != (x.diff().shift(1) > 0)).astype(int)
        )
        
        return sentiment_df
    
    def create_interaction_features(self, sentiment_df: pd.DataFrame) -> pd.DataFrame:
        """Create interaction features between different metrics"""
        
        if sentiment_df.empty:
            return sentiment_df
        
        # Sentiment-Volume interaction
        if 'post_count' in sentiment_df.columns:
            sentiment_df['sentiment_volume_interaction'] = (
                sentiment_df['avg_sentiment'] * sentiment_df['post_count']
            )
        
        # Sentiment-Volatility interaction
        if 'sentiment_volatility_7' in sentiment_df.columns:
            sentiment_df['sentiment_vol_interaction'] = (
                sentiment_df['avg_sentiment'] * sentiment_df['sentiment_volatility_7']
            )
        
        # Time-Sentiment interaction
        sentiment_df['weekend_sentiment'] = (
            sentiment_df['avg_sentiment'] * sentiment_df['is_weekend']
        )
        
        sentiment_df['trading_hours_sentiment'] = (
            sentiment_df['avg_sentiment'] * sentiment_df['is_trading_hours']
        )
        
        return sentiment_df
    
    def engineer_features(self, 
                         posts_df: pd.DataFrame,
                         time_freq: str = 'D') -> FeatureSet:
        """
        Complete feature engineering pipeline
        
        Args:
            posts_df: Raw posts DataFrame
            time_freq: Time frequency for aggregation
            
        Returns:
            FeatureSet with engineered features
        """
        if posts_df.empty:
            return FeatureSet(
                features=pd.DataFrame(),
                feature_names=[],
                metadata={'error': 'Empty input data'}
            )
        
        print("Starting feature engineering pipeline...")
        
        # Step 1: Aggregate sentiment by ticker
        print("1. Aggregating sentiment by ticker...")
        sentiment_df = self.aggregate_sentiment_by_ticker(posts_df, time_freq)
        
        if sentiment_df.empty:
            return FeatureSet(
                features=pd.DataFrame(),
                feature_names=[],
                metadata={'error': 'No ticker data found'}
            )
        
        # Step 2: Create momentum features
        print("2. Creating momentum features...")
        sentiment_df = self.create_momentum_features(sentiment_df)
        
        # Step 3: Create cross-asset features
        print("3. Creating cross-asset features...")
        sentiment_df = self.create_cross_asset_features(sentiment_df)
        
        # Step 4: Create temporal features
        print("4. Creating temporal features...")
        sentiment_df = self.create_temporal_features(sentiment_df)
        
        # Step 5: Create volatility features
        print("5. Creating volatility features...")
        sentiment_df = self.create_volatility_features(sentiment_df)
        
        # Step 6: Create interaction features
        print("6. Creating interaction features...")
        sentiment_df = self.create_interaction_features(sentiment_df)
        
        # Get feature names (exclude identifier columns)
        exclude_cols = ['ticker', 'timestamp']
        feature_names = [col for col in sentiment_df.columns if col not in exclude_cols]
        
        # Handle missing values
        sentiment_df[feature_names] = sentiment_df[feature_names].fillna(0)
        
        # Remove infinite values
        sentiment_df[feature_names] = sentiment_df[feature_names].replace([np.inf, -np.inf], 0)
        
        metadata = {
            'total_features': len(feature_names),
            'lookback_windows': self.lookback_windows,
            'time_frequency': time_freq,
            'unique_tickers': sentiment_df['ticker'].nunique(),
            'date_range': {
                'start': sentiment_df['timestamp'].min().isoformat() if not sentiment_df.empty else None,
                'end': sentiment_df['timestamp'].max().isoformat() if not sentiment_df.empty else None
            },
            'feature_categories': {
                'momentum': len([f for f in feature_names if 'momentum' in f]),
                'volatility': len([f for f in feature_names if 'volatility' in f]),
                'temporal': len([f for f in feature_names if any(t in f for t in ['day_', 'hour_', 'is_'])]),
                'cross_asset': len([f for f in feature_names if 'correlation' in f or 'market_' in f]),
                'interaction': len([f for f in feature_names if 'interaction' in f])
            }
        }
        
        print(f"Feature engineering completed. Generated {len(feature_names)} features.")
        
        return FeatureSet(
            features=sentiment_df,
            feature_names=feature_names,
            metadata=metadata
        )
    
    def fit_scaler(self, features_df: pd.DataFrame, feature_names: List[str]):
        """Fit feature scaler"""
        if not features_df.empty and feature_names:
            self.scaler.fit(features_df[feature_names])
            self.fitted = True
            print("Feature scaler fitted")
    
    def transform_features(self, features_df: pd.DataFrame, feature_names: List[str]) -> pd.DataFrame:
        """Scale features using fitted scaler"""
        if not self.fitted:
            print("Warning: Scaler not fitted. Fitting on current data.")
            self.fit_scaler(features_df, feature_names)
        
        if not features_df.empty and feature_names:
            scaled_features = self.scaler.transform(features_df[feature_names])
            features_df[feature_names] = scaled_features
        
        return features_df
    
    def select_features(self, 
                       features_df: pd.DataFrame, 
                       feature_names: List[str],
                       target: Optional[pd.Series] = None,
                       k: int = 50) -> Tuple[List[str], Dict]:
        """
        Select top k features using statistical tests
        
        Args:
            features_df: Features DataFrame
            feature_names: List of feature names
            target: Target variable for supervised selection
            k: Number of features to select
            
        Returns:
            Selected feature names and selection metadata
        """
        if target is None or features_df.empty:
            # Return top k features by variance if no target
            feature_vars = features_df[feature_names].var().sort_values(ascending=False)
            selected_features = feature_vars.head(k).index.tolist()
            
            return selected_features, {
                'method': 'variance',
                'selected_count': len(selected_features)
            }
        
        # Supervised feature selection
        selector = SelectKBest(score_func=f_regression, k=min(k, len(feature_names)))
        
        # Align features and target
        aligned_features = features_df[feature_names].dropna()
        aligned_target = target.loc[aligned_features.index]
        
        if len(aligned_features) == 0:
            return [], {'error': 'No valid feature-target pairs'}
        
        selector.fit(aligned_features, aligned_target)
        
        # Get selected feature names
        selected_mask = selector.get_support()
        selected_features = [feature_names[i] for i, selected in enumerate(selected_mask) if selected]
        
        # Get feature scores
        feature_scores = dict(zip(feature_names, selector.scores_))
        
        return selected_features, {
            'method': 'f_regression',
            'selected_count': len(selected_features),
            'feature_scores': feature_scores
        }
