"""
Artifact Bundle Manager for Saudi NLP Trading

Creates comprehensive bundles of run artifacts for analysis and debugging.
Includes manifest, metrics, logs, sample data, and debug HTML.
"""

import zipfile
import json
import shutil
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

from .logging import get_logger
from ..config.settings import get_config, RIYADH_TZ

class ArtifactBundleManager:
    """
    Manages creation and organization of run artifact bundles
    """
    
    def __init__(self, run_id: str):
        self.run_id = run_id
        self.config = get_config()
        self.logger = get_logger(__name__)
        
        # Bundle directory
        self.bundles_dir = Path("bundles")
        self.bundles_dir.mkdir(exist_ok=True)
        
        self.bundle_path = self.bundles_dir / f"run_{run_id}.zip"
        
        self.logger.info(
            "Initialized artifact bundle manager",
            run_id=run_id,
            bundle_path=str(self.bundle_path)
        )
    
    def create_bundle(self, manifest_file: Path, metrics_file: Path,
                     log_files: List[Path], sample_data_files: List[Path],
                     debug_html_dirs: List[Path] = None) -> Path:
        """
        Create comprehensive artifact bundle
        
        Args:
            manifest_file: Manifest JSON file
            metrics_file: Metrics JSON file
            log_files: List of log files
            sample_data_files: List of sample JSONL files
            debug_html_dirs: List of debug HTML directories
        
        Returns:
            Path to created bundle
        """
        
        try:
            with zipfile.ZipFile(self.bundle_path, 'w', zipfile.ZIP_DEFLATED) as bundle:
                
                # Add bundle metadata
                self._add_bundle_metadata(bundle)
                
                # Add manifest
                if manifest_file.exists():
                    bundle.write(manifest_file, f"manifest/{manifest_file.name}")
                    self.logger.debug("Added manifest to bundle", file=str(manifest_file))
                
                # Add metrics
                if metrics_file.exists():
                    bundle.write(metrics_file, f"metrics/{metrics_file.name}")
                    self.logger.debug("Added metrics to bundle", file=str(metrics_file))
                
                # Add log files
                for log_file in log_files:
                    if log_file.exists():
                        bundle.write(log_file, f"logs/{log_file.name}")
                        self.logger.debug("Added log to bundle", file=str(log_file))
                
                # Add sample data files
                for data_file in sample_data_files:
                    if data_file.exists():
                        # Preserve directory structure for data files
                        relative_path = self._get_relative_data_path(data_file)
                        bundle.write(data_file, f"data/{relative_path}")
                        self.logger.debug("Added data file to bundle", file=str(data_file))
                
                # Add debug HTML directories
                if debug_html_dirs:
                    for debug_dir in debug_html_dirs:
                        if debug_dir.exists() and debug_dir.is_dir():
                            self._add_directory_to_bundle(bundle, debug_dir, "debug_html")
                
                # Add configuration snapshot
                self._add_config_snapshot(bundle)
                
                # Add environment info
                self._add_environment_info(bundle)
            
            # Get bundle statistics
            bundle_stats = self._get_bundle_stats()
            
            self.logger.info(
                "Created artifact bundle",
                bundle_path=str(self.bundle_path),
                **bundle_stats
            )
            
            return self.bundle_path
            
        except Exception as e:
            self.logger.error(
                "Failed to create artifact bundle",
                bundle_path=str(self.bundle_path),
                exception_class=e.__class__.__name__,
                exc_info=True
            )
            raise
    
    def _add_bundle_metadata(self, bundle: zipfile.ZipFile) -> None:
        """Add bundle metadata file"""
        
        metadata = {
            'bundle_version': '1.0',
            'run_id': self.run_id,
            'created_at': datetime.now(RIYADH_TZ).isoformat(),
            'bundle_type': 'scrape_only_v0.5',
            'contents': {
                'manifest': 'Run manifest with file checksums',
                'metrics': 'Performance and quality metrics',
                'logs': 'Structured JSON logs',
                'data': 'Sample JSONL data partitions',
                'debug_html': 'HTML samples for selector debugging',
                'config': 'Configuration snapshot',
                'environment': 'Runtime environment info'
            },
            'schema': {
                'manifest_schema': '1.0',
                'metrics_schema': '1.0',
                'log_schema': '1.0',
                'data_schema': '1.0'
            }
        }
        
        metadata_json = json.dumps(metadata, indent=2, ensure_ascii=False)
        bundle.writestr("bundle_metadata.json", metadata_json)
    
    def _add_config_snapshot(self, bundle: zipfile.ZipFile) -> None:
        """Add configuration snapshot to bundle"""
        
        config_snapshot = {
            'run_id': self.run_id,
            'timestamp': datetime.now(RIYADH_TZ).isoformat(),
            'scraper_config': {
                'max_rpm': self.config.scraper.max_rpm,
                'max_concurrency': self.config.scraper.max_concurrency,
                'respect_robots': self.config.scraper.respect_robots,
                'user_agent': self.config.scraper.user_agent,
                'save_raw_html': self.config.scraper.save_raw_html
            },
            'storage_config': {
                'backend': self.config.storage.backend,
                'local_base_path': self.config.storage.local_base_path,
                'chunk_size_mb': self.config.storage.chunk_size_mb
            },
            'mode_config': {
                'scrape_only': self.config.mode.scrape_only,
                'nlp_enable': self.config.mode.nlp_enable,
                'dedup_enabled': self.config.mode.dedup_enabled,
                'language_detection_enabled': self.config.mode.language_detection_enabled
            },
            'rate_limit_config': {
                'window_seconds': self.config.rate_limit.window_seconds,
                'max_requests': self.config.rate_limit.max_requests,
                'base_delay': self.config.rate_limit.base_delay,
                'max_delay': self.config.rate_limit.max_delay,
                'jitter': self.config.rate_limit.jitter
            }
        }
        
        config_json = json.dumps(config_snapshot, indent=2, ensure_ascii=False)
        bundle.writestr("config/config_snapshot.json", config_json)
    
    def _add_environment_info(self, bundle: zipfile.ZipFile) -> None:
        """Add environment information to bundle"""
        
        import platform
        import sys
        import os
        
        env_info = {
            'run_id': self.run_id,
            'timestamp': datetime.now(RIYADH_TZ).isoformat(),
            'python': {
                'version': sys.version,
                'executable': sys.executable,
                'platform': sys.platform
            },
            'system': {
                'platform': platform.platform(),
                'machine': platform.machine(),
                'processor': platform.processor(),
                'hostname': platform.node(),
                'architecture': platform.architecture()
            },
            'environment_variables': {
                key: value for key, value in os.environ.items()
                if key.startswith(('MODE_', 'SCRAPER_', 'STORAGE_', 'LOG_', 'TZ'))
            },
            'working_directory': str(Path.cwd()),
            'timezone': str(RIYADH_TZ)
        }
        
        env_json = json.dumps(env_info, indent=2, ensure_ascii=False, default=str)
        bundle.writestr("environment/environment_info.json", env_json)
    
    def _add_directory_to_bundle(self, bundle: zipfile.ZipFile, 
                                directory: Path, bundle_prefix: str) -> None:
        """Add entire directory to bundle with prefix"""
        
        for file_path in directory.rglob("*"):
            if file_path.is_file():
                relative_path = file_path.relative_to(directory)
                bundle_path = f"{bundle_prefix}/{directory.name}/{relative_path}"
                bundle.write(file_path, bundle_path)
    
    def _get_relative_data_path(self, data_file: Path) -> str:
        """Get relative path for data file preserving structure"""
        
        # Try to find data directory in path
        parts = data_file.parts
        
        # Look for 'data' directory
        if 'data' in parts:
            data_index = parts.index('data')
            relative_parts = parts[data_index + 1:]
            return '/'.join(relative_parts)
        
        # Fallback to filename
        return data_file.name
    
    def _get_bundle_stats(self) -> Dict[str, Any]:
        """Get statistics about created bundle"""
        
        if not self.bundle_path.exists():
            return {}
        
        try:
            bundle_size = self.bundle_path.stat().st_size
            
            with zipfile.ZipFile(self.bundle_path, 'r') as bundle:
                file_count = len(bundle.namelist())
                
                # Categorize files
                categories = {
                    'manifest': 0,
                    'metrics': 0,
                    'logs': 0,
                    'data': 0,
                    'debug_html': 0,
                    'config': 0,
                    'other': 0
                }
                
                for filename in bundle.namelist():
                    if filename.startswith('manifest/'):
                        categories['manifest'] += 1
                    elif filename.startswith('metrics/'):
                        categories['metrics'] += 1
                    elif filename.startswith('logs/'):
                        categories['logs'] += 1
                    elif filename.startswith('data/'):
                        categories['data'] += 1
                    elif filename.startswith('debug_html/'):
                        categories['debug_html'] += 1
                    elif filename.startswith('config/') or filename.startswith('environment/'):
                        categories['config'] += 1
                    else:
                        categories['other'] += 1
            
            return {
                'bundle_size_bytes': bundle_size,
                'bundle_size_mb': round(bundle_size / 1024 / 1024, 2),
                'total_files': file_count,
                'file_categories': categories
            }
            
        except Exception as e:
            self.logger.warning(
                "Failed to get bundle stats",
                exception_class=e.__class__.__name__
            )
            return {}
    
    def extract_bundle(self, extract_dir: Path) -> Path:
        """
        Extract bundle to specified directory
        
        Args:
            extract_dir: Directory to extract bundle to
        
        Returns:
            Path to extracted directory
        """
        
        if not self.bundle_path.exists():
            raise FileNotFoundError(f"Bundle not found: {self.bundle_path}")
        
        extract_path = extract_dir / f"run_{self.run_id}"
        extract_path.mkdir(parents=True, exist_ok=True)
        
        try:
            with zipfile.ZipFile(self.bundle_path, 'r') as bundle:
                bundle.extractall(extract_path)
            
            self.logger.info(
                "Extracted bundle",
                bundle_path=str(self.bundle_path),
                extract_path=str(extract_path)
            )
            
            return extract_path
            
        except Exception as e:
            self.logger.error(
                "Failed to extract bundle",
                bundle_path=str(self.bundle_path),
                extract_path=str(extract_path),
                exception_class=e.__class__.__name__,
                exc_info=True
            )
            raise
    
    def validate_bundle(self) -> Dict[str, Any]:
        """
        Validate bundle contents and structure
        
        Returns:
            Validation results
        """
        
        if not self.bundle_path.exists():
            return {'valid': False, 'error': 'Bundle file not found'}
        
        try:
            validation = {
                'valid': True,
                'errors': [],
                'warnings': [],
                'contents': {}
            }
            
            with zipfile.ZipFile(self.bundle_path, 'r') as bundle:
                filenames = bundle.namelist()
                
                # Check required files
                required_files = ['bundle_metadata.json']
                for required_file in required_files:
                    if required_file not in filenames:
                        validation['errors'].append(f"Missing required file: {required_file}")
                
                # Check directory structure
                expected_dirs = ['manifest', 'metrics', 'logs', 'config']
                for expected_dir in expected_dirs:
                    dir_files = [f for f in filenames if f.startswith(f"{expected_dir}/")]
                    validation['contents'][expected_dir] = len(dir_files)
                    
                    if len(dir_files) == 0:
                        validation['warnings'].append(f"No files in {expected_dir}/ directory")
                
                # Validate JSON files
                json_files = [f for f in filenames if f.endswith('.json')]
                for json_file in json_files:
                    try:
                        content = bundle.read(json_file)
                        json.loads(content.decode('utf-8'))
                    except json.JSONDecodeError as e:
                        validation['errors'].append(f"Invalid JSON in {json_file}: {e}")
            
            validation['valid'] = len(validation['errors']) == 0
            
            return validation
            
        except Exception as e:
            return {
                'valid': False,
                'error': f"Bundle validation failed: {e}",
                'exception_class': e.__class__.__name__
            }

def create_run_bundle(run_id: str, manifest_file: Path, metrics_file: Path,
                     log_files: List[Path], sample_data_files: List[Path],
                     debug_html_dirs: List[Path] = None) -> Path:
    """
    Convenience function to create artifact bundle
    
    Returns:
        Path to created bundle
    """
    
    manager = ArtifactBundleManager(run_id)
    return manager.create_bundle(
        manifest_file=manifest_file,
        metrics_file=metrics_file,
        log_files=log_files,
        sample_data_files=sample_data_files,
        debug_html_dirs=debug_html_dirs or []
    )
