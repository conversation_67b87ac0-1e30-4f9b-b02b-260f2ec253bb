{"schema_version": "1.1", "runtime": {"run_id": "v12_complete_20250810_151541_04511c1c", "started_at": "2025-08-10T15:15:41.369562+03:00", "ended_at": "2025-08-10T15:15:41.375686+03:00", "duration_seconds": 0.01, "python_version": "3.12.10", "platform": "Windows-11-10.0.26100-SP0", "git_commit": "16484cd5e186f1866bce00dde3d869665b361904", "config_snapshot": {"requests_per_minute": 30, "rate_limit": {"capacity": 7, "refill_rate": 0.5}}}, "scraping_metrics": {"total_threads": 3, "total_posts": 20, "success_rate": 1.0}, "performance_metrics": {"total_processing_time_seconds": 0.006124258041381836, "posts_per_second": 3265.7017168217385}, "quality_metrics": {"arabic_posts_ratio": 1.0, "financial_content_ratio": 1.0}, "v12_soak_metrics": {"synthetic_records_generated": 100000, "synthetic_throughput_rec_per_sec": 19062.1, "peak_rss_mb": 22.3, "throttle_events": 2, "drift_alerts": 0}}