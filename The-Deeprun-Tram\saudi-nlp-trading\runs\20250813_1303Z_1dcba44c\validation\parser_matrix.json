{"run_id": "1dcba44c", "timestamp": "2025-08-13T13:03:33.883079+03:00", "fixtures_tested": 5, "total_violations": 0, "violation_rate": 0.0, "results": [{"fixture_name": "well_formed", "parsing_success": true, "schema_violations": 0, "record_created": true, "visible_text_length": 27, "language_detected": "ar"}, {"fixture_name": "missing_tags", "parsing_success": true, "schema_violations": 0, "record_created": true, "visible_text_length": 115, "language_detected": "ar"}, {"fixture_name": "mixed_encoding", "parsing_success": true, "schema_violations": 0, "record_created": true, "visible_text_length": 41, "language_detected": "ar"}, {"fixture_name": "malformed_nesting", "parsing_success": true, "schema_violations": 0, "record_created": true, "visible_text_length": 18, "language_detected": "ar"}, {"fixture_name": "empty_content", "parsing_success": true, "schema_violations": 0, "record_created": true, "visible_text_length": 0, "language_detected": "ar"}]}