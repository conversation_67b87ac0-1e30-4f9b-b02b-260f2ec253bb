"""
Utility for launching a stealth Playwright browser with custom fingerprints.

This helper encapsulates the logic for applying anti‑detection measures such
as modifying navigator properties, setting user agents, injecting scripts
and rotating proxies.  It can be used outside of the Hawamer scraper to
instantiate browsers for other Arabic forums.
"""

from playwright.async_api import async_playwright
from typing import Optional


class StealthBrowser:
    """
    Helper class to launch a headless browser with stealth configuration.
    """

    def __init__(self, user_agent: Optional[str] = None, locale: str = 'ar-SA',
                 timezone_id: str = 'Asia/Riyadh'):
        self.user_agent = user_agent
        self.locale = locale
        self.timezone_id = timezone_id
        self.playwright = None
        self.browser = None
        self.context = None

    async def start(self) -> None:
        """
        Launch the Playwright browser with stealth settings.
        """
        self.playwright = await async_playwright().start()
        args = [
            '--disable-blink-features=AutomationControlled',
            '--disable-dev-shm-usage',
            '--no-sandbox',
            '--disable-web-security',
        ]
        if self.user_agent:
            args.append(f'--user-agent={self.user_agent}')

        self.browser = await self.playwright.chromium.launch(headless=True, args=args)
        self.context = await self.browser.new_context(
            locale=self.locale,
            timezone_id=self.timezone_id
        )
        await self._inject_stealth()

    async def _inject_stealth(self) -> None:
        """
        Inject minimal scripts to mask automation.
        """
        stealth_js = """
        Object.defineProperty(navigator, 'webdriver', { get: () => undefined });
        """
        await self.context.add_init_script(stealth_js)

    async def new_page(self):
        """
        Open a new page in the stealth context.
        """
        if self.context is None:
            raise RuntimeError("Browser not started. Call start() first.")
        return await self.context.new_page()

    async def close(self) -> None:
        """
        Clean up resources.
        """
        if self.browser:
            await self.browser.close()
        if self.playwright:
            await self.playwright.stop()
