"""
Adapter for integrating FinBERT with the Saudi NLP Trading System.

This class wraps a financial sentiment model (e.g. FinBERT) and provides
simple interfaces for scoring Arabic text after optional translation or
fusion with dialect‑aware embeddings.
"""

from transformers import AutoModelForSequenceClassification, AutoTokenizer
import torch
from typing import List, Union


class FinBERTAdapter:
    """
    Loads a pre‑trained FinBERT model and computes sentiment scores.
    """

    def __init__(self, model_name: str = "yiyanghkust/finbert-tone"):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModelForSequenceClassification.from_pretrained(model_name)
        self.model.to(self.device)

    def score_texts(self, texts: Union[str, List[str]]) -> List[float]:
        """
        Compute sentiment scores for one or more texts.

        The score represents the probability of positive tone.
        """
        if isinstance(texts, str):
            texts = [texts]
        scores = []
        for text in texts:
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, padding=True).to(self.device)
            with torch.no_grad():
                outputs = self.model(**inputs)
            # Assuming index 2 corresponds to positive sentiment
            probabilities = torch.softmax(outputs.logits, dim=-1)
            score = probabilities[0][2].item()
            scores.append(score)
        return scores
