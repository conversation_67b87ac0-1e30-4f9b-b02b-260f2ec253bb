from camel_tools.utils.normalize import normalize_unicode
from camel_tools.utils.dediac import dediac_ar
from camel_tools.tokenizers.word import simple_word_tokenize
import re
from typing import List, Dict


class ArabicTextProcessor:
    """
    Comprehensive Arabic text preprocessing for Saudi dialect.

    This class performs Unicode normalisation, removes diacritics,
    collapses elongated characters, maps common slang to standard Arabic,
    tokenises text and extracts Tadawul ticker symbols.
    """

    def __init__(self):
        self.ticker_patterns = self._load_tadawul_tickers()
        self.slang_mapper = self._load_saudi_slang()

    def preprocess(self, text: str) -> Dict:
        """
        Full preprocessing pipeline.

        Args:
            text: Raw text from a forum post.

        Returns:
            A dictionary containing the cleaned text, token list, extracted tickers and original length.
        """
        # Basic normalisation
        text = normalize_unicode(text)
        text = dediac_ar(text)

        # Remove elongations (three or more consecutive identical characters)
        text = re.sub(r'(.)\1{2,}', r'\1', text)

        # Extract tickers before dialect normalisation
        tickers = self._extract_tickers(text)

        # Normalise Saudi dialect
        text = self._normalize_dialect(text)

        # Tokenise
        tokens = simple_word_tokenize(text)

        return {
            'clean_text': text,
            'tokens': tokens,
            'tickers': tickers,
            'original_length': len(text)
        }

    def _extract_tickers(self, text: str) -> List[str]:
        """
        Extract Tadawul ticker symbols from text.
        """
        # Pattern for four‑digit Saudi tickers
        ticker_pattern = r'\b\d{4}\b'
        potential_tickers = re.findall(ticker_pattern, text)

        # Validate against known tickers
        valid_tickers = [t for t in potential_tickers if t in self.ticker_patterns]

        return valid_tickers

    def _normalize_dialect(self, text: str) -> str:
        """
        Handle Saudi‑specific dialectal variations.
        """
        for slang, formal in self.slang_mapper.items():
            text = text.replace(slang, formal)
        return text

    def _load_tadawul_tickers(self) -> set:
        """
        Load valid Tadawul ticker codes.
        """
        # In a production environment this would load from a file or database
        return {
            '2222', '1214', '2010', '2020', '2030',  # Example tickers
            # ... full list should be inserted here ...
        }

    def _load_saudi_slang(self) -> Dict[str, str]:
        """
        Load a mapping of common Saudi slang to standard Arabic.
        """
        # A small sample mapping; extend with more terms as needed
        return {
            'وش': 'ما',
            'شلونك': 'كيف حالك',
            'تدري': 'تعلم',
        }
