[tool:pytest]
markers =
    golden_thread: marks tests as golden thread validation (CI critical)
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    slow: marks tests as slow running

testpaths = tests
python_files = test_golden_thread.py
python_classes = Test*
python_functions = test_*

addopts =
    -v
    --tb=short
    --disable-warnings

filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
