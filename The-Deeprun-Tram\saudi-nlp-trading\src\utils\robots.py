"""
Robots.txt Handler for Saudi NLP Trading

Fetches, caches, and respects robots.txt policies.
Implements proper user-agent matching and crawl delay handling.
"""

import time
import urllib.robotparser
from urllib.parse import urljoin, urlparse
from typing import Dict, Optional, Tu<PERSON>
from pathlib import Path
import pickle
from datetime import datetime, timedelta

from .logging import get_logger
from ..config.settings import get_config, RIYADH_TZ

class RobotsChecker:
    """
    Robots.txt checker with caching and proper policy enforcement
    """
    
    def __init__(self):
        self.config = get_config()
        self.logger = get_logger(__name__)
        self.cache: Dict[str, Tuple[urllib.robotparser.RobotFileParser, datetime]] = {}
        self.cache_duration = timedelta(hours=24)  # Cache robots.txt for 24 hours
        self.cache_file = Path('cache/robots_cache.pkl')
        self.cache_file.parent.mkdir(exist_ok=True)
        
        # Load cached robots.txt files
        self._load_cache()
    
    def can_fetch(self, url: str) -> Tuple[bool, str, Optional[float]]:
        """
        Check if URL can be fetched according to robots.txt
        
        Returns:
            (can_fetch: bool, policy: str, crawl_delay: Optional[float])
        """
        
        if not self.config.scraper.respect_robots:
            return True, "robots_disabled", None
        
        try:
            parsed_url = urlparse(url)
            base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
            
            # Get robots.txt parser
            rp, crawl_delay = self._get_robots_parser(base_url)
            
            if rp is None:
                return True, "no_robots_txt", None
            
            # Check if URL can be fetched
            can_fetch = rp.can_fetch(self.config.scraper.user_agent, url)
            
            if can_fetch:
                policy = "allowed"
            else:
                policy = "blocked"
                self.logger.warning(
                    "URL blocked by robots.txt",
                    url=url,
                    robot_policy=policy,
                    user_agent=self.config.scraper.user_agent
                )
            
            return can_fetch, policy, crawl_delay
            
        except Exception as e:
            self.logger.error(
                "Error checking robots.txt",
                url=url,
                exception_class=e.__class__.__name__,
                exc_info=True
            )
            return True, "error", None
    
    def _get_robots_parser(self, base_url: str) -> Tuple[Optional[urllib.robotparser.RobotFileParser], Optional[float]]:
        """
        Get robots.txt parser for base URL, with caching
        """
        
        # Check cache first
        if base_url in self.cache:
            rp, cached_time = self.cache[base_url]
            if datetime.now(RIYADH_TZ) - cached_time < self.cache_duration:
                crawl_delay = self._get_crawl_delay(rp)
                return rp, crawl_delay
        
        # Fetch robots.txt
        robots_url = urljoin(base_url, '/robots.txt')
        
        try:
            self.logger.info(
                "Fetching robots.txt",
                url=robots_url,
                stage="robots_fetch"
            )
            
            rp = urllib.robotparser.RobotFileParser()
            rp.set_url(robots_url)
            rp.read()
            
            # Cache the result
            self.cache[base_url] = (rp, datetime.now(RIYADH_TZ))
            self._save_cache()
            
            crawl_delay = self._get_crawl_delay(rp)
            
            self.logger.info(
                "Robots.txt fetched successfully",
                url=robots_url,
                crawl_delay=crawl_delay,
                stage="robots_fetch"
            )
            
            return rp, crawl_delay
            
        except Exception as e:
            self.logger.warning(
                "Failed to fetch robots.txt",
                url=robots_url,
                exception_class=e.__class__.__name__,
                stage="robots_fetch"
            )
            
            # Cache the failure to avoid repeated attempts
            self.cache[base_url] = (None, datetime.now(RIYADH_TZ))
            self._save_cache()
            
            return None, None
    
    def _get_crawl_delay(self, rp: urllib.robotparser.RobotFileParser) -> Optional[float]:
        """
        Extract crawl delay for our user agent
        """
        try:
            # Get crawl delay for our user agent
            crawl_delay = rp.crawl_delay(self.config.scraper.user_agent)
            
            if crawl_delay is not None:
                return float(crawl_delay)
            
            # Fallback to default crawl delay
            crawl_delay = rp.crawl_delay('*')
            if crawl_delay is not None:
                return float(crawl_delay)
            
            return None
            
        except Exception as e:
            self.logger.debug(
                "Error extracting crawl delay",
                exception_class=e.__class__.__name__
            )
            return None
    
    def _load_cache(self):
        """Load robots.txt cache from disk"""
        try:
            if self.cache_file.exists():
                with open(self.cache_file, 'rb') as f:
                    self.cache = pickle.load(f)
                
                self.logger.debug(
                    "Loaded robots.txt cache",
                    cached_domains=len(self.cache)
                )
        except Exception as e:
            self.logger.warning(
                "Failed to load robots.txt cache",
                exception_class=e.__class__.__name__
            )
            self.cache = {}
    
    def _save_cache(self):
        """Save robots.txt cache to disk"""
        try:
            with open(self.cache_file, 'wb') as f:
                pickle.dump(self.cache, f)
        except Exception as e:
            self.logger.warning(
                "Failed to save robots.txt cache",
                exception_class=e.__class__.__name__
            )
    
    def get_cache_stats(self) -> Dict[str, int]:
        """Get cache statistics"""
        now = datetime.now(RIYADH_TZ)
        
        total_entries = len(self.cache)
        valid_entries = 0
        expired_entries = 0
        
        for base_url, (rp, cached_time) in self.cache.items():
            if now - cached_time < self.cache_duration:
                valid_entries += 1
            else:
                expired_entries += 1
        
        return {
            'total_entries': total_entries,
            'valid_entries': valid_entries,
            'expired_entries': expired_entries
        }
    
    def clear_expired_cache(self):
        """Remove expired cache entries"""
        now = datetime.now(RIYADH_TZ)
        
        expired_keys = []
        for base_url, (rp, cached_time) in self.cache.items():
            if now - cached_time >= self.cache_duration:
                expired_keys.append(base_url)
        
        for key in expired_keys:
            del self.cache[key]
        
        if expired_keys:
            self._save_cache()
            self.logger.info(
                "Cleared expired robots.txt cache entries",
                expired_count=len(expired_keys)
            )

class CrawlDelayManager:
    """
    Manages crawl delays per domain based on robots.txt
    """
    
    def __init__(self, robots_checker: RobotsChecker):
        self.robots_checker = robots_checker
        self.logger = get_logger(__name__)
        self.last_request_time: Dict[str, float] = {}
    
    def wait_if_needed(self, url: str) -> float:
        """
        Wait if needed based on crawl delay for domain
        
        Returns:
            actual_wait_time: float (seconds waited)
        """
        
        try:
            parsed_url = urlparse(url)
            domain = parsed_url.netloc
            
            # Check robots.txt for crawl delay
            can_fetch, policy, crawl_delay = self.robots_checker.can_fetch(url)
            
            if not can_fetch:
                raise ValueError(f"URL blocked by robots.txt: {url}")
            
            # Use configured rate limit if no crawl delay specified
            if crawl_delay is None:
                crawl_delay = 60.0 / self.robots_checker.config.scraper.max_rpm
            
            # Check if we need to wait
            now = time.time()
            if domain in self.last_request_time:
                time_since_last = now - self.last_request_time[domain]
                
                if time_since_last < crawl_delay:
                    wait_time = crawl_delay - time_since_last
                    
                    self.logger.debug(
                        "Waiting for crawl delay",
                        url=url,
                        domain=domain,
                        crawl_delay=crawl_delay,
                        wait_time=wait_time
                    )
                    
                    time.sleep(wait_time)
                    actual_wait_time = wait_time
                else:
                    actual_wait_time = 0.0
            else:
                actual_wait_time = 0.0
            
            # Update last request time
            self.last_request_time[domain] = time.time()
            
            return actual_wait_time
            
        except Exception as e:
            self.logger.error(
                "Error in crawl delay management",
                url=url,
                exception_class=e.__class__.__name__,
                exc_info=True
            )
            return 0.0

# Global instances
_robots_checker: Optional[RobotsChecker] = None
_crawl_delay_manager: Optional[CrawlDelayManager] = None

def get_robots_checker() -> RobotsChecker:
    """Get global robots checker instance"""
    global _robots_checker
    if _robots_checker is None:
        _robots_checker = RobotsChecker()
    return _robots_checker

def get_crawl_delay_manager() -> CrawlDelayManager:
    """Get global crawl delay manager instance"""
    global _crawl_delay_manager
    if _crawl_delay_manager is None:
        robots_checker = get_robots_checker()
        _crawl_delay_manager = CrawlDelayManager(robots_checker)
    return _crawl_delay_manager
