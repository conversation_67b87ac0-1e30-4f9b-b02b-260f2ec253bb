"""
Graph analysis utilities for constructing reply networks and computing influence metrics.

Using NetworkX or igraph, this module will build directed graphs from forum threads,
compute centrality scores (e.g. PageRank) and detect echo chambers or influencer hubs.
"""

import networkx as nx
from typing import Dict, List


class GraphBuilder:
    """
    Constructs and analyses reply graphs from forum posts.
    """

    def __init__(self):
        self.graph = nx.DiGraph()

    def build_from_thread(self, thread_data: Dict) -> None:
        """
        Build a directed graph from thread data where nodes are authors and edges represent replies.
        """
        posts = thread_data.get('posts', [])
        for i in range(1, len(posts)):
            prev_author = posts[i - 1]['author']
            curr_author = posts[i]['author']
            if prev_author != curr_author:
                # Add or increment edge weight
                if self.graph.has_edge(prev_author, curr_author):
                    self.graph[prev_author][curr_author]['weight'] += 1
                else:
                    self.graph.add_edge(prev_author, curr_author, weight=1)

    def compute_influence(self) -> Dict[str, float]:
        """
        Compute a simple influence metric using PageRank.
        """
        pagerank = nx.pagerank(self.graph, weight='weight')
        return pagerank
