import pytest

from src.pipeline.orchestrator import TradingPipeline


@pytest.mark.asyncio
async def test_pipeline_initialisation():
    """
    Ensure the pipeline can be instantiated without errors.
    """
    pipeline = TradingPipeline()
    assert pipeline.scraper is not None
    assert pipeline.preprocessor is not None
    assert pipeline.embedder is not None
    assert pipeline.topic_modeler is not None
