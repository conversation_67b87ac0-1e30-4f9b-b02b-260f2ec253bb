"""
Structured Logging for Saudi NLP Trading

Provides JSON-structured logging with rotation, cloud-ready format,
and performance tracking. All logs include run_id, stage, and timing.
"""

import json
import logging
import logging.handlers
import time
import traceback
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, Union
from contextlib import contextmanager
import uuid

from ..config.settings import get_config, RIYADH_TZ

class StructuredFormatter(logging.Formatter):
    """JSON formatter for structured logging"""
    
    def __init__(self):
        super().__init__()
        self.config = get_config()
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON"""
        
        # Base log structure
        log_entry = {
            'ts': datetime.now(RIYADH_TZ).isoformat(),
            'level': record.levelname.lower(),
            'logger': record.name,
            'message': record.getMessage(),
        }
        
        # Add run_id if available
        if hasattr(record, 'run_id') or self.config.run_id:
            log_entry['run_id'] = getattr(record, 'run_id', self.config.run_id)
        
        # Add structured fields if present
        structured_fields = [
            'stage', 'event', 'url', 'thread_id', 'post_id',
            'attempt', 'http_status', 'elapsed_ms', 'bytes',
            'exception_class', 'retry_count', 'robot_policy'
        ]
        
        for field in structured_fields:
            if hasattr(record, field):
                log_entry[field] = getattr(record, field)
        
        # Add exception info if present
        if record.exc_info:
            log_entry['exception_class'] = record.exc_info[0].__name__
            log_entry['exception_message'] = str(record.exc_info[1])
            log_entry['traceback'] = traceback.format_exception(*record.exc_info)
        
        # Add cloud logging fields if enabled
        if self.config.cloud.logging_enabled:
            log_entry['severity'] = record.levelname
            log_entry['timestamp'] = log_entry['ts']
            
            # Add trace context if available
            if hasattr(record, 'trace_id'):
                log_entry['logging.googleapis.com/trace'] = f"projects/{self.config.storage.gcs_project_id}/traces/{record.trace_id}"
            
            if hasattr(record, 'span_id'):
                log_entry['logging.googleapis.com/spanId'] = record.span_id
        
        return json.dumps(log_entry, ensure_ascii=False, separators=(',', ':'))

class TextFormatter(logging.Formatter):
    """Human-readable formatter for development"""
    
    def __init__(self):
        super().__init__(
            fmt='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

class StructuredLogger:
    """Enhanced logger with structured logging capabilities"""
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.config = get_config()
        self._setup_logger()
    
    def _setup_logger(self):
        """Setup logger with appropriate handlers"""
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Set level
        level_map = {
            'debug': logging.DEBUG,
            'info': logging.INFO,
            'warning': logging.WARNING,
            'error': logging.ERROR
        }
        self.logger.setLevel(level_map[self.config.logging.level])
        
        # Create logs directory
        logs_dir = Path('logs')
        logs_dir.mkdir(exist_ok=True)
        
        # File handler with rotation
        if self.config.run_id:
            log_file = logs_dir / f'run_{self.config.run_id}.log'
        else:
            log_file = logs_dir / 'saudi_nlp_trading.log'
        
        # Parse rotate size
        rotate_size = self._parse_size(self.config.logging.rotate_size)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=rotate_size,
            backupCount=self.config.logging.backup_count,
            encoding='utf-8'
        )
        
        # Set formatter based on config
        if self.config.logging.format == 'json':
            file_handler.setFormatter(StructuredFormatter())
        else:
            file_handler.setFormatter(TextFormatter())
        
        self.logger.addHandler(file_handler)
        
        # Console handler for development
        if self.config.scraper.debug:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(TextFormatter())
            self.logger.addHandler(console_handler)
    
    def _parse_size(self, size_str: str) -> int:
        """Parse size string like '100MB' to bytes"""
        size_str = size_str.upper()
        
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)
    
    def log_structured(self, level: str, message: str, **kwargs):
        """Log with structured fields"""
        
        # Create log record
        record = self.logger.makeRecord(
            self.logger.name,
            getattr(logging, level.upper()),
            __file__,
            0,
            message,
            (),
            None
        )
        
        # Add structured fields
        for key, value in kwargs.items():
            setattr(record, key, value)
        
        self.logger.handle(record)
    
    def info(self, message: str, **kwargs):
        """Log info message with structured fields"""
        self.log_structured('info', message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message with structured fields"""
        self.log_structured('warning', message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error message with structured fields"""
        self.log_structured('error', message, **kwargs)
    
    def debug(self, message: str, **kwargs):
        """Log debug message with structured fields"""
        self.log_structured('debug', message, **kwargs)

class PerformanceTracker:
    """Track performance metrics for operations"""
    
    def __init__(self, logger: StructuredLogger):
        self.logger = logger
        self.metrics = {}
    
    @contextmanager
    def track_operation(self, operation: str, **context):
        """Context manager to track operation timing"""
        
        start_time = time.time()
        start_memory = self._get_memory_usage()
        
        try:
            yield
            
            # Success metrics
            elapsed_ms = (time.time() - start_time) * 1000
            memory_delta = self._get_memory_usage() - start_memory
            
            self.logger.info(
                f"Operation completed: {operation}",
                stage=operation,
                event='completed',
                elapsed_ms=round(elapsed_ms, 2),
                memory_delta_mb=round(memory_delta / 1024 / 1024, 2),
                **context
            )
            
            # Store metrics
            if operation not in self.metrics:
                self.metrics[operation] = []
            
            self.metrics[operation].append({
                'elapsed_ms': elapsed_ms,
                'memory_delta_mb': memory_delta / 1024 / 1024,
                'timestamp': datetime.now(RIYADH_TZ).isoformat(),
                'success': True
            })
            
        except Exception as e:
            # Error metrics
            elapsed_ms = (time.time() - start_time) * 1000
            
            self.logger.error(
                f"Operation failed: {operation}",
                stage=operation,
                event='failed',
                elapsed_ms=round(elapsed_ms, 2),
                exception_class=e.__class__.__name__,
                **context,
                exc_info=True
            )
            
            # Store error metrics
            if operation not in self.metrics:
                self.metrics[operation] = []
            
            self.metrics[operation].append({
                'elapsed_ms': elapsed_ms,
                'timestamp': datetime.now(RIYADH_TZ).isoformat(),
                'success': False,
                'error': str(e)
            })
            
            raise
    
    def _get_memory_usage(self) -> int:
        """Get current memory usage in bytes"""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss
        except ImportError:
            return 0
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get summary of tracked metrics"""
        summary = {}
        
        for operation, measurements in self.metrics.items():
            if not measurements:
                continue
            
            successful = [m for m in measurements if m['success']]
            failed = [m for m in measurements if not m['success']]
            
            if successful:
                elapsed_times = [m['elapsed_ms'] for m in successful]
                elapsed_times.sort()
                
                summary[operation] = {
                    'total_calls': len(measurements),
                    'successful_calls': len(successful),
                    'failed_calls': len(failed),
                    'success_rate': len(successful) / len(measurements),
                    'avg_elapsed_ms': sum(elapsed_times) / len(elapsed_times),
                    'p50_elapsed_ms': elapsed_times[len(elapsed_times) // 2],
                    'p95_elapsed_ms': elapsed_times[int(len(elapsed_times) * 0.95)],
                    'min_elapsed_ms': min(elapsed_times),
                    'max_elapsed_ms': max(elapsed_times)
                }
            else:
                summary[operation] = {
                    'total_calls': len(measurements),
                    'successful_calls': 0,
                    'failed_calls': len(failed),
                    'success_rate': 0.0
                }
        
        return summary

# Global logger instances
_loggers: Dict[str, StructuredLogger] = {}

def get_logger(name: str) -> StructuredLogger:
    """Get or create a structured logger"""
    if name not in _loggers:
        _loggers[name] = StructuredLogger(name)
    return _loggers[name]

def get_performance_tracker(logger_name: str) -> PerformanceTracker:
    """Get performance tracker for a logger"""
    logger = get_logger(logger_name)
    return PerformanceTracker(logger)
