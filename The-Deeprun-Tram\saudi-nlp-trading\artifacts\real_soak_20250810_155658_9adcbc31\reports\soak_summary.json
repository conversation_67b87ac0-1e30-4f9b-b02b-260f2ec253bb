{"run_id": "real_soak_20250810_155658_9adcbc31", "duration_seconds": 384.83811378479004, "total_requests": 2160, "total_records": 5040, "total_throttles": 0, "throughput_rec_per_sec": 13.096415920015861, "phase_breakdown": [{"phase": "Warmup", "duration_seconds": 15, "target_rpm": 10, "requests_made": 2, "records_generated": 6, "throttle_events": 0, "errors": 0}, {"phase": "Steady", "duration_seconds": 60, "target_rpm": 10, "requests_made": 10, "records_generated": 30, "throttle_events": 0, "errors": 0}, {"phase": "<PERSON><PERSON><PERSON>", "duration_seconds": 15, "target_rpm": 20, "requests_made": 5, "records_generated": 5, "throttle_events": 0, "errors": 0}, {"phase": "Cooldown", "duration_seconds": 10, "target_rpm": 10, "requests_made": 1, "records_generated": 1, "throttle_events": 0, "errors": 0}], "circuit_breaker_triggered": false, "abort_reason": null, "test_type": "compressed_demo_2hour_equivalent"}