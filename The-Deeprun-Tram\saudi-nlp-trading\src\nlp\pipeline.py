"""
Unified Arabic NLP Pipeline for Saudi Financial Sentiment Analysis

Orchestrates the complete pipeline from raw Arabic text to trading-ready features:
preprocessing → embeddings → NER → sentiment → vector storage → feature engineering
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
from pathlib import Path
import json
from datetime import datetime
import logging

# Import our NLP components
from .preprocessor import ArabicFinancialPreprocessor, ProcessingResult
from .embeddings import ArabicFinancialEmbedder, EmbeddingResult
from .ner import FinancialNER, NERResult
from .sentiment import FinancialSentimentAnalyzer, SentimentResult
from .vector_store import FinancialVectorStore, SearchResult
from .feature_engineering import FinancialFeatureEngineer, FeatureSet

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class PipelineResult:
    """Container for complete pipeline results"""
    processed_posts: pd.DataFrame
    embeddings: np.ndarray
    features: FeatureSet
    vector_store_ids: List[str]
    metadata: Dict
    performance_metrics: Dict


class ArabicFinancialNLPPipeline:
    """
    Complete NLP pipeline for Arabic financial content analysis
    
    Features:
    - End-to-end processing from raw text to trading signals
    - Configurable components and parameters
    - Batch and streaming processing modes
    - Performance monitoring and caching
    - Integration with vector database
    """
    
    def __init__(self, 
                 config_path: Optional[str] = None,
                 cache_dir: Optional[str] = None,
                 vector_store_config: Optional[Dict] = None):
        
        self.config = self._load_config(config_path)
        self.cache_dir = Path(cache_dir) if cache_dir else Path("cache/nlp")
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize components
        self._initialize_components(vector_store_config)
        
        # Performance tracking
        self.performance_metrics = {
            'total_processed': 0,
            'processing_times': {},
            'error_counts': {}
        }
        
        logger.info("Arabic Financial NLP Pipeline initialized")
    
    def _load_config(self, config_path: Optional[str]) -> Dict:
        """Load pipeline configuration"""
        default_config = {
            "preprocessing": {
                "normalize_unicode": True,
                "remove_diacritics": True,
                "normalize_dialect": True,
                "extract_entities": True,
                "min_token_length": 2
            },
            "embeddings": {
                "model_name": "aubmindlab/bert-base-arabertv2",
                "use_financial_model": True,
                "batch_size": 32,
                "normalize": True
            },
            "ner": {
                "model_name": "hatmimoha/arabic-ner",
                "use_rules": True,
                "confidence_threshold": 0.8
            },
            "sentiment": {
                "models": [
                    "aubmindlab/bert-base-arabertv2-twitter-ar-sentiment",
                    "CAMeL-Lab/bert-base-arabic-camelbert-sa-sentiment"
                ],
                "device": "auto"
            },
            "feature_engineering": {
                "lookback_windows": [1, 3, 7, 14, 30],
                "time_frequency": "D",
                "sentiment_decay": 0.9
            },
            "vector_store": {
                "collection_name": "saudi_financial_posts",
                "host": "localhost",
                "port": 6333
            }
        }
        
        if config_path and Path(config_path).exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                # Deep merge configurations
                for section, settings in user_config.items():
                    if section in default_config:
                        default_config[section].update(settings)
                    else:
                        default_config[section] = settings
        
        return default_config
    
    def _initialize_components(self, vector_store_config: Optional[Dict]):
        """Initialize all NLP components"""
        
        # Preprocessor
        self.preprocessor = ArabicFinancialPreprocessor()
        logger.info("Initialized Arabic preprocessor")
        
        # Embedder
        embeddings_config = self.config["embeddings"]
        self.embedder = ArabicFinancialEmbedder(
            dense_model=embeddings_config["model_name"],
            use_financial_model=embeddings_config["use_financial_model"],
            device=embeddings_config.get("device", "auto")
        )
        logger.info("Initialized embedding system")
        
        # NER
        ner_config = self.config["ner"]
        self.ner = FinancialNER(
            model_name=ner_config["model_name"],
            use_rules=ner_config["use_rules"],
            confidence_threshold=ner_config["confidence_threshold"]
        )
        logger.info("Initialized NER system")
        
        # Sentiment Analyzer
        sentiment_config = self.config["sentiment"]
        self.sentiment_analyzer = FinancialSentimentAnalyzer(
            models=sentiment_config["models"],
            device=sentiment_config.get("device", "auto")
        )
        logger.info("Initialized sentiment analyzer")
        
        # Feature Engineer
        fe_config = self.config["feature_engineering"]
        self.feature_engineer = FinancialFeatureEngineer(
            lookback_windows=fe_config["lookback_windows"],
            sentiment_decay=fe_config["sentiment_decay"]
        )
        logger.info("Initialized feature engineer")
        
        # Vector Store
        vs_config = vector_store_config or self.config["vector_store"]
        try:
            self.vector_store = FinancialVectorStore(
                collection_name=vs_config["collection_name"],
                host=vs_config.get("host", "localhost"),
                port=vs_config.get("port", 6333),
                api_key=vs_config.get("api_key"),
                url=vs_config.get("url")
            )
            logger.info("Initialized vector store")
        except Exception as e:
            logger.warning(f"Could not initialize vector store: {e}")
            self.vector_store = None
    
    def process_posts(self, posts: List[Dict]) -> pd.DataFrame:
        """
        Process raw posts through preprocessing, NER, and sentiment analysis
        
        Args:
            posts: List of post dictionaries with 'content' field
            
        Returns:
            DataFrame with processed posts and extracted features
        """
        if not posts:
            return pd.DataFrame()
        
        start_time = datetime.now()
        processed_data = []
        
        logger.info(f"Processing {len(posts)} posts...")
        
        for i, post in enumerate(posts):
            try:
                content = post.get('content', '')
                if not content or not content.strip():
                    continue
                
                # Step 1: Preprocessing
                preprocessing_result = self.preprocessor.process(content)
                
                # Step 2: NER
                ner_result = self.ner.extract_entities(preprocessing_result.clean_text)
                
                # Step 3: Sentiment Analysis
                sentiment_result = self.sentiment_analyzer.analyze_sentiment(
                    preprocessing_result.clean_text,
                    entities=ner_result.summary.get('companies', []) + ner_result.summary.get('tickers', [])
                )
                
                # Combine results
                processed_post = {
                    # Original post data
                    'post_id': post.get('post_id'),
                    'author': post.get('author'),
                    'timestamp': post.get('timestamp'),
                    'source': post.get('source', 'unknown'),
                    'url': post.get('url'),
                    'likes': post.get('likes', 0),
                    'shares': post.get('shares', 0),
                    
                    # Processed content
                    'original_content': content,
                    'clean_content': preprocessing_result.clean_text,
                    'tokens': preprocessing_result.tokens,
                    'token_count': len(preprocessing_result.tokens),
                    
                    # Entities
                    'companies': ner_result.summary.get('companies', []),
                    'tickers': ner_result.summary.get('tickers', []),
                    'financial_terms': ner_result.summary.get('instruments', []),
                    'monetary_amounts': ner_result.summary.get('money', []),
                    'percentages': ner_result.summary.get('percentages', []),
                    
                    # Sentiment
                    'sentiment': sentiment_result.overall_sentiment,
                    'sentiment_score': sentiment_result.scores.get('ensemble', 0.5),
                    'sentiment_confidence': sentiment_result.confidence,
                    'lexicon_score': sentiment_result.lexicon_score,
                    'sentiment_indicators': sentiment_result.indicators,
                    'aspect_sentiments': sentiment_result.aspect_sentiments,
                    
                    # Metadata
                    'processing_metadata': {
                        'preprocessing_steps': preprocessing_result.metadata.get('processing_steps', []),
                        'entity_count': ner_result.metadata.get('total_entities', 0),
                        'sentiment_models': sentiment_result.metadata.get('model_count', 0)
                    }
                }
                
                processed_data.append(processed_post)
                
                # Progress logging
                if (i + 1) % 100 == 0:
                    logger.info(f"Processed {i + 1}/{len(posts)} posts")
                    
            except Exception as e:
                logger.error(f"Error processing post {i}: {e}")
                self.performance_metrics['error_counts']['processing'] = \
                    self.performance_metrics['error_counts'].get('processing', 0) + 1
                continue
        
        # Convert to DataFrame
        df = pd.DataFrame(processed_data)
        
        # Record performance
        processing_time = (datetime.now() - start_time).total_seconds()
        self.performance_metrics['processing_times']['posts'] = processing_time
        self.performance_metrics['total_processed'] += len(processed_data)
        
        logger.info(f"Processed {len(processed_data)} posts in {processing_time:.2f} seconds")
        
        return df
    
    def generate_embeddings(self, processed_df: pd.DataFrame) -> np.ndarray:
        """Generate embeddings for processed posts"""
        if processed_df.empty:
            return np.array([])
        
        start_time = datetime.now()
        
        # Extract clean content for embedding
        texts = processed_df['clean_content'].tolist()
        
        logger.info(f"Generating embeddings for {len(texts)} texts...")
        
        try:
            # Generate dense embeddings
            embeddings = self.embedder.encode_dense(
                texts,
                batch_size=self.config["embeddings"]["batch_size"],
                normalize=self.config["embeddings"]["normalize"]
            )
            
            # Record performance
            embedding_time = (datetime.now() - start_time).total_seconds()
            self.performance_metrics['processing_times']['embeddings'] = embedding_time
            
            logger.info(f"Generated {embeddings.shape} embeddings in {embedding_time:.2f} seconds")
            
            return embeddings
            
        except Exception as e:
            logger.error(f"Error generating embeddings: {e}")
            self.performance_metrics['error_counts']['embeddings'] = \
                self.performance_metrics['error_counts'].get('embeddings', 0) + 1
            return np.array([])
    
    def store_in_vector_db(self, 
                          processed_df: pd.DataFrame, 
                          embeddings: np.ndarray) -> List[str]:
        """Store processed posts and embeddings in vector database"""
        if self.vector_store is None or processed_df.empty or len(embeddings) == 0:
            return []
        
        start_time = datetime.now()
        
        # Convert DataFrame to documents format
        documents = processed_df.to_dict('records')
        
        logger.info(f"Storing {len(documents)} documents in vector database...")
        
        try:
            success = self.vector_store.add_documents(documents, embeddings)
            
            if success:
                # Generate document IDs (simplified - in practice, get from vector store)
                doc_ids = [f"doc_{i}" for i in range(len(documents))]
                
                # Record performance
                storage_time = (datetime.now() - start_time).total_seconds()
                self.performance_metrics['processing_times']['vector_storage'] = storage_time
                
                logger.info(f"Stored documents in {storage_time:.2f} seconds")
                return doc_ids
            else:
                logger.error("Failed to store documents in vector database")
                return []
                
        except Exception as e:
            logger.error(f"Error storing in vector database: {e}")
            self.performance_metrics['error_counts']['vector_storage'] = \
                self.performance_metrics['error_counts'].get('vector_storage', 0) + 1
            return []
    
    def engineer_features(self, processed_df: pd.DataFrame) -> FeatureSet:
        """Engineer trading features from processed posts"""
        if processed_df.empty:
            return FeatureSet(
                features=pd.DataFrame(),
                feature_names=[],
                metadata={'error': 'Empty input data'}
            )
        
        start_time = datetime.now()
        
        logger.info("Engineering trading features...")
        
        try:
            # Engineer features
            feature_set = self.feature_engineer.engineer_features(
                processed_df,
                time_freq=self.config["feature_engineering"]["time_frequency"]
            )
            
            # Record performance
            feature_time = (datetime.now() - start_time).total_seconds()
            self.performance_metrics['processing_times']['feature_engineering'] = feature_time
            
            logger.info(f"Engineered {len(feature_set.feature_names)} features in {feature_time:.2f} seconds")
            
            return feature_set
            
        except Exception as e:
            logger.error(f"Error engineering features: {e}")
            self.performance_metrics['error_counts']['feature_engineering'] = \
                self.performance_metrics['error_counts'].get('feature_engineering', 0) + 1
            return FeatureSet(
                features=pd.DataFrame(),
                feature_names=[],
                metadata={'error': str(e)}
            )
    
    def run_pipeline(self, posts: List[Dict]) -> PipelineResult:
        """
        Run complete NLP pipeline
        
        Args:
            posts: List of raw post dictionaries
            
        Returns:
            PipelineResult with all outputs
        """
        pipeline_start = datetime.now()
        
        logger.info(f"Starting complete NLP pipeline for {len(posts)} posts")
        
        # Step 1: Process posts (preprocessing + NER + sentiment)
        processed_df = self.process_posts(posts)
        
        if processed_df.empty:
            return PipelineResult(
                processed_posts=pd.DataFrame(),
                embeddings=np.array([]),
                features=FeatureSet(pd.DataFrame(), [], {'error': 'No posts processed'}),
                vector_store_ids=[],
                metadata={'error': 'No posts processed'},
                performance_metrics=self.performance_metrics
            )
        
        # Step 2: Generate embeddings
        embeddings = self.generate_embeddings(processed_df)
        
        # Step 3: Store in vector database
        vector_store_ids = self.store_in_vector_db(processed_df, embeddings)
        
        # Step 4: Engineer features
        features = self.engineer_features(processed_df)
        
        # Calculate total pipeline time
        total_time = (datetime.now() - pipeline_start).total_seconds()
        self.performance_metrics['processing_times']['total_pipeline'] = total_time
        
        # Compile metadata
        metadata = {
            'pipeline_version': '1.0',
            'total_posts_input': len(posts),
            'total_posts_processed': len(processed_df),
            'processing_success_rate': len(processed_df) / len(posts) if posts else 0,
            'total_processing_time': total_time,
            'embeddings_shape': embeddings.shape if len(embeddings) > 0 else (0, 0),
            'features_generated': len(features.feature_names),
            'vector_store_success': len(vector_store_ids) > 0,
            'timestamp': datetime.now().isoformat()
        }
        
        logger.info(f"Pipeline completed in {total_time:.2f} seconds")
        logger.info(f"Success rate: {metadata['processing_success_rate']:.2%}")
        
        return PipelineResult(
            processed_posts=processed_df,
            embeddings=embeddings,
            features=features,
            vector_store_ids=vector_store_ids,
            metadata=metadata,
            performance_metrics=self.performance_metrics
        )
    
    def save_results(self, result: PipelineResult, output_dir: str):
        """Save pipeline results to disk"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save processed posts
        if not result.processed_posts.empty:
            posts_file = output_path / f"processed_posts_{timestamp}.parquet"
            result.processed_posts.to_parquet(posts_file)
            logger.info(f"Saved processed posts to {posts_file}")
        
        # Save embeddings
        if len(result.embeddings) > 0:
            embeddings_file = output_path / f"embeddings_{timestamp}.npy"
            np.save(embeddings_file, result.embeddings)
            logger.info(f"Saved embeddings to {embeddings_file}")
        
        # Save features
        if not result.features.features.empty:
            features_file = output_path / f"features_{timestamp}.parquet"
            result.features.features.to_parquet(features_file)
            logger.info(f"Saved features to {features_file}")
        
        # Save metadata
        metadata_file = output_path / f"pipeline_metadata_{timestamp}.json"
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump({
                'metadata': result.metadata,
                'performance_metrics': result.performance_metrics,
                'feature_metadata': result.features.metadata
            }, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Saved metadata to {metadata_file}")
    
    def get_performance_summary(self) -> Dict:
        """Get performance summary"""
        return {
            'total_processed': self.performance_metrics['total_processed'],
            'processing_times': self.performance_metrics['processing_times'],
            'error_counts': self.performance_metrics['error_counts'],
            'average_processing_time': (
                self.performance_metrics['processing_times'].get('total_pipeline', 0) /
                max(1, self.performance_metrics['total_processed'])
            )
        }
