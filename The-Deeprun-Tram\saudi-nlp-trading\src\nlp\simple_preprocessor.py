"""
Simple but Effective Arabic Preprocessor for Financial Text

Focus: Clean Arabic text properly for embeddings and analysis.
No over-engineering - just what works for trading signals.
"""

import re
import unicodedata
from typing import Dict, List, Tuple
import hashlib

class ArabicPreprocessor:
    """
    Simple Arabic preprocessor focused on financial content
    
    Key principles:
    1. Handle Arabic text complexity properly
    2. Extract financial entities accurately  
    3. Preserve sentiment-bearing content
    4. Fast processing for real-time use
    """
    
    def __init__(self):
        # Saudi company ticker mapping (top 50 most traded)
        self.company_tickers = {
            # Banks
            'الراجحي': '1120', 'الأهلي': '1180', 'الرياض': '1010', 'سامبا': '1090',
            'البلاد': '1140', 'الجزيرة': '4200', 'الإنماء': '1150',
            
            # Petrochemicals  
            'سابك': '2010', 'ينبع': '2290', 'المتقدمة': '2330', 'كيان': '2350',
            'سبكيم': '2380', 'بترو رابغ': '2380', 'الصحراء': '2310',
            
            # Energy
            'أرامكو': '2222', 'الكهرباء': '5110', 'أكوا باور': '2082',
            
            # Telecom
            'الاتصالات': '7010', 'موبايلي': '7020', 'زين': '7030',
            
            # Retail
            'العثيم': '4001', 'بن داود': '4008', 'جرير': '4190',
            'الدريس': '4200', 'فتيحي': '4240',
            
            # Real Estate
            'دار الأركان': '4320', 'جبل عمر': '4250', 'طيبة': '4090',
            'مكة': '4140', 'التعمير': '4030',
            
            # Healthcare
            'دله': '4004', 'المواساة': '4002', 'سليمان الحبيب': '4013',
            
            # Industrial
            'معادن': '1211', 'أسمنت العربية': '3010', 'يانساب': '2001',
            'الأسمنت السعودية': '3020', 'أسمنت اليمامة': '3030'
        }
        
        # Reverse mapping
        self.ticker_companies = {v: k for k, v in self.company_tickers.items()}
        
        # Financial sentiment terms (proven to correlate with price movements)
        self.bullish_terms = {
            'صاعد', 'ارتفاع', 'قوي', 'ممتاز', 'فرصة', 'شراء', 'استثمار',
            'نمو', 'أرباح', 'إيجابي', 'اختراق', 'هدف', 'انطلاق', 'قفزة'
        }
        
        self.bearish_terms = {
            'هابط', 'انخفاض', 'ضعيف', 'خسارة', 'بيع', 'تصريف', 'سلبي',
            'تراجع', 'هبوط', 'انهيار', 'مقاومة', 'ضغط', 'تصحيح'
        }
        
        print(f"Initialized preprocessor with {len(self.company_tickers)} companies")
    
    def normalize_arabic(self, text: str) -> str:
        """
        Core Arabic normalization - essential for consistent processing
        """
        if not text:
            return ""
        
        # Unicode normalization
        text = unicodedata.normalize('NFKC', text)
        
        # Remove diacritics (تشكيل)
        diacritics = 'ًٌٍَُِّْ'
        for d in diacritics:
            text = text.replace(d, '')
        
        # Normalize hamza variations
        text = re.sub(r'[أإآ]', 'ا', text)
        text = re.sub(r'[ؤ]', 'و', text)
        text = re.sub(r'[ئ]', 'ي', text)
        text = re.sub(r'[ة]', 'ه', text)
        
        # Remove tatweel (elongation)
        text = re.sub(r'ـ+', '', text)
        
        # Fix common elongations in social media
        text = re.sub(r'(.)\1{2,}', r'\1', text)
        
        # Convert Arabic-Indic numerals
        arabic_nums = '٠١٢٣٤٥٦٧٨٩'
        western_nums = '0123456789'
        for ar, we in zip(arabic_nums, western_nums):
            text = text.replace(ar, we)
        
        # Normalize whitespace
        text = ' '.join(text.split())
        
        return text
    
    def extract_financial_entities(self, text: str) -> Dict[str, List[str]]:
        """
        Extract financial entities that matter for trading signals
        """
        entities = {
            'companies': [],
            'tickers': [],
            'amounts': [],
            'percentages': []
        }
        
        # Extract companies mentioned
        for company, ticker in self.company_tickers.items():
            if company in text:
                entities['companies'].append(company)
                entities['tickers'].append(ticker)
        
        # Extract 4-digit tickers directly mentioned
        ticker_matches = re.findall(r'\b\d{4}\b', text)
        for ticker in ticker_matches:
            if ticker in self.ticker_companies and ticker not in entities['tickers']:
                entities['tickers'].append(ticker)
                entities['companies'].append(self.ticker_companies[ticker])
        
        # Extract percentages (critical for sentiment context)
        percentages = re.findall(r'[+-]?\d+\.?\d*\s*%', text)
        entities['percentages'] = percentages
        
        # Extract monetary amounts
        amount_patterns = [
            r'\d+\.?\d*\s*ريال',
            r'\d+\.?\d*\s*ر\.س',
            r'\d+\.?\d*\s*مليون',
            r'\d+\.?\d*\s*مليار'
        ]
        
        for pattern in amount_patterns:
            amounts = re.findall(pattern, text, re.IGNORECASE)
            entities['amounts'].extend(amounts)
        
        return entities
    
    def calculate_sentiment_score(self, text: str) -> Tuple[float, List[str]]:
        """
        Simple but effective sentiment scoring based on financial lexicon
        Returns: (score between 0-1, list of found indicators)
        """
        words = set(text.split())
        
        bullish_found = words.intersection(self.bullish_terms)
        bearish_found = words.intersection(self.bearish_terms)
        
        bullish_count = len(bullish_found)
        bearish_count = len(bearish_found)
        
        # Simple scoring
        if bullish_count == 0 and bearish_count == 0:
            score = 0.5  # Neutral
        else:
            # Score based on ratio
            total = bullish_count + bearish_count
            score = bullish_count / total
        
        indicators = [f"bullish:{term}" for term in bullish_found] + \
                    [f"bearish:{term}" for term in bearish_found]
        
        return score, indicators
    
    def clean_for_embedding(self, text: str) -> str:
        """
        Clean text specifically for embedding generation
        """
        # Start with normalized text
        clean = self.normalize_arabic(text)
        
        # Remove URLs
        clean = re.sub(r'http[s]?://\S+', '', clean)
        clean = re.sub(r'www\.\S+', '', clean)
        
        # Remove mentions but keep the name
        clean = re.sub(r'@(\w+)', r'\1', clean)
        
        # Remove hashtags but keep content
        clean = re.sub(r'#(\w+)', r'\1', clean)
        
        # Remove excessive punctuation
        clean = re.sub(r'([!؟?]){2,}', r'\1', clean)
        clean = re.sub(r'([.]){2,}', '.', clean)
        
        # Remove emojis and special characters
        clean = re.sub(r'[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s\d\.\-+%]', ' ', clean)
        
        # Final whitespace cleanup
        clean = ' '.join(clean.split())
        
        return clean.strip()
    
    def anonymize_user(self, username: str) -> str:
        """
        Anonymize usernames for PDPL compliance
        """
        if not username or username == 'unknown':
            return 'anonymous'
        
        # SHA-256 hash for consistent anonymization
        return hashlib.sha256(username.encode('utf-8')).hexdigest()[:12]
    
    def process_post(self, post_content: str, author: str = None) -> Dict:
        """
        Complete processing of a single post
        Returns all extracted information needed for analysis
        """
        if not post_content or len(post_content.strip()) < 5:
            return {
                'valid': False,
                'reason': 'Empty or too short content'
            }
        
        # Normalize text
        normalized = self.normalize_arabic(post_content)
        
        # Clean for embedding
        clean_text = self.clean_for_embedding(normalized)
        
        # Extract entities
        entities = self.extract_financial_entities(normalized)
        
        # Calculate sentiment
        sentiment_score, sentiment_indicators = self.calculate_sentiment_score(normalized)
        
        # Determine sentiment category
        if sentiment_score > 0.6:
            sentiment_label = 'bullish'
        elif sentiment_score < 0.4:
            sentiment_label = 'bearish'
        else:
            sentiment_label = 'neutral'
        
        # Anonymize author
        anon_author = self.anonymize_user(author) if author else 'anonymous'
        
        return {
            'valid': True,
            'original_text': post_content,
            'normalized_text': normalized,
            'clean_text': clean_text,
            'entities': entities,
            'sentiment_score': sentiment_score,
            'sentiment_label': sentiment_label,
            'sentiment_indicators': sentiment_indicators,
            'author_hash': anon_author,
            'has_financial_content': bool(entities['companies'] or entities['tickers']),
            'text_length': len(clean_text),
            'word_count': len(clean_text.split())
        }
    
    def process_batch(self, posts: List[Dict]) -> List[Dict]:
        """
        Process multiple posts efficiently
        """
        results = []
        
        for post in posts:
            content = post.get('content', '')
            author = post.get('author', 'unknown')
            
            processed = self.process_post(content, author)
            
            if processed['valid']:
                # Add original post metadata
                processed.update({
                    'post_id': post.get('post_id'),
                    'timestamp': post.get('timestamp'),
                    'source': post.get('source', 'hawamer'),
                    'url': post.get('url'),
                    'likes': post.get('likes', 0),
                    'shares': post.get('shares', 0)
                })
                
                results.append(processed)
        
        return results
    
    def get_stats(self, processed_posts: List[Dict]) -> Dict:
        """
        Get processing statistics for monitoring
        """
        if not processed_posts:
            return {'error': 'No processed posts'}
        
        total_posts = len(processed_posts)
        financial_posts = sum(1 for p in processed_posts if p.get('has_financial_content', False))
        
        sentiment_dist = {}
        for post in processed_posts:
            label = post.get('sentiment', post.get('sentiment_label', 'unknown'))
            sentiment_dist[label] = sentiment_dist.get(label, 0) + 1
        
        # Most mentioned companies
        company_counts = {}
        for post in processed_posts:
            companies = post.get('companies', post.get('entities', {}).get('companies', []))
            for company in companies:
                company_counts[company] = company_counts.get(company, 0) + 1
        
        top_companies = sorted(company_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        
        return {
            'total_posts': total_posts,
            'financial_posts': financial_posts,
            'financial_ratio': financial_posts / total_posts if total_posts > 0 else 0,
            'sentiment_distribution': sentiment_dist,
            'top_companies': top_companies,
            'avg_text_length': sum(p.get('text_length', 0) for p in processed_posts) / total_posts,
            'avg_word_count': sum(p.get('word_count', 0) for p in processed_posts) / total_posts
        }
