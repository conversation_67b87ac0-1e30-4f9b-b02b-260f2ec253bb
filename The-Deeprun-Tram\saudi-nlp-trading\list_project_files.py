import os

def list_files(start_path='.'):
    for root, dirs, files in os.walk(start_path):
        level = root.replace(start_path, '').count(os.sep)
        indent = ' ' * 4 * level
        print(f"{indent}{os.path.basename(root)}/")
        subindent = ' ' * 4 * (level + 1)
        for f in files:
            if f.endswith('.py') or f.endswith('.md'):
                print(f"{subindent}{f}")

if __name__ == "__main__":
    list_files('.')