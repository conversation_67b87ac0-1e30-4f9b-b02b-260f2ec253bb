from sentence_transformers import SentenceTransformer
import torch
import numpy as np
from typing import List, Union


class SwanEmbedder:
    """
    Swan dialect‑aware Arabic embedding model integration.

    If the Swan model cannot be loaded (e.g. offline or unavailable),
    falls back to a multilingual MiniLM model.
    """

    def __init__(self, model_name: str = "Omartificial-Intelligence-Space/Swan-7b"):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        # Attempt to load Swan; fallback to miniLM if necessary
        try:
            self.model = SentenceTransformer(model_name)
            self.model.to(self.device)
        except Exception:
            self.model = SentenceTransformer('sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2')

    def embed_texts(self, texts: Union[str, List[str]], batch_size: int = 32) -> np.ndarray:
        """
        Generate Swan embeddings for Arabic text.

        Args:
            texts: A string or list of strings to embed.
            batch_size: Number of samples per batch during encoding.

        Returns:
            A numpy array of normalised embeddings.
        """
        if isinstance(texts, str):
            texts = [texts]

        embeddings = self.model.encode(
            texts,
            batch_size=batch_size,
            show_progress_bar=False,
            convert_to_numpy=True,
            normalize_embeddings=True
        )

        return embeddings

    def compute_similarity(self, text1: str, text2: str) -> float:
        """
        Compute cosine similarity between two texts using Swan embeddings.
        """
        emb1 = self.embed_texts(text1)
        emb2 = self.embed_texts(text2)
        return float(np.dot(emb1[0], emb2[0]))
