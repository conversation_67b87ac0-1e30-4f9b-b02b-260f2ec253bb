"""Proxy management utilities for rotating IP addresses.

This module manages a list of proxies and exposes a simple interface to
retrieve the next proxy in the rotation.  It can be extended to fetch
proxies from external providers or rotate based on usage.
"""

from typing import List, Optional


class ProxyManager:
    """Manage a list of proxies and return them in a round‑robin fashion."""

    def __init__(self, proxies: Optional[List[str]] = None):
        self.proxies = proxies or []
        self.index = 0

    def get_next_proxy(self) -> Optional[str]:
        """Return the next proxy in the list, or None if no proxies are available."""
        if not self.proxies:
            return None
        proxy = self.proxies[self.index % len(self.proxies)]
        self.index += 1
        return proxy
