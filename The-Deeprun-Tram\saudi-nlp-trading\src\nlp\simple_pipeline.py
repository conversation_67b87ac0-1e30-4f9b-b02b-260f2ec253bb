"""
Simple but Effective NLP Pipeline

Focus: Convert Arabic financial text to trading signals.
Measure everything, optimize based on actual performance.
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Tuple
import json
from datetime import datetime, timedelta
from pathlib import Path
import time

from .simple_preprocessor import ArabicPreprocessor
from .simple_embeddings import SimpleEmbedder
from .simple_sentiment import SimpleSentimentAnalyzer

class SimpleNLPPipeline:
    """
    Complete NLP pipeline for Arabic financial content
    
    Design principles:
    1. Start simple, measure everything
    2. Focus on trading signal quality
    3. Optimize based on actual performance
    4. Fast processing for real-time use
    """
    
    def __init__(self, 
                 cache_dir: str = "cache/nlp",
                 enable_embeddings: bool = True,
                 enable_model_sentiment: bool = True):
        
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        print("Initializing Simple NLP Pipeline...")
        
        # Initialize components
        self.preprocessor = ArabicPreprocessor()
        
        self.embedder = None
        if enable_embeddings:
            self.embedder = SimpleEmbedder(cache_dir=str(self.cache_dir / "embeddings"))
        
        self.sentiment_analyzer = SimpleSentimentAnalyzer(use_model=enable_model_sentiment)
        
        # Performance tracking
        self.stats = {
            'total_posts_processed': 0,
            'total_processing_time': 0.0,
            'financial_posts_found': 0,
            'embeddings_generated': 0,
            'sentiment_predictions': 0
        }
        
        print("Simple NLP Pipeline initialized successfully!")
    
    def process_single_post(self, post: Dict) -> Optional[Dict]:
        """
        Process a single post through the complete pipeline
        """
        start_time = time.time()
        
        # Extract content
        content = post.get('content', '')
        if not content or len(content.strip()) < 5:
            return None
        
        # Preprocess
        processed = self.preprocessor.process_post(
            content, 
            post.get('author', 'unknown')
        )
        
        if not processed['valid']:
            return None
        
        # Sentiment analysis
        sentiment_result = self.sentiment_analyzer.analyze_sentiment(
            processed['clean_text']
        )
        
        # Generate embeddings if available
        embedding = None
        if self.embedder:
            try:
                embeddings = self.embedder.encode_dense([processed['clean_text']])
                if len(embeddings) > 0:
                    embedding = embeddings[0].tolist()
                    self.stats['embeddings_generated'] += 1
            except Exception as e:
                print(f"Embedding generation failed: {e}")
        
        # Combine results
        result = {
            # Original post data
            'post_id': post.get('post_id'),
            'timestamp': post.get('timestamp', datetime.now().isoformat()),
            'source': post.get('source', 'hawamer'),
            'url': post.get('url'),
            'likes': post.get('likes', 0),
            'shares': post.get('shares', 0),
            
            # Processed content
            'original_text': content,
            'clean_text': processed['clean_text'],
            'text_length': processed['text_length'],
            'word_count': processed['word_count'],
            
            # Entities
            'companies': processed['entities']['companies'],
            'tickers': processed['entities']['tickers'],
            'amounts': processed['entities']['amounts'],
            'percentages': processed['entities']['percentages'],
            'has_financial_content': processed['has_financial_content'],
            
            # Sentiment
            'sentiment': sentiment_result['sentiment'],
            'sentiment_score': sentiment_result['score'],
            'sentiment_confidence': sentiment_result['confidence'],
            'sentiment_method': sentiment_result['method'],
            
            # Additional features
            'author_hash': processed['author_hash'],
            'embedding': embedding,
            
            # Processing metadata
            'processing_time': time.time() - start_time
        }
        
        # Update stats
        self.stats['total_posts_processed'] += 1
        self.stats['total_processing_time'] += result['processing_time']
        self.stats['sentiment_predictions'] += 1
        
        if processed['has_financial_content']:
            self.stats['financial_posts_found'] += 1
        
        return result
    
    def process_batch(self, posts: List[Dict], 
                     batch_size: int = 100) -> pd.DataFrame:
        """
        Process multiple posts efficiently
        """
        print(f"Processing {len(posts)} posts in batches of {batch_size}...")
        
        all_results = []
        
        for i in range(0, len(posts), batch_size):
            batch = posts[i:i + batch_size]
            batch_results = []
            
            for post in batch:
                result = self.process_single_post(post)
                if result:
                    batch_results.append(result)
            
            all_results.extend(batch_results)
            
            if (i // batch_size + 1) % 10 == 0:
                print(f"Processed {i + len(batch)} posts...")
        
        # Convert to DataFrame
        df = pd.DataFrame(all_results)
        
        print(f"Processing complete: {len(all_results)} valid posts from {len(posts)} input posts")
        
        return df
    
    def generate_trading_features(self, df: pd.DataFrame, 
                                time_window: str = '1H') -> pd.DataFrame:
        """
        Generate aggregated features for trading signals
        """
        if df.empty:
            return pd.DataFrame()
        
        print(f"Generating trading features with {time_window} time window...")
        
        # Ensure timestamp is datetime
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        features = []
        
        # Get all unique tickers
        all_tickers = set()
        for tickers in df['tickers']:
            all_tickers.update(tickers)
        
        print(f"Found {len(all_tickers)} unique tickers")
        
        for ticker in all_tickers:
            # Filter posts mentioning this ticker
            ticker_mask = df['tickers'].apply(lambda x: ticker in x)
            ticker_df = df[ticker_mask].copy()
            
            if ticker_df.empty:
                continue
            
            # Set timestamp as index for resampling
            ticker_df.set_index('timestamp', inplace=True)
            
            # Resample by time window
            resampled = ticker_df.resample(time_window)
            
            for timestamp, group in resampled:
                if group.empty:
                    continue
                
                # Basic volume features
                post_count = len(group)
                unique_authors = group['author_hash'].nunique()
                
                # Sentiment features
                sentiment_scores = group['sentiment_score']
                sentiment_mean = sentiment_scores.mean()
                sentiment_std = sentiment_scores.std() if len(sentiment_scores) > 1 else 0
                
                # Sentiment distribution
                sentiment_counts = group['sentiment'].value_counts()
                bullish_ratio = sentiment_counts.get('bullish', 0) / post_count
                bearish_ratio = sentiment_counts.get('bearish', 0) / post_count
                neutral_ratio = sentiment_counts.get('neutral', 0) / post_count
                
                # Engagement features
                total_likes = group['likes'].sum()
                total_shares = group['shares'].sum()
                avg_likes = group['likes'].mean()
                
                # Text features
                avg_text_length = group['text_length'].mean()
                total_text_volume = group['text_length'].sum()
                
                # Confidence features
                avg_confidence = group['sentiment_confidence'].mean()
                high_confidence_ratio = (group['sentiment_confidence'] > 0.7).mean()
                
                # Entity features
                price_mentions = sum(len(amounts) for amounts in group['amounts'])
                percent_mentions = sum(len(pcts) for pcts in group['percentages'])
                
                feature_row = {
                    'ticker': ticker,
                    'timestamp': timestamp,
                    
                    # Volume features
                    'post_count': post_count,
                    'unique_authors': unique_authors,
                    'author_diversity': unique_authors / post_count if post_count > 0 else 0,
                    
                    # Sentiment features
                    'sentiment_mean': sentiment_mean,
                    'sentiment_std': sentiment_std,
                    'bullish_ratio': bullish_ratio,
                    'bearish_ratio': bearish_ratio,
                    'neutral_ratio': neutral_ratio,
                    'sentiment_skew': bullish_ratio - bearish_ratio,
                    
                    # Confidence features
                    'avg_confidence': avg_confidence,
                    'high_confidence_ratio': high_confidence_ratio,
                    
                    # Engagement features
                    'total_likes': total_likes,
                    'total_shares': total_shares,
                    'avg_likes': avg_likes,
                    'engagement_score': (total_likes + total_shares * 2) / post_count,
                    
                    # Text features
                    'avg_text_length': avg_text_length,
                    'total_text_volume': total_text_volume,
                    
                    # Entity features
                    'price_mentions': price_mentions,
                    'percent_mentions': percent_mentions,
                    'entity_density': (price_mentions + percent_mentions) / post_count
                }
                
                features.append(feature_row)
        
        features_df = pd.DataFrame(features)
        
        if not features_df.empty:
            # Sort by ticker and timestamp
            features_df = features_df.sort_values(['ticker', 'timestamp'])
            
            # Add rolling features
            print("Adding rolling features...")
            
            for ticker in features_df['ticker'].unique():
                ticker_mask = features_df['ticker'] == ticker
                
                # 3-period rolling averages
                features_df.loc[ticker_mask, 'sentiment_ma_3'] = (
                    features_df.loc[ticker_mask, 'sentiment_mean'].rolling(3, min_periods=1).mean()
                )
                
                features_df.loc[ticker_mask, 'volume_ma_3'] = (
                    features_df.loc[ticker_mask, 'post_count'].rolling(3, min_periods=1).mean()
                )
                
                # Momentum features (change from previous period)
                features_df.loc[ticker_mask, 'sentiment_momentum'] = (
                    features_df.loc[ticker_mask, 'sentiment_mean'].diff()
                )
                
                features_df.loc[ticker_mask, 'volume_momentum'] = (
                    features_df.loc[ticker_mask, 'post_count'].diff()
                )
                
                # Volatility features
                features_df.loc[ticker_mask, 'sentiment_volatility'] = (
                    features_df.loc[ticker_mask, 'sentiment_mean'].rolling(3, min_periods=1).std()
                )
        
        print(f"Generated {len(features_df)} feature rows for {len(all_tickers)} tickers")
        
        return features_df
    
    def fit_embedder(self, texts: List[str]) -> bool:
        """
        Fit the embedder on corpus for retrieval
        """
        if not self.embedder:
            print("Embedder not available")
            return False
        
        print(f"Fitting embedder on {len(texts)} texts...")
        return self.embedder.fit_bm25(texts)
    
    def search_similar_posts(self, query: str, 
                           corpus_embeddings: np.ndarray,
                           top_k: int = 10) -> List[Tuple[int, float]]:
        """
        Search for similar posts using hybrid retrieval
        """
        if not self.embedder:
            return []
        
        results = self.embedder.search_hybrid(query, corpus_embeddings, top_k)
        return [(idx, score) for idx, score, _ in results]
    
    def evaluate_pipeline(self, test_posts: List[Dict],
                         ground_truth: Optional[Dict] = None) -> Dict:
        """
        Evaluate pipeline performance
        """
        print("Evaluating pipeline performance...")
        
        start_time = time.time()
        
        # Process test posts
        df = self.process_batch(test_posts)
        
        processing_time = time.time() - start_time
        
        # Basic performance metrics
        metrics = {
            'total_input_posts': len(test_posts),
            'valid_processed_posts': len(df),
            'processing_success_rate': len(df) / len(test_posts) if test_posts else 0,
            'total_processing_time': processing_time,
            'avg_processing_time_per_post': processing_time / len(test_posts) if test_posts else 0,
            'financial_posts_ratio': (df['has_financial_content'].sum() / len(df)) if not df.empty else 0,
            'sentiment_distribution': df['sentiment'].value_counts().to_dict() if not df.empty else {},
            'avg_sentiment_confidence': df['sentiment_confidence'].mean() if not df.empty else 0
        }
        
        # Add component-specific metrics
        if self.embedder:
            metrics['embedder_stats'] = self.embedder.get_stats()
        
        metrics['sentiment_stats'] = self.sentiment_analyzer.get_stats()
        metrics['preprocessor_stats'] = self.preprocessor.get_stats(df.to_dict('records')) if not df.empty else {}
        
        return metrics
    
    def save_results(self, df: pd.DataFrame, features_df: pd.DataFrame,
                    output_dir: str = "data/processed") -> Dict[str, str]:
        """
        Save pipeline results
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        saved_files = {}
        
        # Save processed posts
        if not df.empty:
            posts_file = output_path / f"processed_posts_{timestamp}.parquet"
            df.to_parquet(posts_file, compression='gzip')
            saved_files['processed_posts'] = str(posts_file)
        
        # Save trading features
        if not features_df.empty:
            features_file = output_path / f"trading_features_{timestamp}.csv"
            features_df.to_csv(features_file, index=False)
            saved_files['trading_features'] = str(features_file)
        
        # Save pipeline stats
        stats_file = output_path / f"pipeline_stats_{timestamp}.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(self.stats, f, indent=2, default=str)
        saved_files['stats'] = str(stats_file)
        
        print(f"Results saved to {output_dir}")
        return saved_files
    
    def get_stats(self) -> Dict:
        """Get comprehensive pipeline statistics"""
        avg_time = (self.stats['total_processing_time'] / 
                   max(1, self.stats['total_posts_processed']))
        
        return {
            **self.stats,
            'avg_processing_time_per_post': avg_time,
            'financial_posts_ratio': (self.stats['financial_posts_found'] / 
                                    max(1, self.stats['total_posts_processed'])),
            'embeddings_success_rate': (self.stats['embeddings_generated'] / 
                                      max(1, self.stats['total_posts_processed']))
        }
