"""
Configuration Management for Saudi NLP Trading

Handles environment variables, feature flags, and cloud-ready settings.
All configurations are validated and type-safe.
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, field
from datetime import timezone, timedelta
import logging

# Asia/Riyadh timezone
RIYADH_TZ = timezone(timedelta(hours=3))

@dataclass
class ScraperConfig:
    """Scraper-specific configuration"""
    max_rpm: int = 30
    max_concurrency: int = 5
    respect_robots: bool = True
    user_agent: str = "Mozilla/5.0 (compatible; SaudiNLPBot/1.0)"
    debug: bool = False
    save_raw_html: bool = True
    min_post_length: int = 10
    max_empty_post_ratio: float = 0.05

@dataclass
class RateLimitConfig:
    """Rate limiting configuration"""
    window_seconds: int = 60
    max_requests: int = 30
    base_delay: float = 1.0
    max_delay: float = 300.0
    jitter: float = 0.1

@dataclass
class StorageConfig:
    """Storage backend configuration"""
    backend: str = "local"  # local | gcs
    gcs_bucket: Optional[str] = None
    gcs_project_id: Optional[str] = None
    local_base_path: str = "data"
    chunk_size_mb: int = 100

@dataclass
class LoggingConfig:
    """Logging configuration"""
    format: str = "json"  # json | text
    level: str = "info"
    rotate_size: str = "100MB"
    backup_count: int = 5
    cloud_enabled: bool = False

@dataclass
class SecurityConfig:
    """Security and privacy configuration"""
    pii_redaction_enabled: bool = True
    hash_salt_length: int = 32
    user_handle_hash_algo: str = "sha256"

@dataclass
class CaptchaConfig:
    """CAPTCHA solving configuration"""
    enabled: bool = True
    api_key: Optional[str] = None
    service: str = "2captcha"
    timeout: int = 120

@dataclass
class ProxyConfig:
    """Proxy configuration"""
    enabled: bool = False
    url: Optional[str] = None
    rotation_enabled: bool = False

@dataclass
class ModeConfig:
    """Feature flags and mode toggles"""
    scrape_only: bool = True
    nlp_enable: bool = False
    dedup_enabled: bool = True
    language_detection_enabled: bool = True

@dataclass
class CloudConfig:
    """Cloud platform configuration (Vertex AI ready)"""
    logging_enabled: bool = False
    pubsub_topic: Optional[str] = None
    trace_sampling_rate: float = 0.1

@dataclass
class Config:
    """Main configuration container"""
    scraper: ScraperConfig = field(default_factory=ScraperConfig)
    rate_limit: RateLimitConfig = field(default_factory=RateLimitConfig)
    storage: StorageConfig = field(default_factory=StorageConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)
    captcha: CaptchaConfig = field(default_factory=CaptchaConfig)
    proxy: ProxyConfig = field(default_factory=ProxyConfig)
    mode: ModeConfig = field(default_factory=ModeConfig)
    cloud: CloudConfig = field(default_factory=CloudConfig)
    
    # Runtime settings
    timezone: timezone = RIYADH_TZ
    run_id: Optional[str] = None

class ConfigLoader:
    """Load and validate configuration from multiple sources"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file
        self.logger = logging.getLogger(__name__)
    
    def load(self) -> Config:
        """Load configuration from environment and optional YAML file"""
        
        # Start with defaults
        config = Config()
        
        # Load from YAML file if provided
        if self.config_file and Path(self.config_file).exists():
            config = self._load_from_yaml(config)
        
        # Override with environment variables
        config = self._load_from_env(config)
        
        # Validate configuration
        self._validate_config(config)
        
        return config
    
    def _load_from_yaml(self, config: Config) -> Config:
        """Load configuration from YAML file"""
        try:
            with open(self.config_file, 'r') as f:
                yaml_data = yaml.safe_load(f)
            
            # Update config with YAML data
            if 'scraper' in yaml_data:
                for key, value in yaml_data['scraper'].items():
                    if hasattr(config.scraper, key):
                        setattr(config.scraper, key, value)
            
            if 'storage' in yaml_data:
                for key, value in yaml_data['storage'].items():
                    if hasattr(config.storage, key):
                        setattr(config.storage, key, value)
            
            # Add other sections as needed
            
        except Exception as e:
            self.logger.warning(f"Failed to load YAML config: {e}")
        
        return config
    
    def _load_from_env(self, config: Config) -> Config:
        """Load configuration from environment variables"""
        
        # Scraper config
        config.scraper.max_rpm = int(os.getenv('SCRAPER_MAX_RPM', config.scraper.max_rpm))
        config.scraper.max_concurrency = int(os.getenv('SCRAPER_MAX_CONCURRENCY', config.scraper.max_concurrency))
        config.scraper.respect_robots = os.getenv('SCRAPER_RESPECT_ROBOTS', 'true').lower() == 'true'
        config.scraper.user_agent = os.getenv('SCRAPER_USER_AGENT', config.scraper.user_agent)
        config.scraper.debug = os.getenv('SCRAPER_DEBUG', 'false').lower() == 'true'
        config.scraper.save_raw_html = os.getenv('SAVE_RAW_HTML', 'true').lower() == 'true'
        config.scraper.min_post_length = int(os.getenv('MIN_POST_LENGTH', config.scraper.min_post_length))
        config.scraper.max_empty_post_ratio = float(os.getenv('MAX_EMPTY_POST_RATIO', config.scraper.max_empty_post_ratio))
        
        # Rate limiting
        config.rate_limit.window_seconds = int(os.getenv('RATE_LIMIT_WINDOW_SECONDS', config.rate_limit.window_seconds))
        config.rate_limit.max_requests = int(os.getenv('RATE_LIMIT_MAX_REQUESTS', config.rate_limit.max_requests))
        config.rate_limit.base_delay = float(os.getenv('BACKOFF_BASE_DELAY', config.rate_limit.base_delay))
        config.rate_limit.max_delay = float(os.getenv('BACKOFF_MAX_DELAY', config.rate_limit.max_delay))
        config.rate_limit.jitter = float(os.getenv('BACKOFF_JITTER', config.rate_limit.jitter))
        
        # Storage
        config.storage.backend = os.getenv('STORAGE_BACKEND', config.storage.backend)
        config.storage.gcs_bucket = os.getenv('GCS_BUCKET')
        config.storage.gcs_project_id = os.getenv('GCS_PROJECT_ID')
        config.storage.local_base_path = os.getenv('LOCAL_BASE_PATH', config.storage.local_base_path)
        
        # Logging
        config.logging.format = os.getenv('LOGGING_FORMAT', config.logging.format)
        config.logging.level = os.getenv('LOGGING_LEVEL', config.logging.level)
        config.logging.rotate_size = os.getenv('LOGGING_ROTATE_SIZE', config.logging.rotate_size)
        config.logging.backup_count = int(os.getenv('LOGGING_BACKUP_COUNT', config.logging.backup_count))
        config.logging.cloud_enabled = os.getenv('CLOUD_LOGGING_ENABLED', 'false').lower() == 'true'
        
        # Security
        config.security.pii_redaction_enabled = os.getenv('PII_REDACTION_ENABLED', 'true').lower() == 'true'
        config.security.hash_salt_length = int(os.getenv('HASH_SALT_LENGTH', config.security.hash_salt_length))
        config.security.user_handle_hash_algo = os.getenv('USER_HANDLE_HASH_ALGO', config.security.user_handle_hash_algo)
        
        # CAPTCHA
        config.captcha.enabled = os.getenv('CAPTCHA_ENABLED', 'true').lower() == 'true'
        config.captcha.api_key = os.getenv('CAPTCHA_API_KEY')
        
        # Proxy
        config.proxy.enabled = os.getenv('PROXY_ENABLED', 'false').lower() == 'true'
        config.proxy.url = os.getenv('PROXY_URL')
        
        # Mode toggles
        config.mode.scrape_only = os.getenv('MODE_SCRAPE_ONLY', 'true').lower() == 'true'
        config.mode.nlp_enable = os.getenv('NLP_ENABLE', 'false').lower() == 'true'
        config.mode.dedup_enabled = os.getenv('DEDUP_ENABLED', 'true').lower() == 'true'
        config.mode.language_detection_enabled = os.getenv('LANGUAGE_DETECTION_ENABLED', 'true').lower() == 'true'
        
        # Cloud
        config.cloud.logging_enabled = os.getenv('CLOUD_LOGGING_ENABLED', 'false').lower() == 'true'
        config.cloud.pubsub_topic = os.getenv('PUBSUB_TOPIC')
        config.cloud.trace_sampling_rate = float(os.getenv('TRACE_SAMPLING_RATE', config.cloud.trace_sampling_rate))
        
        return config
    
    def _validate_config(self, config: Config) -> None:
        """Validate configuration values"""
        
        # Validate scraper settings
        if config.scraper.max_rpm <= 0:
            raise ValueError("SCRAPER_MAX_RPM must be positive")
        
        if config.scraper.max_concurrency <= 0:
            raise ValueError("SCRAPER_MAX_CONCURRENCY must be positive")
        
        if not (0.0 <= config.scraper.max_empty_post_ratio <= 1.0):
            raise ValueError("MAX_EMPTY_POST_RATIO must be between 0.0 and 1.0")
        
        # Validate storage settings
        if config.storage.backend not in ['local', 'gcs']:
            raise ValueError("STORAGE_BACKEND must be 'local' or 'gcs'")
        
        if config.storage.backend == 'gcs':
            if not config.storage.gcs_bucket:
                raise ValueError("GCS_BUCKET required when STORAGE_BACKEND=gcs")
            if not config.storage.gcs_project_id:
                raise ValueError("GCS_PROJECT_ID required when STORAGE_BACKEND=gcs")
        
        # Validate logging settings
        if config.logging.format not in ['json', 'text']:
            raise ValueError("LOGGING_FORMAT must be 'json' or 'text'")
        
        if config.logging.level not in ['debug', 'info', 'warning', 'error']:
            raise ValueError("LOGGING_LEVEL must be debug/info/warning/error")
        
        # Validate rate limiting
        if config.rate_limit.base_delay < 0:
            raise ValueError("BACKOFF_BASE_DELAY must be non-negative")
        
        if config.rate_limit.max_delay < config.rate_limit.base_delay:
            raise ValueError("BACKOFF_MAX_DELAY must be >= BACKOFF_BASE_DELAY")
        
        # Validate CAPTCHA settings
        if config.captcha.enabled and not config.captcha.api_key:
            raise ValueError("CAPTCHA_API_KEY required when CAPTCHA_ENABLED=true")
        
        self.logger.info("Configuration validation passed")

# Global configuration instance
_config: Optional[Config] = None

def get_config(config_file: Optional[str] = None, reload: bool = False) -> Config:
    """Get global configuration instance"""
    global _config
    
    if _config is None or reload:
        loader = ConfigLoader(config_file)
        _config = loader.load()
    
    return _config

def set_run_id(run_id: str) -> None:
    """Set the current run ID"""
    config = get_config()
    config.run_id = run_id
