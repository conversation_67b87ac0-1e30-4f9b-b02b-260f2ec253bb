# System Architecture

This document describes the overall design of the Saudi NLP Trading System.  The system is composed of several loosely coupled components that work together to scrape, process and analyse Arabic financial discussions from Hawamer.com and generate features for trading models.

## Overview

1. **Scraper Layer**  
   The scraper uses <PERSON><PERSON> to control a headless Chromium browser with stealth techniques (e.g. disabling `navigator.webdriver`) to bypass Cloudflare challenges.  It navigates to forum threads, captures network traffic to discover hidden API endpoints and extracts post content.  Scraper configuration (rate limits, proxies, user agents) is stored in `config/scraper_config.yaml`.

2. **Pre‑processor**  
   Raw HTML posts are normalised and tokenised using CAMeL Tools.  The preprocessor removes diacritics, deduplicates repeated characters and maps common Saudi slang to formal Arabic.  It also extracts potential Tadawul ticker codes and returns a structured representation containing cleaned text, tokens and tickers.

3. **Embedding Layer**  
   Posts are converted into dense vectors using Swan, a dialect‑aware sentence transformer fine‑tuned on Arabic dialects.  The embedder falls back to a multilingual model if <PERSON> is not available.  Embeddings are cached and stored to minimise redundant computation.

4. **Analytics**  
   On top of embeddings, the system performs:
   - **Topic modelling** with BERTopic to discover trending themes and detect topic drift.
   - **Sentiment analysis** by integrating Swan embeddings with FinBERT scores (module to be implemented).
   - **Graph analytics** to build reply networks and compute influence metrics.
   - **Volatility coupling** to relate sentiment and topic drift to Tadawul volatility metrics (GARCH models).

5. **Pipeline Orchestrator**  
   The orchestrator coordinates scraping, preprocessing, embedding and analytics in a sequential asynchronous pipeline.  It logs all actions, persists intermediate results and generates feature dictionaries for downstream trading models.

6. **Storage**  
   Raw HTML is saved to S3.  Cleaned posts and features are stored in PostgreSQL and optionally indexed in Elasticsearch for full‑text search.  Caching layers (Redis) handle rate limiting and repeated queries.

## Data Flow

```
Client → Scraper → Preprocessor → Embedder → Analytics → Feature Engineering → Storage
```

## Extensibility

The modular structure allows for easy swapping of models (e.g., switching to Dallah for multimodal inputs) and adding new analytics.  Configuration files centralise all tunable parameters, and notebooks under `notebooks/` provide examples of individual components.
