"""
Hawamer Forum Scraper for Saudi NLP Trading

Extracts posts from Hawamer financial forum with enhanced V1.1 schema.
Emits thread_url, page_url, selector_version, dedup_key for all records.
"""

import hashlib
import re
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from urllib.parse import urljoin, urlparse
from pathlib import Path

# Use try/except for imports to handle both relative and absolute imports
try:
    from ..utils.logging import get_logger
    from ..utils.robots import get_robots_checker
    from ..utils.rate_limiter import get_domain_rate_limiter
    from ..config.settings import get_config, RIYADH_TZ
except ImportError:
    # Fallback for standalone execution
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent.parent))

    from utils.logging import get_logger
    from utils.robots import get_robots_checker
    from utils.rate_limiter import get_domain_rate_limiter
    from config.settings import get_config, RIYADH_TZ

class HawamerScraper:
    """
    Hawamer forum scraper with enhanced V1.1 record schema
    """
    
    def __init__(self, run_id: str):
        self.run_id = run_id
        self.config = get_config()
        self.logger = get_logger(__name__)
        self.robots_checker = get_robots_checker()
        self.rate_limiter = get_domain_rate_limiter()
        
        # Scraper configuration
        self.base_url = "https://hawamer.com"
        self.selector_version = "1.1"  # Track selector changes
        self.schema_version = "1.1"    # Enhanced schema with required fields
        
        # CSS selectors for Hawamer forum structure
        self.selectors = {
            'thread_title': 'h1.thread-title, .thread-header h1',
            'posts': '.post, .message',
            'post_content': '.postcontent, .message-content',
            'post_author': '.author, .username',
            'post_likes': '.likes-count, .reaction-count',
            'post_timestamp': '.timestamp, .post-date',
            'pagination': '.pagination a, .page-nav a',
            'next_page': '.pagination .next, .page-nav .next'
        }
        
        self.logger.info(
            "Initialized Hawamer scraper",
            run_id=run_id,
            selector_version=self.selector_version,
            schema_version=self.schema_version
        )
    
    def extract_thread_id(self, url: str) -> str:
        """Extract thread ID from Hawamer URL"""
        # Pattern: https://hawamer.com/vb/hawamer123456
        match = re.search(r'/hawamer(\d+)', url)
        if match:
            return f"thread_{match.group(1)}"
        
        # Fallback: use URL hash
        url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
        return f"thread_{url_hash}"
    
    def generate_dedup_key(self, content: str, author: str, timestamp: str = None) -> str:
        """Generate stable deduplication key for post"""
        # Normalize content for deduplication
        normalized_content = re.sub(r'\s+', ' ', content.strip())
        
        # Create dedup string
        dedup_string = f"{normalized_content}|{author}"
        if timestamp:
            dedup_string += f"|{timestamp}"
        
        # Return first 16 chars of SHA256 hash
        return hashlib.sha256(dedup_string.encode('utf-8')).hexdigest()[:16]
    
    def create_enhanced_record(self, 
                             thread_id: str,
                             thread_url: str,
                             page_url: str,
                             page_no: int,
                             post_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create enhanced V1.1 record with all required fields
        
        Args:
            thread_id: Thread identifier
            thread_url: Base thread URL (page 1)
            page_url: Specific page URL
            page_no: Page number
            post_data: Extracted post data
        
        Returns:
            Enhanced record dict with V1.1 schema
        """
        
        # Generate author hash for privacy
        author_hash = hashlib.sha256(
            post_data.get('author', 'unknown').encode('utf-8')
        ).hexdigest()[:16]
        
        # Generate dedup key
        dedup_key = self.generate_dedup_key(
            post_data.get('content', ''),
            post_data.get('author', ''),
            post_data.get('timestamp')
        )
        
        # Create enhanced record
        record = {
            # Core V1.0 fields
            'run_id': self.run_id,
            'source': 'hawamer',
            'thread_id': thread_id,
            'post_id': post_data.get('post_id', f"post_{hash(post_data.get('content', ''))[:8]}"),
            'url': page_url,  # Page-specific URL
            'scraped_at': datetime.now(RIYADH_TZ).isoformat(),
            'author_hash': author_hash,
            'raw_html': post_data.get('raw_html', ''),
            'raw_text': post_data.get('content', ''),
            'visible_text': post_data.get('content', ''),
            'likes': post_data.get('likes', 0),
            'reply_to_id': post_data.get('reply_to_id'),
            'page_no': page_no,
            'lang_detect': 'ar',  # Assume Arabic for Hawamer
            'http_status': post_data.get('http_status', 200),
            'retry_count': post_data.get('retry_count', 0),
            'robot_policy': post_data.get('robot_policy', 'allowed'),
            
            # Enhanced V1.1 fields (REQUIRED)
            'thread_url': thread_url,      # Base thread URL
            'page_url': page_url,          # Specific page URL  
            'selector_version': self.selector_version,  # Track selector changes
            'dedup_key': dedup_key,        # Stable deduplication key
            'schema_version': self.schema_version,      # Schema version
            
            # Additional V1.1 metadata
            'thread_title': post_data.get('thread_title', ''),
            'extraction_timestamp': datetime.now(RIYADH_TZ).isoformat(),
            'post_index': post_data.get('post_index', 0),
            'has_replies': post_data.get('has_replies', False)
        }
        
        return record
    
    def scrape_thread_page(self, thread_url: str, page_no: int = 1) -> List[Dict[str, Any]]:
        """
        Scrape a single page of a Hawamer thread
        
        Args:
            thread_url: Base thread URL
            page_no: Page number to scrape
        
        Returns:
            List of enhanced V1.1 records
        """
        
        # Construct page URL
        if page_no == 1:
            page_url = thread_url
        else:
            page_url = f"{thread_url}?page={page_no}"
        
        # Extract thread ID
        thread_id = self.extract_thread_id(thread_url)
        
        self.logger.info(
            "Scraping thread page",
            thread_url=thread_url,
            page_url=page_url,
            page_no=page_no,
            thread_id=thread_id
        )
        
        try:
            # Check robots.txt and rate limiting
            domain = urlparse(thread_url).netloc
            can_fetch, policy, crawl_delay = self.robots_checker.can_fetch(page_url)
            
            if not can_fetch:
                self.logger.warning(
                    "Robots.txt blocks access",
                    page_url=page_url,
                    policy=policy
                )
                return []
            
            # Apply rate limiting
            wait_time = self.rate_limiter.wait_for_domain(domain)
            if wait_time > 0:
                self.logger.info(
                    "Rate limited - waited",
                    domain=domain,
                    wait_time=wait_time
                )
            
            # Mock scraping for demo (replace with actual HTTP + parsing)
            mock_posts = self._generate_mock_posts(thread_url, page_no)
            
            # Convert to enhanced records
            records = []
            for i, post_data in enumerate(mock_posts):
                post_data['post_index'] = i
                post_data['thread_title'] = f"Mock Thread {thread_id}"
                
                record = self.create_enhanced_record(
                    thread_id=thread_id,
                    thread_url=thread_url,
                    page_url=page_url,
                    page_no=page_no,
                    post_data=post_data
                )
                
                records.append(record)
            
            self.logger.info(
                "Scraped thread page successfully",
                page_url=page_url,
                posts_extracted=len(records),
                schema_version=self.schema_version
            )
            
            return records
            
        except Exception as e:
            self.logger.error(
                "Failed to scrape thread page",
                page_url=page_url,
                exception_class=e.__class__.__name__,
                exc_info=True
            )
            return []
    
    def _generate_mock_posts(self, thread_url: str, page_no: int) -> List[Dict[str, Any]]:
        """Generate mock posts for demo (replace with actual parsing)"""
        
        mock_content = [
            "الراجحي سهم ممتاز للاستثمار! ارتفع 3.5% اليوم وكسر مقاومة 85 ريال",
            "أرامكو تراجعت 2% بسبب انخفاض أسعار النفط العالمية. السوق متخوف من الركود",
            "سابك حققت أرباح ممتازة في الربع الثاني! نمو 25% مقارنة بالعام الماضي",
            "الاتصالات السعودية تعلن شراكة جديدة مع شركة تقنية عالمية. السهم يمكن يطير!",
            "السوق اليوم أحمر بالكامل. تصريف قوي من المؤسسات. أنصح بالحذر والانتظار"
        ]
        
        mock_authors = [
            "saudi_trader", "oil_analyst", "financial_expert", "tech_investor", "market_watcher"
        ]
        
        posts = []
        for i in range(3):  # 3 posts per page
            content_idx = (page_no - 1) * 3 + i
            if content_idx < len(mock_content):
                post = {
                    'post_id': f"post_{hash(thread_url + str(page_no) + str(i)) % 100000}",
                    'content': mock_content[content_idx],
                    'author': mock_authors[i % len(mock_authors)],
                    'likes': (i + 1) * 5 + page_no,
                    'timestamp': datetime.now(RIYADH_TZ).isoformat(),
                    'raw_html': f'<div class="postcontent">{mock_content[content_idx]}</div>',
                    'http_status': 200,
                    'retry_count': 0,
                    'robot_policy': 'allowed'
                }
                posts.append(post)
        
        return posts
    
    def scrape_thread(self, thread_url: str, max_pages: int = 5) -> List[Dict[str, Any]]:
        """
        Scrape entire thread with pagination
        
        Args:
            thread_url: Base thread URL
            max_pages: Maximum pages to scrape
        
        Returns:
            List of all enhanced V1.1 records from thread
        """
        
        all_records = []
        
        for page_no in range(1, max_pages + 1):
            page_records = self.scrape_thread_page(thread_url, page_no)
            
            if not page_records:
                self.logger.info(
                    "No more posts found, stopping pagination",
                    thread_url=thread_url,
                    page_no=page_no
                )
                break
            
            all_records.extend(page_records)
            
            # Check if we should continue (mock logic)
            if len(page_records) < 3:  # Fewer posts than expected
                break
        
        self.logger.info(
            "Completed thread scraping",
            thread_url=thread_url,
            total_pages=page_no,
            total_posts=len(all_records)
        )
        
        return all_records
    
    def validate_record_schema(self, record: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate that record has all required V1.1 fields
        
        Returns:
            (is_valid, missing_fields)
        """
        
        required_fields = [
            'run_id', 'source', 'thread_id', 'post_id', 'url', 'scraped_at',
            'author_hash', 'raw_html', 'raw_text', 'visible_text', 'likes',
            'page_no', 'lang_detect', 'http_status', 'retry_count', 'robot_policy',
            # V1.1 required fields
            'thread_url', 'page_url', 'selector_version', 'dedup_key', 'schema_version'
        ]
        
        missing_fields = []
        for field in required_fields:
            if field not in record or record[field] is None:
                missing_fields.append(field)
        
        return len(missing_fields) == 0, missing_fields

def create_hawamer_scraper(run_id: str) -> HawamerScraper:
    """Factory function to create Hawamer scraper"""
    return HawamerScraper(run_id)
