#!/usr/bin/env python3
"""Quick setup script for the Saudi NLP Trading System."""
import sys
import subprocess
from pathlib import Path

def setup_environment():
    print("🚀 Saudi NLP Trading System Setup")
    print("=" * 50)
    directories = [
        'data/raw', 'data/processed', 'data/embeddings', 'data/models',
        'logs', 'config', 'notebooks'
    ]
    for dir_path in directories:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        print(f"✓ Created {dir_path}")
    print("\n📦 Installing dependencies...")
    subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip', 'setuptools', 'wheel'])
    subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
    print("\n🧭 Installing Playwright browsers (Chromium)...")
    subprocess.run([sys.executable, '-m', 'playwright', 'install', 'chromium'])
    print("\n🤖 Downloading NLP models...")
    download_models()
    print("\n🧪 Running tests...")
    subprocess.run([sys.executable, '-m', 'pytest', 'tests/', '-v'])
    print("\n✅ Setup complete! Run 'python -m src.pipeline.orchestrator' to start.")

def download_models():
    from transformers import AutoModel, AutoTokenizer
    models = [
        'CAMeL-Lab/bert-base-arabic-camelbert-mix',
        'aubmindlab/bert-base-arabertv2'
    ]
    for model_name in models:
        print(f"Downloading {model_name}...")
        try:
            AutoModel.from_pretrained(model_name)
            AutoTokenizer.from_pretrained(model_name)
            print(f"✓ {model_name} downloaded")
        except Exception as e:
            print(f"⚠ Failed to download {model_name}: {e}")

if __name__ == '__main__':
    setup_environment()
