#!/usr/bin/env python3
"""
Hawamer Scraper Demo V1.1 - Fixed Config Drift

Uses the proper HawamerScraper that emits enhanced V1.1 schema with:
- thread_url, page_url, selector_version, dedup_key
- Proper orchestration between scraper and storage layer
- Schema version 1.1 compliance
"""

import os
import sys
import json
import uuid
import argparse
from pathlib import Path
from datetime import datetime, timezone, timedelta

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Set environment for demo
os.environ["MODE_SCRAPE_ONLY"] = os.getenv("SCRAPE_ONLY", "1")
os.environ["NLP_ENABLED"] = "false" if os.getenv("NLP_DISABLED", "1") == "1" else "true"
os.environ["MAX_REQUESTS_PER_MINUTE"] = "30"
os.environ["STORAGE_BACKEND"] = "local"
os.environ["SAVE_DEBUG_HTML"] = "true"

def print_header(title: str):
    """Print formatted header"""
    print("\n" + "="*80)
    print(f" {title}")
    print("="*80)

def demo_hawamer_scraper_v1_1(run_id: str, thread_urls: list):
    """Demo Hawamer scraper with V1.1 schema compliance"""
    print_header("HAWAMER SCRAPER V1.1 DEMO")

    try:
        # Import with absolute path to avoid relative import issues
        import importlib.util
        spec = importlib.util.spec_from_file_location(
            "hawamer",
            Path(__file__).parent / "src" / "scraper" / "hawamer.py"
        )
        hawamer_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(hawamer_module)

        create_hawamer_scraper = hawamer_module.create_hawamer_scraper
        
        # Create scraper
        scraper = create_hawamer_scraper(run_id)
        
        print(f"Hawamer scraper initialized:")
        print(f"  Selector version: {scraper.selector_version}")
        print(f"  Schema version: {scraper.schema_version}")
        print(f"  Run ID: {scraper.run_id}")
        
        all_records = []
        
        # Scrape each thread
        for thread_url in thread_urls:
            print(f"\nScraping thread: {thread_url}")
            
            # Scrape 2 pages per thread for pagination testing
            thread_records = scraper.scrape_thread(thread_url, max_pages=2)
            
            print(f"  Extracted {len(thread_records)} posts")
            
            # Validate schema compliance
            compliant_count = 0
            for record in thread_records:
                is_valid, missing_fields = scraper.validate_record_schema(record)
                if is_valid:
                    compliant_count += 1
                else:
                    print(f"  ⚠️  Record missing fields: {missing_fields}")
            
            compliance_rate = compliant_count / len(thread_records) if thread_records else 0
            print(f"  Schema compliance: {compliance_rate:.1%} ({compliant_count}/{len(thread_records)})")
            
            all_records.extend(thread_records)
        
        print(f"\nTotal records extracted: {len(all_records)}")
        
        # Validate enhanced fields
        enhanced_fields = ['thread_url', 'page_url', 'selector_version', 'dedup_key']
        for field in enhanced_fields:
            non_null_count = sum(1 for r in all_records if r.get(field) is not None)
            rate = non_null_count / len(all_records) if all_records else 0
            print(f"  {field}: {rate:.1%} non-null ({non_null_count}/{len(all_records)})")
        
        print("\n✓ Hawamer scraper V1.1 working with enhanced schema")
        return all_records
        
    except ImportError as e:
        print(f"✗ Failed to import Hawamer scraper: {e}")
        return []
    except Exception as e:
        print(f"✗ Hawamer scraper failed: {e}")
        import traceback
        traceback.print_exc()
        return []

def demo_storage_with_v1_1_schema(records: list, run_id: str):
    """Demo storage layer with V1.1 schema"""
    print_header("STORAGE LAYER V1.1 DEMO")
    
    if not records:
        print("No records to store")
        return None
    
    # Create storage directory
    data_dir = Path("data")
    date_str = datetime.now().strftime('%Y-%m-%d')
    source = "hawamer"
    
    raw_dir = data_dir / f"raw/source={source}/date={date_str}"
    raw_dir.mkdir(parents=True, exist_ok=True)
    
    # Store records as JSONL
    raw_file = raw_dir / "part-00000.jsonl"
    
    bytes_written = 0
    with open(raw_file, 'w', encoding='utf-8') as f:
        for record in records:
            line = json.dumps(record, ensure_ascii=False, separators=(',', ':'))
            f.write(line + '\n')
            bytes_written += len(line.encode('utf-8')) + 1
    
    # Demonstrate POSIX path normalization
    posix_path = raw_file.relative_to(data_dir).as_posix()
    
    print(f"V1.1 records stored:")
    print(f"  POSIX path: {posix_path}")
    print(f"  Records: {len(records)}")
    print(f"  Bytes: {bytes_written}")
    print(f"  Schema version: {records[0].get('schema_version', 'unknown')}")
    
    # Validate first record
    first_record = records[0]
    enhanced_fields = ['thread_url', 'page_url', 'selector_version', 'dedup_key']
    
    print(f"\nFirst record validation:")
    for field in enhanced_fields:
        value = first_record.get(field)
        status = "✓" if value is not None else "✗"
        print(f"  {status} {field}: {value}")
    
    print(f"\n✓ Storage layer working with V1.1 schema")
    return raw_file

def demo_golden_thread_validation(jsonl_file: Path):
    """Demo golden thread validation"""
    print_header("GOLDEN THREAD VALIDATION DEMO")
    
    if not jsonl_file or not jsonl_file.exists():
        print("No JSONL file to validate")
        return
    
    # Load records
    records = []
    with open(jsonl_file, 'r', encoding='utf-8') as f:
        for line in f:
            if line.strip():
                records.append(json.loads(line))
    
    print(f"Validating {len(records)} records...")
    
    # Required V1.1 fields
    required_fields = [
        'run_id', 'source', 'thread_id', 'post_id', 'url', 'scraped_at',
        'author_hash', 'raw_html', 'raw_text', 'visible_text', 'likes',
        'page_no', 'lang_detect', 'http_status', 'retry_count', 'robot_policy',
        'thread_url', 'page_url', 'selector_version', 'dedup_key', 'schema_version'
    ]
    
    # Check compliance
    compliant_records = 0
    missing_fields_summary = {}
    
    for record in records:
        missing_fields = []
        for field in required_fields:
            if field not in record or record[field] is None:
                missing_fields.append(field)
        
        if not missing_fields:
            compliant_records += 1
        else:
            for field in missing_fields:
                missing_fields_summary[field] = missing_fields_summary.get(field, 0) + 1
    
    compliance_rate = compliant_records / len(records)
    
    print(f"Schema compliance results:")
    print(f"  Compliant records: {compliant_records}/{len(records)} ({compliance_rate:.1%})")
    
    if missing_fields_summary:
        print(f"  Most missing fields:")
        for field, count in sorted(missing_fields_summary.items(), key=lambda x: x[1], reverse=True)[:5]:
            print(f"    {field}: missing in {count} records")
    
    # Enhanced field validation
    enhanced_fields = ['thread_url', 'page_url', 'selector_version', 'dedup_key']
    print(f"\nEnhanced field validation:")
    
    for field in enhanced_fields:
        non_null_count = sum(1 for r in records if r.get(field) is not None and str(r.get(field)).strip())
        rate = non_null_count / len(records)
        status = "✓" if rate >= 0.95 else "✗"
        print(f"  {status} {field}: {rate:.1%} non-null ({non_null_count}/{len(records)})")
    
    # Overall validation
    overall_pass = compliance_rate >= 0.95
    status = "✓ PASS" if overall_pass else "✗ FAIL"
    print(f"\nGolden thread validation: {status}")
    
    if not overall_pass:
        print(f"  Compliance rate {compliance_rate:.1%} below 95% threshold")
    
    return overall_pass

def main():
    """Run Hawamer V1.1 demo with proper schema compliance"""
    
    parser = argparse.ArgumentParser(description="Hawamer Scraper V1.1 Demo")
    parser.add_argument("--threads", type=int, default=2, help="Number of threads to scrape")
    args = parser.parse_args()
    
    print_header("HAWAMER SCRAPER V1.1 - FIXED CONFIG DRIFT")
    print("Demonstrates proper orchestration with enhanced V1.1 schema")
    print("Features: thread_url, page_url, selector_version, dedup_key")
    
    # Generate run ID
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    run_id = f"{timestamp}_{str(uuid.uuid4())[:8]}"
    print(f"\nRun ID: {run_id}")
    
    # Load test URLs
    urls_file = Path("hawamer_urls.txt")
    test_urls = []
    
    if urls_file.exists():
        with open(urls_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    test_urls.append(line)
    
    if not test_urls:
        # Fallback URLs
        test_urls = [
            "https://hawamer.com/vb/hawamer917322",
            "https://hawamer.com/vb/hawamer918456"
        ]
    
    # Limit to requested number of threads
    test_urls = test_urls[:args.threads]
    
    print(f"Test URLs: {test_urls}")
    
    try:
        # Demo components
        records = demo_hawamer_scraper_v1_1(run_id, test_urls)
        jsonl_file = demo_storage_with_v1_1_schema(records, run_id)
        validation_pass = demo_golden_thread_validation(jsonl_file)
        
        # Summary
        print_header("V1.1 DEMO COMPLETED")
        
        if records and validation_pass:
            print("✓ Config drift FIXED - proper orchestration working")
            print("✓ Enhanced V1.1 schema compliance achieved")
            print("✓ Golden thread validation PASSED")
            print(f"✓ {len(records)} records with all required fields")
            
            print(f"\nFiles created:")
            if jsonl_file:
                print(f"  JSONL: {jsonl_file.relative_to(Path.cwd()).as_posix()}")
            
            print(f"\nNext steps:")
            print("1. Run pytest tests/test_golden_thread.py")
            print("2. Add to CI pipeline for regression detection")
            print("3. Scale test with 100k+ records")
            
            return True
        else:
            print("✗ Demo failed - check logs above")
            return False
        
    except Exception as e:
        print(f"\n❌ V1.1 Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
