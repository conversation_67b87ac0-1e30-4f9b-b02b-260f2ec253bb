#!/usr/bin/env python3
"""
Live Hawamer Scraper Validation Script
Minimal implementation for real-world validation without heavy NLP dependencies
"""

import json
import time
import hashlib
import gzip
from datetime import datetime, timezone, timedelta
from pathlib import Path
from typing import Dict, List, Any
import requests
from bs4 import BeautifulSoup

# Saudi timezone
RIYADH_TZ = timezone(timedelta(hours=3))

class MinimalRateLimiter:
    """Simple token bucket rate limiter"""
    
    def __init__(self, requests_per_minute: int = 6):
        self.rpm = requests_per_minute
        self.tokens = float(requests_per_minute // 4)  # Start with some tokens
        self.capacity = float(requests_per_minute // 4)
        self.refill_rate = requests_per_minute / 60.0  # tokens per second
        self.last_refill = time.time()
    
    def wait_for_token(self) -> float:
        """Wait for a token and return actual wait time"""
        start_time = time.time()
        
        # Refill tokens
        now = time.time()
        elapsed = now - self.last_refill
        if elapsed > 0:
            tokens_to_add = elapsed * self.refill_rate
            self.tokens = min(self.capacity, self.tokens + tokens_to_add)
            self.last_refill = now
        
        # Check if we have tokens
        if self.tokens >= 1:
            self.tokens -= 1
            return 0.0
        
        # Wait for tokens
        wait_time = (1 - self.tokens) / self.refill_rate
        time.sleep(wait_time)
        self.tokens = 0  # Consumed the token we waited for
        
        return time.time() - start_time

class LiveHawamerValidator:
    """Minimal live Hawamer scraper for validation"""
    
    def __init__(self, run_id: str):
        self.run_id = run_id
        self.rate_limiter = MinimalRateLimiter(requests_per_minute=6)
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'ar-SA,ar;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        # Create output directory
        self.output_dir = Path("data/live_validation")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"🎯 Live Hawamer Validator initialized")
        print(f"Run ID: {run_id}")
        print(f"Rate limit: 6 RPM (0.1 RPS)")
        print(f"Output: {self.output_dir}")
    
    def scrape_thread_page(self, url: str) -> Dict[str, Any]:
        """Scrape a single Hawamer thread page"""
        
        print(f"\n🔍 Scraping: {url}")
        
        # Rate limiting
        wait_time = self.rate_limiter.wait_for_token()
        if wait_time > 0:
            print(f"⏱️  Rate limited: waited {wait_time:.2f}s")
        
        try:
            # Make request
            start_time = time.time()
            response = self.session.get(url, timeout=30)
            latency = time.time() - start_time
            
            print(f"📡 HTTP {response.status_code} in {latency:.2f}s")
            
            # Check for Cloudflare
            is_cloudflare = (
                response.status_code == 403 or
                "Just a moment" in response.text or
                "cloudflare" in response.text.lower() or
                "cf-challenge" in response.text
            )
            
            if is_cloudflare:
                print("🚨 Cloudflare protection detected")
                return {
                    "url": url,
                    "status": "blocked",
                    "cloudflare": True,
                    "posts": [],
                    "scraped_at": datetime.now(RIYADH_TZ).isoformat()
                }
            
            # Parse HTML
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract posts (basic selectors)
            posts = []
            post_elements = soup.find_all(['div', 'td'], class_=lambda x: x and any(
                keyword in str(x).lower() for keyword in ['post', 'message', 'content']
            ))
            
            for i, post_elem in enumerate(post_elements[:10]):  # Limit to 10 posts
                # Extract text content
                text_content = post_elem.get_text(strip=True)
                
                # Skip if too short or likely not a post
                if len(text_content) < 50:
                    continue
                
                # Check for Arabic content
                arabic_chars = sum(1 for c in text_content if '\u0600' <= c <= '\u06FF')
                has_arabic = arabic_chars > 10
                
                if not has_arabic:
                    continue
                
                # Create post record
                post_id = f"post_{i+1}"
                author_text = "anonymous_user"  # Simplified for validation
                
                # PDPL-compliant author hashing
                salt = "hawamer_validation_salt_2025"
                author_hash = hashlib.sha256(f"{salt}|{author_text}".encode()).hexdigest()[:16]
                
                # Content hashing
                content_hash = hashlib.sha256(text_content.encode()).hexdigest()[:16]
                
                # Deduplication key
                dedup_key = hashlib.sha256(f"hawamer|{url}|{post_id}|{text_content[:128]}".encode()).hexdigest()[:16]
                
                post_record = {
                    "run_id": self.run_id,
                    "schema_version": "1.1",
                    "source": "hawamer",
                    "thread_id": url.split('/')[-1],
                    "post_id": post_id,
                    "url": url,
                    "scraped_at": datetime.now(RIYADH_TZ).isoformat(),
                    "author_hash": author_hash,
                    "raw_html": str(post_elem)[:1000],  # Truncated for demo
                    "raw_text": text_content,
                    "visible_text": text_content,
                    "language": "ar" if has_arabic else "unknown",
                    "encoding": "utf-8",
                    "page_index": 0,
                    "thread_page_count": 1,
                    "content_hash": content_hash,
                    "dedup_key": dedup_key,
                    "parser_version": "1.1",
                    "compliance_flags": {
                        "robots_ok": True,  # Simplified for validation
                        "pdpl_ok": True
                    }
                }
                
                posts.append(post_record)
            
            print(f"✅ Extracted {len(posts)} posts with Arabic content")
            
            return {
                "url": url,
                "status": "success",
                "cloudflare": False,
                "posts": posts,
                "scraped_at": datetime.now(RIYADH_TZ).isoformat(),
                "latency": latency
            }
            
        except Exception as e:
            print(f"❌ Error scraping {url}: {e}")
            return {
                "url": url,
                "status": "error",
                "error": str(e),
                "posts": [],
                "scraped_at": datetime.now(RIYADH_TZ).isoformat()
            }
    
    def validate_urls(self, urls: List[str]) -> Dict[str, Any]:
        """Validate multiple URLs and save results"""
        
        print(f"\n🚀 Starting live validation of {len(urls)} URLs")
        
        results = {
            "run_id": self.run_id,
            "started_at": datetime.now(RIYADH_TZ).isoformat(),
            "urls_tested": len(urls),
            "results": []
        }
        
        for i, url in enumerate(urls):
            print(f"\n[{i+1}/{len(urls)}] Processing: {url}")
            
            result = self.scrape_thread_page(url)
            results["results"].append(result)
            
            # Small delay between URLs
            if i < len(urls) - 1:
                time.sleep(2)
        
        # Calculate summary stats
        successful = sum(1 for r in results["results"] if r["status"] == "success")
        blocked = sum(1 for r in results["results"] if r.get("cloudflare", False))
        total_posts = sum(len(r["posts"]) for r in results["results"])
        
        results.update({
            "completed_at": datetime.now(RIYADH_TZ).isoformat(),
            "summary": {
                "successful_scrapes": successful,
                "cloudflare_blocks": blocked,
                "total_posts_extracted": total_posts,
                "success_rate": successful / len(urls) if urls else 0
            }
        })
        
        # Save results
        output_file = self.output_dir / f"validation_{self.run_id}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        # Save JSONL format for posts
        if total_posts > 0:
            jsonl_file = self.output_dir / f"posts_{self.run_id}.jsonl.gz"
            with gzip.open(jsonl_file, 'wt', encoding='utf-8') as f:
                for result in results["results"]:
                    for post in result["posts"]:
                        f.write(json.dumps(post, ensure_ascii=False) + '\n')
            print(f"📄 Saved {total_posts} posts to {jsonl_file}")
        
        print(f"\n📊 Validation Results:")
        print(f"✅ Successful scrapes: {successful}/{len(urls)}")
        print(f"🚨 Cloudflare blocks: {blocked}/{len(urls)}")
        print(f"📝 Total posts extracted: {total_posts}")
        print(f"💾 Results saved to: {output_file}")
        
        return results

def main():
    """Run live Hawamer validation"""
    
    # Generate run ID
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    run_id = f"live_validation_{timestamp}"
    
    # Load URLs
    urls_file = Path("hawamer_urls.txt")
    test_urls = []
    
    if urls_file.exists():
        with open(urls_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    test_urls.append(line)
    
    if not test_urls:
        print("❌ No URLs found in hawamer_urls.txt")
        return
    
    # Limit to first 3 URLs for validation
    test_urls = test_urls[:3]
    
    # Run validation
    validator = LiveHawamerValidator(run_id)
    results = validator.validate_urls(test_urls)
    
    # Print final summary
    print(f"\n🎯 Live Validation Complete!")
    print(f"Run ID: {run_id}")
    print(f"Success Rate: {results['summary']['success_rate']:.1%}")
    
    if results['summary']['total_posts_extracted'] > 0:
        print(f"✅ VALIDATION SUCCESSFUL: Extracted real Arabic posts from Hawamer")
    else:
        print(f"⚠️  No posts extracted - likely Cloudflare protection")

if __name__ == "__main__":
    main()
