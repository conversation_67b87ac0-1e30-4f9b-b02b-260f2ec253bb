"""
Financial Named Entity Recognition for Arabic Text

Specialized NER system for extracting financial entities from Saudi social media content,
including companies, tickers, monetary amounts, percentages, and financial instruments.
"""

import re
from typing import List, Dict, Tuple, Optional, Set
from dataclasses import dataclass
from pathlib import Path
import json

try:
    from transformers import pipeline, AutoTokenizer, AutoModelForTokenClassification
    import torch
except ImportError:
    print("Warning: Transformers not available. Install with: pip install transformers")

@dataclass
class FinancialEntity:
    """Container for extracted financial entity"""
    text: str
    label: str
    start: int
    end: int
    confidence: float
    metadata: Dict = None


@dataclass
class NERResult:
    """Container for NER results"""
    entities: List[FinancialEntity]
    summary: Dict[str, List[str]]
    metadata: Dict


class FinancialNER:
    """
    Financial Named Entity Recognition for Arabic text
    
    Combines:
    - Pre-trained Arabic NER models
    - Rule-based extraction for financial patterns
    - Saudi company and ticker gazetteers
    - Financial instrument recognition
    """
    
    def __init__(self, 
                 model_name: str = "hatmimoha/arabic-ner",
                 use_rules: bool = True,
                 confidence_threshold: float = 0.8):
        
        self.model_name = model_name
        self.use_rules = use_rules
        self.confidence_threshold = confidence_threshold
        
        # Initialize models and resources
        self._load_ner_model()
        self._load_financial_gazetteers()
        self._compile_patterns()
        
    def _load_ner_model(self):
        """Load pre-trained Arabic NER model"""
        try:
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            self.model = AutoModelForTokenClassification.from_pretrained(self.model_name)
            self.nlp_pipeline = pipeline(
                "ner",
                model=self.model,
                tokenizer=self.tokenizer,
                aggregation_strategy="simple",
                device=0 if torch.cuda.is_available() else -1
            )
            print(f"Loaded NER model: {self.model_name}")
        except Exception as e:
            print(f"Warning: Could not load NER model {self.model_name}: {e}")
            self.nlp_pipeline = None
    
    def _load_financial_gazetteers(self):
        """Load comprehensive Saudi financial entity gazetteers"""
        
        # Major Saudi companies with detailed metadata
        self.companies = {
            # Banking Sector
            'الراجحي': {'ticker': '1120', 'sector': 'مصارف', 'market_cap': 'large', 'aliases': ['مصرف الراجحي', 'بنك الراجحي']},
            'الأهلي': {'ticker': '1180', 'sector': 'مصارف', 'market_cap': 'large', 'aliases': ['البنك الأهلي', 'الأهلي التجاري']},
            'الرياض': {'ticker': '1010', 'sector': 'مصارف', 'market_cap': 'large', 'aliases': ['بنك الرياض', 'مصرف الرياض']},
            'سامبا': {'ticker': '1090', 'sector': 'مصارف', 'market_cap': 'large', 'aliases': ['سامبا المالية']},
            'البلاد': {'ticker': '1140', 'sector': 'مصارف', 'market_cap': 'medium', 'aliases': ['بنك البلاد']},
            'الجزيرة': {'ticker': '4200', 'sector': 'مصارف', 'market_cap': 'medium', 'aliases': ['بنك الجزيرة']},
            
            # Petrochemicals
            'سابك': {'ticker': '2010', 'sector': 'مواد أساسية', 'market_cap': 'large', 'aliases': ['الصناعات الأساسية', 'سابك العالمية']},
            'ينبع': {'ticker': '2290', 'sector': 'مواد أساسية', 'market_cap': 'large', 'aliases': ['ينبع الوطنية']},
            'المتقدمة': {'ticker': '2330', 'sector': 'مواد أساسية', 'market_cap': 'large', 'aliases': ['الصناعات المتقدمة']},
            'كيان': {'ticker': '2350', 'sector': 'مواد أساسية', 'market_cap': 'medium', 'aliases': ['كيان السعودية']},
            'سبكيم': {'ticker': '2380', 'sector': 'مواد أساسية', 'market_cap': 'medium', 'aliases': ['سبكيم العالمية']},
            
            # Energy
            'أرامكو': {'ticker': '2222', 'sector': 'طاقة', 'market_cap': 'mega', 'aliases': ['أرامكو السعودية', 'الزيت العربية']},
            'الكهرباء': {'ticker': '5110', 'sector': 'مرافق', 'market_cap': 'large', 'aliases': ['الكهرباء السعودية']},
            'أكوا باور': {'ticker': '2082', 'sector': 'مرافق', 'market_cap': 'large', 'aliases': ['اكوا باور']},
            
            # Telecommunications
            'الاتصالات': {'ticker': '7010', 'sector': 'اتصالات', 'market_cap': 'large', 'aliases': ['الاتصالات السعودية', 'stc']},
            'موبايلي': {'ticker': '7020', 'sector': 'اتصالات', 'market_cap': 'large', 'aliases': ['اتحاد اتصالات']},
            'زين': {'ticker': '7030', 'sector': 'اتصالات', 'market_cap': 'medium', 'aliases': ['زين السعودية']},
            
            # Retail & Consumer
            'العثيم': {'ticker': '4001', 'sector': 'تجارة تجزئة', 'market_cap': 'large', 'aliases': ['مجموعة العثيم']},
            'بن داود': {'ticker': '4008', 'sector': 'تجارة تجزئة', 'market_cap': 'medium', 'aliases': ['بن داود القابضة']},
            'جرير': {'ticker': '4190', 'sector': 'تجارة تجزئة', 'market_cap': 'large', 'aliases': ['مكتبة جرير']},
            
            # Real Estate
            'دار الأركان': {'ticker': '4320', 'sector': 'عقار', 'market_cap': 'large', 'aliases': ['الأركان']},
            'جبل عمر': {'ticker': '4250', 'sector': 'عقار', 'market_cap': 'large', 'aliases': ['جبل عمر التنمية']},
            'طيبة': {'ticker': '4090', 'sector': 'عقار', 'market_cap': 'medium', 'aliases': ['طيبة للاستثمار']},
            
            # Healthcare
            'دله': {'ticker': '4004', 'sector': 'رعاية صحية', 'market_cap': 'large', 'aliases': ['دله الصحية']},
            'المواساة': {'ticker': '4002', 'sector': 'رعاية صحية', 'market_cap': 'large', 'aliases': ['مستشفى المواساة']},
            'سليمان الحبيب': {'ticker': '4013', 'sector': 'رعاية صحية', 'market_cap': 'large', 'aliases': ['د. سليمان الحبيب']},
            
            # Industrial
            'معادن': {'ticker': '1211', 'sector': 'مواد أساسية', 'market_cap': 'large', 'aliases': ['التعدين العربية']},
            'أسمنت العربية': {'ticker': '3010', 'sector': 'مواد البناء', 'market_cap': 'large', 'aliases': ['الأسمنت العربية']},
            'يانساب': {'ticker': '2001', 'sector': 'مواد أساسية', 'market_cap': 'large', 'aliases': ['ينبع الوطنية للبتروكيماويات']}
        }
        
        # Create reverse mappings
        self.ticker_to_company = {v['ticker']: k for k, v in self.companies.items()}
        
        # Build alias mapping
        self.company_aliases = {}
        for company, data in self.companies.items():
            self.company_aliases[company] = company
            for alias in data.get('aliases', []):
                self.company_aliases[alias] = company
        
        # Financial instruments and terms
        self.financial_instruments = {
            'أسهم', 'سهم', 'سندات', 'سند', 'صكوك', 'صك', 'وحدات', 'وحدة',
            'محفظة', 'استثمار', 'صندوق', 'ريت', 'مؤشر', 'خيارات', 'مشتقات'
        }
        
        # Market terms
        self.market_terms = {
            'تداول', 'السوق', 'البورصة', 'المؤشر', 'القطاع', 'الجلسة',
            'الافتتاح', 'الإغلاق', 'التداول', 'السيولة', 'الحجم', 'القيمة'
        }
        
        print(f"Loaded {len(self.companies)} companies and {len(self.financial_instruments)} financial terms")
    
    def _compile_patterns(self):
        """Compile regex patterns for financial entity extraction"""
        
        # Monetary amounts
        self.amount_patterns = [
            r'\d+\.?\d*\s*(?:ريال|ر\.س|SAR|sar)',
            r'\d+\.?\d*\s*(?:مليون|مليار|ألف|trillion|billion|million|thousand)',
            r'\d+\.?\d*\s*(?:م\.ر|ب\.ر)',  # Million/Billion Riyals abbreviations
        ]
        
        # Percentages
        self.percentage_pattern = r'[+-]?\d+\.?\d*\s*%'
        
        # Ticker patterns (4-digit codes)
        self.ticker_pattern = r'\b\d{4}\b'
        
        # Price patterns
        self.price_patterns = [
            r'\d+\.?\d*\s*(?:نقطة|point|pts)',
            r'(?:سعر|price)\s*:?\s*\d+\.?\d*',
            r'\d+\.?\d*\s*(?:للسهم|per share)'
        ]
        
        # Date patterns (Arabic)
        self.date_patterns = [
            r'\d{1,2}[/-]\d{1,2}[/-]\d{2,4}',
            r'\d{4}[/-]\d{1,2}[/-]\d{1,2}',
            r'(?:يناير|فبراير|مارس|أبريل|مايو|يونيو|يوليو|أغسطس|سبتمبر|أكتوبر|نوفمبر|ديسمبر)\s*\d{4}',
            r'(?:الربع)\s*(?:الأول|الثاني|الثالث|الرابع)\s*\d{4}'
        ]
        
        print("Compiled financial extraction patterns")
    
    def extract_with_model(self, text: str) -> List[FinancialEntity]:
        """Extract entities using pre-trained NER model"""
        entities = []
        
        if not self.nlp_pipeline:
            return entities
        
        try:
            results = self.nlp_pipeline(text)
            
            for result in results:
                if result['score'] >= self.confidence_threshold:
                    entity = FinancialEntity(
                        text=result['word'],
                        label=result['entity_group'],
                        start=result['start'],
                        end=result['end'],
                        confidence=result['score'],
                        metadata={'source': 'model'}
                    )
                    entities.append(entity)
        
        except Exception as e:
            print(f"Warning: NER model extraction failed: {e}")
        
        return entities
    
    def extract_with_rules(self, text: str) -> List[FinancialEntity]:
        """Extract entities using rule-based patterns"""
        entities = []
        
        # Extract companies and tickers
        for company_name, company_data in self.companies.items():
            # Find company mentions
            pattern = r'\b' + re.escape(company_name) + r'\b'
            for match in re.finditer(pattern, text):
                entity = FinancialEntity(
                    text=match.group(),
                    label='COMPANY',
                    start=match.start(),
                    end=match.end(),
                    confidence=1.0,
                    metadata={
                        'source': 'rules',
                        'ticker': company_data['ticker'],
                        'sector': company_data['sector'],
                        'market_cap': company_data['market_cap']
                    }
                )
                entities.append(entity)
            
            # Find aliases
            for alias in company_data.get('aliases', []):
                pattern = r'\b' + re.escape(alias) + r'\b'
                for match in re.finditer(pattern, text):
                    entity = FinancialEntity(
                        text=match.group(),
                        label='COMPANY',
                        start=match.start(),
                        end=match.end(),
                        confidence=0.9,
                        metadata={
                            'source': 'rules',
                            'canonical_name': company_name,
                            'ticker': company_data['ticker'],
                            'sector': company_data['sector']
                        }
                    )
                    entities.append(entity)
        
        # Extract monetary amounts
        for pattern in self.amount_patterns:
            for match in re.finditer(pattern, text, re.IGNORECASE):
                entity = FinancialEntity(
                    text=match.group(),
                    label='MONEY',
                    start=match.start(),
                    end=match.end(),
                    confidence=0.95,
                    metadata={'source': 'rules', 'pattern': 'amount'}
                )
                entities.append(entity)
        
        # Extract percentages
        for match in re.finditer(self.percentage_pattern, text):
            entity = FinancialEntity(
                text=match.group(),
                label='PERCENT',
                start=match.start(),
                end=match.end(),
                confidence=0.98,
                metadata={'source': 'rules', 'pattern': 'percentage'}
            )
            entities.append(entity)
        
        # Extract tickers
        for match in re.finditer(self.ticker_pattern, text):
            ticker = match.group()
            if ticker in self.ticker_to_company:
                entity = FinancialEntity(
                    text=ticker,
                    label='TICKER',
                    start=match.start(),
                    end=match.end(),
                    confidence=1.0,
                    metadata={
                        'source': 'rules',
                        'company': self.ticker_to_company[ticker],
                        'pattern': 'ticker'
                    }
                )
                entities.append(entity)
        
        # Extract financial instruments
        for instrument in self.financial_instruments:
            pattern = r'\b' + re.escape(instrument) + r'\b'
            for match in re.finditer(pattern, text):
                entity = FinancialEntity(
                    text=match.group(),
                    label='INSTRUMENT',
                    start=match.start(),
                    end=match.end(),
                    confidence=0.9,
                    metadata={'source': 'rules', 'pattern': 'instrument'}
                )
                entities.append(entity)
        
        return entities
    
    def merge_entities(self, entities: List[FinancialEntity]) -> List[FinancialEntity]:
        """Merge overlapping entities and resolve conflicts"""
        if not entities:
            return entities
        
        # Sort by start position
        sorted_entities = sorted(entities, key=lambda x: x.start)
        merged = []
        
        for entity in sorted_entities:
            # Check for overlap with last merged entity
            if merged and entity.start < merged[-1].end:
                # Overlapping entities - keep the one with higher confidence
                if entity.confidence > merged[-1].confidence:
                    merged[-1] = entity
            else:
                merged.append(entity)
        
        return merged
    
    def extract_entities(self, text: str) -> NERResult:
        """
        Extract all financial entities from text
        
        Args:
            text: Input Arabic text
            
        Returns:
            NERResult with extracted entities and summary
        """
        all_entities = []
        
        # Extract with model
        if self.nlp_pipeline:
            model_entities = self.extract_with_model(text)
            all_entities.extend(model_entities)
        
        # Extract with rules
        if self.use_rules:
            rule_entities = self.extract_with_rules(text)
            all_entities.extend(rule_entities)
        
        # Merge overlapping entities
        merged_entities = self.merge_entities(all_entities)
        
        # Create summary
        summary = {
            'companies': [],
            'tickers': [],
            'money': [],
            'percentages': [],
            'instruments': [],
            'other': []
        }
        
        for entity in merged_entities:
            if entity.label == 'COMPANY':
                summary['companies'].append(entity.text)
            elif entity.label == 'TICKER':
                summary['tickers'].append(entity.text)
            elif entity.label == 'MONEY':
                summary['money'].append(entity.text)
            elif entity.label == 'PERCENT':
                summary['percentages'].append(entity.text)
            elif entity.label == 'INSTRUMENT':
                summary['instruments'].append(entity.text)
            else:
                summary['other'].append(entity.text)
        
        # Remove duplicates
        for key in summary:
            summary[key] = list(set(summary[key]))
        
        metadata = {
            'total_entities': len(merged_entities),
            'entity_counts': {label: len(entities) for label, entities in summary.items()},
            'extraction_methods': ['model'] if self.nlp_pipeline else [] + ['rules'] if self.use_rules else []
        }
        
        return NERResult(
            entities=merged_entities,
            summary=summary,
            metadata=metadata
        )
