"""
Storage Manager for Saudi NLP Trading

Handles data storage with pluggable backends (local filesystem, GCS).
Implements partitioning, JSONL format, and data contracts.
"""

import json
import gzip
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Dict, List, Any, Optional, Iterator
from datetime import datetime
import os

from ..config.settings import get_config, RIYADH_TZ
from ..utils.logging import get_logger
from ..utils.manifest import ManifestManager

class StorageBackend(ABC):
    """Abstract storage backend"""
    
    @abstractmethod
    def write_jsonl(self, data: List[Dict[str, Any]], file_path: str) -> int:
        """Write data as JSONL format"""
        pass
    
    @abstractmethod
    def read_jsonl(self, file_path: str) -> Iterator[Dict[str, Any]]:
        """Read data from JSONL format"""
        pass
    
    @abstractmethod
    def exists(self, file_path: str) -> bool:
        """Check if file exists"""
        pass
    
    @abstractmethod
    def list_files(self, prefix: str) -> List[str]:
        """List files with prefix"""
        pass
    
    @abstractmethod
    def get_file_size(self, file_path: str) -> int:
        """Get file size in bytes"""
        pass

class LocalStorageBackend(StorageBackend):
    """Local filesystem storage backend"""
    
    def __init__(self, base_path: str = "data"):
        self.base_path = Path(base_path)
        self.logger = get_logger(__name__)
        
        # Create base directory
        self.base_path.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(
            "Initialized local storage backend",
            base_path=str(self.base_path.absolute())
        )
    
    def write_jsonl(self, data: List[Dict[str, Any]], file_path: str) -> int:
        """Write data as JSONL to local file"""
        
        full_path = self.base_path / file_path
        full_path.parent.mkdir(parents=True, exist_ok=True)
        
        bytes_written = 0
        
        try:
            # Determine if we should compress
            use_compression = file_path.endswith('.gz')
            
            if use_compression:
                with gzip.open(full_path, 'wt', encoding='utf-8') as f:
                    for item in data:
                        line = json.dumps(item, ensure_ascii=False, separators=(',', ':'))
                        f.write(line + '\n')
                        bytes_written += len(line.encode('utf-8')) + 1
            else:
                with open(full_path, 'w', encoding='utf-8') as f:
                    for item in data:
                        line = json.dumps(item, ensure_ascii=False, separators=(',', ':'))
                        f.write(line + '\n')
                        bytes_written += len(line.encode('utf-8')) + 1
            
            self.logger.debug(
                "Wrote JSONL file",
                file_path=file_path,
                records=len(data),
                bytes_written=bytes_written
            )
            
            return bytes_written
            
        except Exception as e:
            self.logger.error(
                "Failed to write JSONL file",
                file_path=file_path,
                exception_class=e.__class__.__name__,
                exc_info=True
            )
            raise
    
    def read_jsonl(self, file_path: str) -> Iterator[Dict[str, Any]]:
        """Read data from JSONL file"""
        
        full_path = self.base_path / file_path
        
        if not full_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        try:
            # Determine if file is compressed
            use_compression = file_path.endswith('.gz')
            
            if use_compression:
                file_handle = gzip.open(full_path, 'rt', encoding='utf-8')
            else:
                file_handle = open(full_path, 'r', encoding='utf-8')
            
            with file_handle as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue
                    
                    try:
                        yield json.loads(line)
                    except json.JSONDecodeError as e:
                        self.logger.warning(
                            "Invalid JSON line",
                            file_path=file_path,
                            line_num=line_num,
                            error=str(e)
                        )
                        continue
        
        except Exception as e:
            self.logger.error(
                "Failed to read JSONL file",
                file_path=file_path,
                exception_class=e.__class__.__name__,
                exc_info=True
            )
            raise
    
    def exists(self, file_path: str) -> bool:
        """Check if file exists"""
        full_path = self.base_path / file_path
        return full_path.exists()
    
    def list_files(self, prefix: str) -> List[str]:
        """List files with prefix"""
        prefix_path = self.base_path / prefix
        
        if not prefix_path.exists():
            return []
        
        files = []
        if prefix_path.is_file():
            files.append(str(prefix_path.relative_to(self.base_path)))
        else:
            for file_path in prefix_path.rglob('*'):
                if file_path.is_file():
                    files.append(str(file_path.relative_to(self.base_path)))
        
        return sorted(files)
    
    def get_file_size(self, file_path: str) -> int:
        """Get file size in bytes"""
        full_path = self.base_path / file_path
        return full_path.stat().st_size if full_path.exists() else 0

class GCSStorageBackend(StorageBackend):
    """Google Cloud Storage backend"""
    
    def __init__(self, bucket_name: str, project_id: Optional[str] = None):
        self.bucket_name = bucket_name
        self.project_id = project_id
        self.logger = get_logger(__name__)
        
        try:
            from google.cloud import storage
            self.client = storage.Client(project=project_id)
            self.bucket = self.client.bucket(bucket_name)
            
            self.logger.info(
                "Initialized GCS storage backend",
                bucket=bucket_name,
                project=project_id
            )
        except ImportError:
            raise ImportError("google-cloud-storage package required for GCS backend")
        except Exception as e:
            self.logger.error(
                "Failed to initialize GCS backend",
                bucket=bucket_name,
                project=project_id,
                exception_class=e.__class__.__name__,
                exc_info=True
            )
            raise
    
    def write_jsonl(self, data: List[Dict[str, Any]], file_path: str) -> int:
        """Write data as JSONL to GCS"""
        
        try:
            # Prepare JSONL content
            lines = []
            for item in data:
                line = json.dumps(item, ensure_ascii=False, separators=(',', ':'))
                lines.append(line)
            
            content = '\n'.join(lines) + '\n'
            content_bytes = content.encode('utf-8')
            
            # Upload to GCS
            blob = self.bucket.blob(file_path)
            
            # Set content type
            blob.content_type = 'application/jsonl'
            if file_path.endswith('.gz'):
                blob.content_encoding = 'gzip'
                # Compress content
                import gzip
                content_bytes = gzip.compress(content_bytes)
            
            blob.upload_from_string(content_bytes)
            
            self.logger.debug(
                "Wrote JSONL to GCS",
                file_path=file_path,
                records=len(data),
                bytes_written=len(content_bytes)
            )
            
            return len(content_bytes)
            
        except Exception as e:
            self.logger.error(
                "Failed to write JSONL to GCS",
                file_path=file_path,
                exception_class=e.__class__.__name__,
                exc_info=True
            )
            raise
    
    def read_jsonl(self, file_path: str) -> Iterator[Dict[str, Any]]:
        """Read data from JSONL in GCS"""
        
        try:
            blob = self.bucket.blob(file_path)
            
            if not blob.exists():
                raise FileNotFoundError(f"GCS file not found: {file_path}")
            
            # Download content
            content_bytes = blob.download_as_bytes()
            
            # Decompress if needed
            if file_path.endswith('.gz'):
                import gzip
                content_bytes = gzip.decompress(content_bytes)
            
            content = content_bytes.decode('utf-8')
            
            # Parse JSONL
            for line_num, line in enumerate(content.splitlines(), 1):
                line = line.strip()
                if not line:
                    continue
                
                try:
                    yield json.loads(line)
                except json.JSONDecodeError as e:
                    self.logger.warning(
                        "Invalid JSON line in GCS file",
                        file_path=file_path,
                        line_num=line_num,
                        error=str(e)
                    )
                    continue
        
        except Exception as e:
            self.logger.error(
                "Failed to read JSONL from GCS",
                file_path=file_path,
                exception_class=e.__class__.__name__,
                exc_info=True
            )
            raise
    
    def exists(self, file_path: str) -> bool:
        """Check if file exists in GCS"""
        blob = self.bucket.blob(file_path)
        return blob.exists()
    
    def list_files(self, prefix: str) -> List[str]:
        """List files with prefix in GCS"""
        blobs = self.client.list_blobs(self.bucket, prefix=prefix)
        return [blob.name for blob in blobs]
    
    def get_file_size(self, file_path: str) -> int:
        """Get file size in GCS"""
        blob = self.bucket.blob(file_path)
        blob.reload()
        return blob.size if blob.exists() else 0

class StorageManager:
    """Main storage manager with partitioning and data contracts"""
    
    def __init__(self, run_id: str):
        self.run_id = run_id
        self.config = get_config()
        self.logger = get_logger(__name__)
        
        # Initialize backend
        if self.config.storage.backend == 'gcs':
            self.backend = GCSStorageBackend(
                self.config.storage.gcs_bucket,
                self.config.storage.gcs_project_id
            )
        else:
            self.backend = LocalStorageBackend(self.config.storage.local_base_path)
        
        # Initialize manifest manager
        self.manifest_manager = ManifestManager(run_id)
        
        self.logger.info(
            "Initialized storage manager",
            run_id=run_id,
            backend=self.config.storage.backend
        )
    
    def store_raw_documents(self, documents: List[Dict[str, Any]], 
                          source: str, date: Optional[str] = None) -> str:
        """
        Store raw documents with partitioning
        
        Returns:
            File path where documents were stored
        """
        
        if not documents:
            return ""
        
        # Generate partition path
        if date is None:
            date = datetime.now(RIYADH_TZ).strftime('%Y-%m-%d')
        
        # Create file path with partitioning
        part_num = self._get_next_part_number(source, date)
        file_path = f"raw/source={source}/date={date}/part-{part_num:04d}.jsonl"
        
        # Check if we should compress
        total_size = sum(len(json.dumps(doc).encode('utf-8')) for doc in documents)
        if total_size > 10 * 1024 * 1024:  # > 10MB
            file_path += '.gz'
        
        try:
            # Store documents
            bytes_written = self.backend.write_jsonl(documents, file_path)
            
            # Add to manifest
            self.manifest_manager.add_file(
                Path(file_path),
                category='raw_data',
                metadata={
                    'source': source,
                    'date': date,
                    'part_number': part_num,
                    'record_count': len(documents),
                    'compressed': file_path.endswith('.gz')
                }
            )
            
            self.logger.info(
                "Stored raw documents",
                file_path=file_path,
                source=source,
                date=date,
                records=len(documents),
                bytes_written=bytes_written
            )
            
            return file_path
            
        except Exception as e:
            self.logger.error(
                "Failed to store raw documents",
                source=source,
                date=date,
                records=len(documents),
                exception_class=e.__class__.__name__,
                exc_info=True
            )
            raise
    
    def store_processed_documents(self, documents: List[Dict[str, Any]], 
                                source: str, date: Optional[str] = None) -> str:
        """Store processed documents with partitioning"""
        
        if not documents:
            return ""
        
        # Generate partition path
        if date is None:
            date = datetime.now(RIYADH_TZ).strftime('%Y-%m-%d')
        
        part_num = self._get_next_part_number(source, date, data_type='processed')
        file_path = f"processed/source={source}/date={date}/part-{part_num:04d}.jsonl"
        
        # Check if we should compress
        total_size = sum(len(json.dumps(doc).encode('utf-8')) for doc in documents)
        if total_size > 10 * 1024 * 1024:  # > 10MB
            file_path += '.gz'
        
        try:
            bytes_written = self.backend.write_jsonl(documents, file_path)
            
            # Add to manifest
            self.manifest_manager.add_file(
                Path(file_path),
                category='processed_data',
                metadata={
                    'source': source,
                    'date': date,
                    'part_number': part_num,
                    'record_count': len(documents),
                    'compressed': file_path.endswith('.gz')
                }
            )
            
            self.logger.info(
                "Stored processed documents",
                file_path=file_path,
                source=source,
                date=date,
                records=len(documents),
                bytes_written=bytes_written
            )
            
            return file_path
            
        except Exception as e:
            self.logger.error(
                "Failed to store processed documents",
                source=source,
                date=date,
                records=len(documents),
                exception_class=e.__class__.__name__,
                exc_info=True
            )
            raise
    
    def _get_next_part_number(self, source: str, date: str, data_type: str = 'raw') -> int:
        """Get next part number for partitioned storage"""
        
        prefix = f"{data_type}/source={source}/date={date}/"
        existing_files = self.backend.list_files(prefix)
        
        # Find highest part number
        max_part = 0
        for file_path in existing_files:
            filename = Path(file_path).name
            if filename.startswith('part-'):
                try:
                    part_str = filename.split('-')[1].split('.')[0]
                    part_num = int(part_str)
                    max_part = max(max_part, part_num)
                except (ValueError, IndexError):
                    continue
        
        return max_part + 1
    
    def read_raw_documents(self, source: str, date: str) -> Iterator[Dict[str, Any]]:
        """Read raw documents for a source and date"""
        
        prefix = f"raw/source={source}/date={date}/"
        files = self.backend.list_files(prefix)
        
        for file_path in sorted(files):
            try:
                for document in self.backend.read_jsonl(file_path):
                    yield document
            except Exception as e:
                self.logger.warning(
                    "Failed to read file",
                    file_path=file_path,
                    exception_class=e.__class__.__name__
                )
    
    def finalize_storage(self) -> Path:
        """Finalize storage and create manifest"""
        return self.manifest_manager.save_manifest()
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """Get storage statistics"""
        return self.manifest_manager.get_manifest_summary()
