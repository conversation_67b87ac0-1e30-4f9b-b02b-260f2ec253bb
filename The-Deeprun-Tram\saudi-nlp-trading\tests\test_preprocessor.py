import pytest
from src.preprocessors.arabic_normalizer import ArabicTextProcessor

def test_preprocess():
    """Simple test for the Arabic text processor."""
    processor = ArabicTextProcessor()
    sample_text = "وش رايك في سهم 2222؟؟؟"
    result = processor.preprocess(sample_text)
    assert 'clean_text' in result
    assert 'tokens' in result
    assert 'tickers' in result
    assert result['tickers'] == ['2222']
