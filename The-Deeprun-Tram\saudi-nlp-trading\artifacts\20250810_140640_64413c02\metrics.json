{"schema_version": "1.1", "run_id": "20250810_140640_64413c02", "timestamp": "2025-08-10T14:06:40.329952+03:00", "scraping_metrics": {"total_threads": 3, "total_posts": 20, "posts_per_thread": 6.666666666666667, "success_rate": 1.0, "http_status_histogram": {"200": 20}, "retry_histogram": {"0": 20}, "robots_blocked_count": 0}, "performance_metrics": {"total_processing_time_seconds": 0.009094953536987305, "avg_processing_time_per_post": 0.0004547476768493652, "posts_per_second": 2199.02167929326, "memory_usage_mb": 50, "peak_memory_mb": 75}, "quality_metrics": {"arabic_posts_ratio": 1.0, "financial_content_ratio": 0.9, "avg_post_length": 68.45, "empty_posts_count": 0, "language_distribution": {"ar": 20}}, "schema_compliance": {"v11_fields_present": 1.0, "required_fields_coverage": 1.0, "enhanced_fields_coverage": 1.0}, "runtime": {"run_id": "20250810_140640_64413c02", "started_at": "2025-08-10T14:06:40.323857+03:00", "ended_at": "2025-08-10T14:07:28.324895+03:00", "duration_seconds": 48.0, "python_version": "3.12.10", "platform": "Windows-11-10.0.26100-SP0", "git_commit": "a20887421bbfbe74d1b4f229f59cec959109f76b", "config_snapshot": {"requests_per_minute": 30, "rate_limit": {"capacity": 7, "refill_rate": 0.5}}}}