#!/usr/bin/env python3
"""
Test Script for Scrape-Only Foundation

Simple test script to verify all core components work correctly.
Tests configuration, logging, robots.txt, retry logic, and storage.
"""

import os
import sys
import uuid
import tempfile
import json
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_configuration():
    """Test configuration system"""
    print("Testing configuration system...")
    
    try:
        from config.settings import ConfigLoader, get_config, set_run_id
        
        # Set some environment variables
        os.environ["SCRAPER_MAX_RPM"] = "60"
        os.environ["MODE_SCRAPE_ONLY"] = "true"
        os.environ["NLP_ENABLE"] = "false"
        
        # Load configuration
        config = get_config(reload=True)
        
        assert config.scraper.max_rpm == 60
        assert config.mode.scrape_only == True
        assert config.mode.nlp_enable == False
        
        print("✓ Configuration system working")
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False

def test_logging():
    """Test structured logging"""
    print("Testing structured logging...")
    
    try:
        from utils.logging import get_logger, get_performance_tracker
        import time
        
        # Create logger
        logger = get_logger("test_logger")
        
        # Test structured logging
        logger.info("Test message", test_field="test_value", stage="test")
        
        # Test performance tracking
        tracker = get_performance_tracker("test_performance")
        
        with tracker.track_operation("test_operation", url="http://example.com"):
            time.sleep(0.01)  # Simulate work
        
        # Check metrics
        metrics = tracker.get_metrics_summary()
        assert "test_operation" in metrics
        
        print("✓ Structured logging working")
        return True
        
    except Exception as e:
        print(f"✗ Logging test failed: {e}")
        return False

def test_robots():
    """Test robots.txt handling"""
    print("Testing robots.txt handling...")
    
    try:
        from utils.robots import get_robots_checker, get_crawl_delay_manager
        
        # Create robots checker
        checker = get_robots_checker()
        
        # Test can_fetch (will use cached or fetch robots.txt)
        can_fetch, policy, crawl_delay = checker.can_fetch("http://example.com/test")
        
        assert isinstance(can_fetch, bool)
        assert policy in ["allowed", "blocked", "robots_disabled", "no_robots_txt", "error"]
        
        # Test crawl delay manager
        manager = get_crawl_delay_manager()
        wait_time = manager.wait_if_needed("http://example.com/test1")
        
        assert wait_time >= 0
        
        print("✓ Robots.txt handling working")
        return True
        
    except Exception as e:
        print(f"✗ Robots.txt test failed: {e}")
        return False

def test_retry_logic():
    """Test retry and backoff system"""
    print("Testing retry logic...")
    
    try:
        from utils.retry import get_retry_manager, RetryableError, NonRetryableError
        
        manager = get_retry_manager()
        
        # Test successful function
        def success_func():
            return "success"
        
        result = manager.execute_with_retry(success_func)
        assert result == "success"
        
        # Test function that fails then succeeds
        call_count = 0
        def fail_then_succeed():
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                raise RetryableError("First attempt fails")
            return "success"
        
        result = manager.execute_with_retry(fail_then_succeed)
        assert result == "success"
        assert call_count == 2
        
        print("✓ Retry logic working")
        return True
        
    except Exception as e:
        print(f"✗ Retry logic test failed: {e}")
        return False

def test_manifest_system():
    """Test manifest and checksum system"""
    print("Testing manifest system...")
    
    try:
        from utils.manifest import ManifestManager, FileManifest
        
        run_id = str(uuid.uuid4())
        manager = ManifestManager(run_id)
        
        # Create test file
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
            f.write("test content for manifest")
            temp_path = Path(f.name)
        
        try:
            # Add file to manifest
            manager.add_file(temp_path, "test_data")
            
            # Check manifest content
            assert manager.manifest['stats']['total_files'] == 1
            assert temp_path.name in manager.manifest['files']
            
            # Save manifest
            manifest_file = manager.save_manifest()
            assert manifest_file.exists()
            
            # Validate manifest
            is_valid = manager.validate_manifest()
            assert is_valid == True
            
            print("✓ Manifest system working")
            return True
            
        finally:
            temp_path.unlink()
            if manager.manifest_file.exists():
                manager.manifest_file.unlink()
        
    except Exception as e:
        print(f"✗ Manifest system test failed: {e}")
        return False

def test_storage_system():
    """Test storage backends"""
    print("Testing storage system...")
    
    try:
        from storage.storage_manager import LocalStorageBackend, StorageManager
        from config.settings import set_run_id
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Test local storage backend
            backend = LocalStorageBackend(temp_dir)
            
            # Test data
            test_data = [
                {"id": 1, "content": "test content 1"},
                {"id": 2, "content": "test content 2"}
            ]
            
            # Write JSONL
            file_path = "test/data.jsonl"
            bytes_written = backend.write_jsonl(test_data, file_path)
            assert bytes_written > 0
            
            # Check file exists
            assert backend.exists(file_path) == True
            
            # Read JSONL
            read_data = list(backend.read_jsonl(file_path))
            assert len(read_data) == 2
            assert read_data[0]["id"] == 1
            
            print("✓ Storage system working")
            return True
        
    except Exception as e:
        print(f"✗ Storage system test failed: {e}")
        return False

def test_mode_manager():
    """Test mode management"""
    print("Testing mode manager...")
    
    try:
        from core.mode_manager import get_mode_manager, require_nlp_enabled
        
        # Set scrape-only mode
        os.environ["MODE_SCRAPE_ONLY"] = "true"
        os.environ["NLP_ENABLE"] = "false"
        
        # Reload config
        from config.settings import get_config
        config = get_config(reload=True)
        
        manager = get_mode_manager()
        assert manager.is_scrape_only() == True
        assert manager.is_nlp_enabled() == False
        
        # Test NLP decorator
        @require_nlp_enabled
        def nlp_function():
            return "nlp_result"
        
        # Should return None in scrape-only mode
        result = nlp_function()
        assert result is None
        
        print("✓ Mode manager working")
        return True
        
    except Exception as e:
        print(f"✗ Mode manager test failed: {e}")
        return False

def test_integration():
    """Test complete integration"""
    print("Testing complete integration...")
    
    try:
        from config.settings import set_run_id, get_config
        from utils.logging import get_logger
        from storage.storage_manager import StorageManager
        
        run_id = str(uuid.uuid4())
        set_run_id(run_id)
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Set local storage
            os.environ["STORAGE_BACKEND"] = "local"
            config = get_config(reload=True)
            config.storage.local_base_path = temp_dir
            
            # Initialize components
            logger = get_logger("integration_test")
            storage_manager = StorageManager(run_id)
            
            # Simulate scraped data
            scraped_data = [
                {
                    "run_id": run_id,
                    "source": "hawamer",
                    "thread_id": "12345",
                    "post_id": "67890",
                    "url": "https://hawamer.com/vb/hawamer12345",
                    "scraped_at": "2024-08-10T10:00:00+03:00",
                    "author_hash": "abc123",
                    "raw_html": "<div>Test post</div>",
                    "raw_text": "Test post",
                    "visible_text": "Test post",
                    "likes": 5,
                    "reply_to_id": None,
                    "page_no": 1,
                    "lang_detect": "ar",
                    "http_status": 200,
                    "retry_count": 0,
                    "robot_policy": "allowed"
                }
            ]
            
            # Store data
            file_path = storage_manager.store_raw_documents(scraped_data, "hawamer")
            assert file_path != ""
            
            # Finalize storage
            manifest_file = storage_manager.finalize_storage()
            assert manifest_file.exists()
            
            # Log success
            logger.info(
                "Integration test completed successfully",
                run_id=run_id,
                file_path=file_path,
                manifest_file=str(manifest_file)
            )
            
            print("✓ Integration test working")
            return True
        
    except Exception as e:
        print(f"✗ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("="*80)
    print("SCRAPE-ONLY FOUNDATION TEST SUITE")
    print("="*80)
    
    tests = [
        test_configuration,
        test_logging,
        test_robots,
        test_retry_logic,
        test_manifest_system,
        test_storage_system,
        test_mode_manager,
        test_integration
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ {test.__name__} failed with exception: {e}")
            failed += 1
        print()
    
    print("="*80)
    print(f"TEST RESULTS: {passed} passed, {failed} failed")
    print("="*80)
    
    if failed == 0:
        print("🎉 ALL TESTS PASSED! Foundation is solid.")
        return True
    else:
        print("❌ Some tests failed. Check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
