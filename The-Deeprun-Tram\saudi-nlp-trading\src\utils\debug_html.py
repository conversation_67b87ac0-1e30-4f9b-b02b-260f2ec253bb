"""
Debug HTML Preservation for Saudi NLP Trading

Saves HTML samples alongside JSONL partitions for selector drift detection
and debugging. Preserves complete pages with metadata for analysis.
"""

import hashlib
import json
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

from .logging import get_logger
from ..config.settings import get_config, RIYADH_TZ

class DebugHTMLManager:
    """
    Manages debug HTML preservation alongside data partitions
    """
    
    def __init__(self, run_id: str):
        self.run_id = run_id
        self.config = get_config()
        self.logger = get_logger(__name__)
        self.enabled = self.config.scraper.save_raw_html
        
        # Base debug directory
        self.debug_base = Path("data/debug_html")
        self.debug_base.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(
            "Initialized debug HTML manager",
            run_id=run_id,
            enabled=self.enabled,
            debug_base=str(self.debug_base)
        )
    
    def save_page_html(self, html_content: str, url: str, 
                      thread_id: str, page_no: int,
                      partition_path: str) -> Optional[Path]:
        """
        Save complete HTML page for debugging
        
        Args:
            html_content: Raw HTML content
            url: Source URL
            thread_id: Thread identifier
            page_no: Page number in thread
            partition_path: Associated JSONL partition path
        
        Returns:
            Path to saved HTML file or None if disabled
        """
        
        if not self.enabled:
            return None
        
        try:
            # Create debug directory structure matching partition
            # e.g., data/debug_html/source=hawamer/date=2024-08-10/thread_12345/
            partition_parts = Path(partition_path).parts
            
            # Extract source and date from partition path
            source_part = next((p for p in partition_parts if p.startswith('source=')), 'source=unknown')
            date_part = next((p for p in partition_parts if p.startswith('date=')), 'date=unknown')
            
            debug_dir = self.debug_base / source_part / date_part / f"thread_{thread_id}"
            debug_dir.mkdir(parents=True, exist_ok=True)
            
            # Generate filename
            url_hash = hashlib.md5(url.encode('utf-8')).hexdigest()[:8]
            html_filename = f"page_{page_no:03d}_{url_hash}.html"
            html_file = debug_dir / html_filename
            
            # Save HTML content
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            # Save metadata
            metadata = {
                'url': url,
                'thread_id': thread_id,
                'page_no': page_no,
                'partition_path': partition_path,
                'saved_at': datetime.now(RIYADH_TZ).isoformat(),
                'run_id': self.run_id,
                'selector_version': self.config.scraper.user_agent,  # Proxy for selector version
                'html_size_bytes': len(html_content.encode('utf-8')),
                'url_hash': url_hash
            }
            
            metadata_file = debug_dir / f"page_{page_no:03d}_{url_hash}.json"
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
            
            self.logger.info(
                "Saved debug HTML",
                url=url,
                thread_id=thread_id,
                page_no=page_no,
                html_file=str(html_file),
                html_size_bytes=len(html_content.encode('utf-8'))
            )
            
            return html_file
            
        except Exception as e:
            self.logger.error(
                "Failed to save debug HTML",
                url=url,
                thread_id=thread_id,
                page_no=page_no,
                exception_class=e.__class__.__name__,
                exc_info=True
            )
            return None
    
    def save_thread_summary(self, thread_id: str, thread_metadata: Dict[str, Any],
                           partition_path: str) -> Optional[Path]:
        """
        Save thread-level summary for debugging
        
        Args:
            thread_id: Thread identifier
            thread_metadata: Thread metadata (title, total_pages, etc.)
            partition_path: Associated JSONL partition path
        
        Returns:
            Path to saved summary file
        """
        
        if not self.enabled:
            return None
        
        try:
            # Create debug directory
            partition_parts = Path(partition_path).parts
            source_part = next((p for p in partition_parts if p.startswith('source=')), 'source=unknown')
            date_part = next((p for p in partition_parts if p.startswith('date=')), 'date=unknown')
            
            debug_dir = self.debug_base / source_part / date_part / f"thread_{thread_id}"
            debug_dir.mkdir(parents=True, exist_ok=True)
            
            # Create thread summary
            summary = {
                'thread_id': thread_id,
                'run_id': self.run_id,
                'created_at': datetime.now(RIYADH_TZ).isoformat(),
                'partition_path': partition_path,
                'metadata': thread_metadata,
                'debug_files': []
            }
            
            # List existing debug files
            for html_file in debug_dir.glob("page_*.html"):
                summary['debug_files'].append({
                    'filename': html_file.name,
                    'size_bytes': html_file.stat().st_size,
                    'created_at': datetime.fromtimestamp(
                        html_file.stat().st_ctime, RIYADH_TZ
                    ).isoformat()
                })
            
            # Save summary
            summary_file = debug_dir / "thread_summary.json"
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, indent=2, ensure_ascii=False)
            
            self.logger.info(
                "Saved thread summary",
                thread_id=thread_id,
                summary_file=str(summary_file),
                debug_files_count=len(summary['debug_files'])
            )
            
            return summary_file
            
        except Exception as e:
            self.logger.error(
                "Failed to save thread summary",
                thread_id=thread_id,
                exception_class=e.__class__.__name__,
                exc_info=True
            )
            return None
    
    def get_debug_stats(self) -> Dict[str, Any]:
        """Get debug HTML preservation statistics"""
        
        if not self.enabled:
            return {'enabled': False}
        
        try:
            stats = {
                'enabled': True,
                'total_html_files': 0,
                'total_size_bytes': 0,
                'threads_count': 0,
                'sources': {}
            }
            
            # Scan debug directory
            for source_dir in self.debug_base.glob("source=*"):
                source_name = source_dir.name.split('=')[1]
                source_stats = {
                    'html_files': 0,
                    'size_bytes': 0,
                    'threads': 0,
                    'dates': []
                }
                
                for date_dir in source_dir.glob("date=*"):
                    date_name = date_dir.name.split('=')[1]
                    source_stats['dates'].append(date_name)
                    
                    for thread_dir in date_dir.glob("thread_*"):
                        source_stats['threads'] += 1
                        
                        for html_file in thread_dir.glob("*.html"):
                            source_stats['html_files'] += 1
                            source_stats['size_bytes'] += html_file.stat().st_size
                
                stats['sources'][source_name] = source_stats
                stats['total_html_files'] += source_stats['html_files']
                stats['total_size_bytes'] += source_stats['size_bytes']
                stats['threads_count'] += source_stats['threads']
            
            return stats
            
        except Exception as e:
            self.logger.error(
                "Failed to get debug stats",
                exception_class=e.__class__.__name__,
                exc_info=True
            )
            return {'enabled': True, 'error': str(e)}
    
    def cleanup_old_debug_files(self, days_to_keep: int = 7) -> int:
        """
        Clean up debug files older than specified days
        
        Args:
            days_to_keep: Number of days to keep debug files
        
        Returns:
            Number of files cleaned up
        """
        
        if not self.enabled:
            return 0
        
        try:
            from datetime import timedelta
            cutoff_time = datetime.now() - timedelta(days=days_to_keep)
            cutoff_timestamp = cutoff_time.timestamp()
            
            cleaned_count = 0
            
            for html_file in self.debug_base.rglob("*.html"):
                if html_file.stat().st_mtime < cutoff_timestamp:
                    # Also remove associated metadata file
                    metadata_file = html_file.with_suffix('.json')
                    
                    html_file.unlink()
                    if metadata_file.exists():
                        metadata_file.unlink()
                    
                    cleaned_count += 1
            
            # Remove empty directories
            for dir_path in self.debug_base.rglob("*"):
                if dir_path.is_dir() and not any(dir_path.iterdir()):
                    dir_path.rmdir()
            
            self.logger.info(
                "Cleaned up old debug files",
                days_to_keep=days_to_keep,
                files_cleaned=cleaned_count
            )
            
            return cleaned_count
            
        except Exception as e:
            self.logger.error(
                "Failed to cleanup debug files",
                exception_class=e.__class__.__name__,
                exc_info=True
            )
            return 0

# Global debug HTML manager
_debug_html_manager: Optional[DebugHTMLManager] = None

def get_debug_html_manager(run_id: str) -> DebugHTMLManager:
    """Get global debug HTML manager instance"""
    global _debug_html_manager
    if _debug_html_manager is None or _debug_html_manager.run_id != run_id:
        _debug_html_manager = DebugHTMLManager(run_id)
    return _debug_html_manager
