# Literature Review – Arabic Financial NLP (≥2022)

### SaudiBERT: Monodialect Model and Saudi Forums Corpus (2024)

**Source**: <PERSON><PERSON><PERSON> et al. 2024 (arXiv).  This paper introduces **SaudiBERT**, a BERT‑based model pretrained exclusively on Saudi dialectal text and evaluates it against six Arabic dialectal language models on sentiment analysis and text classification tasks.  SaudiBERT achieved state‑of‑the‑art performance (average F1‑scores of 86.15 % for sentiment analysis and 87.86 % for text classification).  A key contribution is the creation of two large pretraining corpora: the **Saudi Tweets Mega Corpus** and the **Saudi Forums Corpus (SFC)**.  The SFC contains 15.2 GB of text extracted from five popular Saudi forums—Btalah, Hawamer, Kooora, Mbt3th and Mekshat—and was built by downloading forum HTML, extracting posts and replies with BeautifulSoup, removing URLs and noise, and deduplicating posts.  The final SFC corpus includes over 70 million sentences and more than 1.5 billion words.  This corpus demonstrates that large‑scale Saudi‑dialect data can be collected from forums such as Hawamer and used to train domain‑specific models.

### AraFinNLP 2024 Shared Task: Banking Intent Detection and Translation

**Source**: <PERSON><PERSON> et al. 2024 (arXiv).  The **AraFinNLP 2024** shared task was the first Arabic Financial NLP competition focusing on banking queries.  It defined two subtasks: (1) **Multi‑dialect intent detection** and (2) **Cross‑dialect translation and intent preservation**.  Participants used the updated **ArBanking77** dataset, which contains thousands of banking queries labelled with 77 intents and covers Modern Standard Arabic plus four dialects.  The task emphasised the importance of dialect awareness and banking‑specific intent classification in Arabic financial NLP and highlighted the shortage of research in this domain.  Although the dataset excluded Saudi dialects in its test set, the shared task demonstrates the growing interest in Arabic FinNLP and provides baselines for multilingual intent detection.

### Sentiments Analysis Prediction of Arabic Stock‑Market News (2025)

**Source**: Alasmari & Alotaibi 2025, Journal of Information Systems Engineering and Management.  This study collected **30 098 Arabic news articles** from the Saudi stock‑market platform **Tadawul** and compared machine‑learning and deep‑learning approaches for sentiment classification.  The authors tested Naive Bayes, logistic regression, FastText and LSTM models; deep‑learning models outperformed machine‑learning methods.  In particular, the LSTM model and a reduced‑feature logistic regression classifier each achieved **84 % classification accuracy**, while Naive Bayes performed worst.  The authors conclude that sentiment analysis of stock‑market news can support investment decision‑making and risk management.  The dataset provides a labelled Arabic news corpus relevant to Saudi equities, which could complement forum‑derived sentiment signals.

### Arabic Stock News Sentiments Using BERT (2024)

**Source**: Alasmari et al. 2024, International Journal of Computer Science & Network Security.  This paper develops a sentiment‑classification approach for Arabic stock news using logistic regression and a **Bidirectional Encoder Representations from Transformers (BERT)** model.  The authors collected and preprocessed news articles from an official Saudi stock‑market platform, then trained BERT to classify articles as positive, negative or neutral.  The supervised BERT model achieved **88 % classification accuracy**, while an unsupervised mean Word2Vec encoder produced **80 % clustering accuracy**.  They also annotated news with the economic reasons underlying sentiment (seven economic aspects), which aids interpretability.  The study demonstrates that transformer‑based models can effectively analyse Arabic financial news and extract sentiment‑driven signals.

### AMWAL: Named Entity Recognition for Arabic Financial News (2025)

**Source**: Abdo et al. 2025, ACL FinNLP Workshop.  **AMWAL** is a named entity recognition (NER) system tailored for Arabic financial news.  The authors compiled a specialised corpus from three major Arabic financial newspapers covering the period 2000–2023 and manually annotated entities in 20 categories.  The resulting dataset contains **17.1 k tokens** of financial entities.  AMWAL adopts financial concepts from the **Financial Industry Business Ontology (FIBO)** to standardise entities and achieve interoperability.  The proposed NER model achieved **precision 96.08**, **recall 95.87** and **F1‑score 95.97**, outperforming general Arabic NER systems and highlighting the value of domain‑specific training.  Although focused on news rather than forums, AMWAL provides a blueprint for extracting structured financial information (e.g., company names, instruments) from Arabic text.

### Relevance to Hawamer Forum Sentiment Analysis

The reviewed literature shows rapid advances in Arabic financial NLP.  SaudiBERT and the Saudi Forums Corpus demonstrate that large‑scale forum data—including Hawamer—can be scraped and used to train powerful models.  AraFinNLP 2024 highlights emerging benchmarks for banking intent detection.  Studies on stock‑market news sentiment use deep learning (LSTM, BERT) to achieve 84–88 % accuracy on Saudi news data.  AMWAL shows that domain‑specific NER can reach F1 ≈ 96 % on financial newspapers, suggesting that similar techniques could extract entities (stocks, sectors) from forum posts.

In the context of Hawamer, these insights imply that a sentiment‑trading system should combine **forum‑derived sentiment** with **news sentiment** and **entity recognition**.  SaudiBERT’s dialectal embeddings can capture colloquial language and emojis common on Hawamer, while AMWAL‑style NER can identify tickers and companies mentioned in posts.  The performance of LSTM and BERT models on Saudi news suggests that fine‑tuning such models on Hawamer data—augmented with labelled sentiment samples—could yield robust sentiment signals for Saudi equities.  Additionally, the legal and ethical considerations highlighted in PDPL must be respected when scraping and processing user‑generated content.
