"""
Token Bucket Rate Limiter for Saudi NLP Trading

Implements observable token bucket algorithm with detailed logging,
Retry-After header compliance, and exponential backoff with jitter.
"""

import time
import threading
from typing import Dict, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

from .logging import get_logger
from ..config.settings import get_config, RIYADH_TZ

@dataclass
class TokenBucketState:
    """Current state of token bucket"""
    tokens: float
    last_refill: float
    capacity: float
    refill_rate: float
    
    def to_dict(self) -> Dict:
        return {
            'tokens': round(self.tokens, 3),
            'last_refill': self.last_refill,
            'capacity': self.capacity,
            'refill_rate': self.refill_rate,
            'utilization': round((self.capacity - self.tokens) / self.capacity, 3)
        }

class TokenBucketRateLimiter:
    """
    Token bucket rate limiter with observable math and detailed logging
    """
    
    def __init__(self, requests_per_minute: int = 30, burst_capacity: Optional[int] = None):
        self.config = get_config()
        self.logger = get_logger(__name__)
        
        # Rate limiting parameters
        self.requests_per_minute = requests_per_minute
        self.refill_rate = requests_per_minute / 60.0  # tokens per second
        self.capacity = burst_capacity or max(requests_per_minute // 4, 5)  # Allow some burst
        
        # Token bucket state
        self.tokens = float(self.capacity)
        self.last_refill = time.time()
        
        # Thread safety
        self.lock = threading.Lock()
        
        # Statistics
        self.stats = {
            'total_requests': 0,
            'allowed_requests': 0,
            'blocked_requests': 0,
            'total_wait_time': 0.0,
            'max_wait_time': 0.0,
            'bucket_empty_count': 0
        }
        
        self.logger.info(
            "Initialized token bucket rate limiter",
            requests_per_minute=requests_per_minute,
            refill_rate=self.refill_rate,
            capacity=self.capacity
        )
    
    def _refill_tokens(self) -> None:
        """Refill tokens based on elapsed time"""
        now = time.time()
        elapsed = now - self.last_refill
        
        if elapsed > 0:
            # Add tokens based on elapsed time
            tokens_to_add = elapsed * self.refill_rate
            self.tokens = min(self.capacity, self.tokens + tokens_to_add)
            self.last_refill = now
            
            if tokens_to_add > 0:
                self.logger.debug(
                    "Refilled token bucket",
                    elapsed_seconds=round(elapsed, 3),
                    tokens_added=round(tokens_to_add, 3),
                    current_tokens=round(self.tokens, 3),
                    capacity=self.capacity
                )
    
    def acquire_token(self, tokens_needed: int = 1, 
                     retry_after: Optional[float] = None) -> Tuple[bool, float]:
        """
        Attempt to acquire tokens from bucket
        
        Args:
            tokens_needed: Number of tokens to acquire
            retry_after: Server-suggested retry delay (from Retry-After header)
        
        Returns:
            (success: bool, wait_time: float)
        """
        
        with self.lock:
            self.stats['total_requests'] += 1
            
            # Refill tokens
            self._refill_tokens()
            
            # Check if we have enough tokens
            if self.tokens >= tokens_needed:
                # Consume tokens
                self.tokens -= tokens_needed
                self.stats['allowed_requests'] += 1
                
                self.logger.debug(
                    "Token acquired",
                    tokens_needed=tokens_needed,
                    remaining_tokens=round(self.tokens, 3),
                    bucket_state=self.get_state().to_dict()
                )
                
                return True, 0.0
            
            else:
                # Not enough tokens - calculate wait time
                self.stats['blocked_requests'] += 1
                
                if self.tokens == 0:
                    self.stats['bucket_empty_count'] += 1
                
                # Use server-suggested retry time if provided
                if retry_after is not None:
                    wait_time = retry_after
                    self.logger.info(
                        "Using server Retry-After header",
                        retry_after=retry_after,
                        bucket_state=self.get_state().to_dict()
                    )
                else:
                    # Calculate time needed to get required tokens
                    tokens_deficit = tokens_needed - self.tokens
                    wait_time = tokens_deficit / self.refill_rate
                
                # Update statistics
                self.stats['total_wait_time'] += wait_time
                self.stats['max_wait_time'] = max(self.stats['max_wait_time'], wait_time)
                
                self.logger.info(
                    "Token acquisition blocked",
                    tokens_needed=tokens_needed,
                    current_tokens=round(self.tokens, 3),
                    wait_time=round(wait_time, 3),
                    bucket_state=self.get_state().to_dict()
                )
                
                return False, wait_time
    
    def wait_for_token(self, tokens_needed: int = 1,
                      retry_after: Optional[float] = None) -> float:
        """
        Wait until tokens are available and acquire them
        
        Args:
            tokens_needed: Number of tokens to acquire
            retry_after: Server-suggested retry delay
        
        Returns:
            Actual wait time in seconds
        """
        
        start_time = time.time()
        
        success, wait_time = self.acquire_token(tokens_needed, retry_after)
        
        if success:
            return 0.0
        
        # Wait for tokens to be available
        self.logger.info(
            "Waiting for token bucket refill",
            wait_time=round(wait_time, 3),
            tokens_needed=tokens_needed
        )
        
        time.sleep(wait_time)
        
        # Try to acquire again after waiting
        with self.lock:
            self._refill_tokens()
            if self.tokens >= tokens_needed:
                self.tokens -= tokens_needed
                self.stats['allowed_requests'] += 1
                
                actual_wait = time.time() - start_time
                
                self.logger.info(
                    "Token acquired after wait",
                    actual_wait_time=round(actual_wait, 3),
                    expected_wait_time=round(wait_time, 3),
                    bucket_state=self.get_state().to_dict()
                )
                
                return actual_wait
            else:
                # Still not enough tokens - this shouldn't happen
                self.logger.warning(
                    "Insufficient tokens after wait",
                    tokens_needed=tokens_needed,
                    current_tokens=round(self.tokens, 3),
                    bucket_state=self.get_state().to_dict()
                )
                return time.time() - start_time
    
    def get_state(self) -> TokenBucketState:
        """Get current bucket state"""
        with self.lock:
            self._refill_tokens()
            return TokenBucketState(
                tokens=self.tokens,
                last_refill=self.last_refill,
                capacity=self.capacity,
                refill_rate=self.refill_rate
            )
    
    def get_stats(self) -> Dict:
        """Get rate limiting statistics"""
        with self.lock:
            total_requests = self.stats['total_requests']
            
            return {
                **self.stats,
                'success_rate': (self.stats['allowed_requests'] / total_requests) if total_requests > 0 else 0,
                'avg_wait_time': (self.stats['total_wait_time'] / self.stats['blocked_requests']) if self.stats['blocked_requests'] > 0 else 0,
                'bucket_state': self.get_state().to_dict(),
                'configuration': {
                    'requests_per_minute': self.requests_per_minute,
                    'refill_rate': self.refill_rate,
                    'capacity': self.capacity
                }
            }
    
    def reset_stats(self) -> None:
        """Reset statistics counters"""
        with self.lock:
            self.stats = {
                'total_requests': 0,
                'allowed_requests': 0,
                'blocked_requests': 0,
                'total_wait_time': 0.0,
                'max_wait_time': 0.0,
                'bucket_empty_count': 0
            }
            
            self.logger.info("Rate limiter statistics reset")

class DomainRateLimiter:
    """
    Per-domain rate limiting with token buckets
    """
    
    def __init__(self):
        self.config = get_config()
        self.logger = get_logger(__name__)
        self.domain_limiters: Dict[str, TokenBucketRateLimiter] = {}
        self.lock = threading.Lock()
    
    def get_limiter(self, domain: str) -> TokenBucketRateLimiter:
        """Get or create rate limiter for domain"""
        with self.lock:
            if domain not in self.domain_limiters:
                self.domain_limiters[domain] = TokenBucketRateLimiter(
                    requests_per_minute=self.config.scraper.max_rpm
                )
                
                self.logger.info(
                    "Created rate limiter for domain",
                    domain=domain,
                    requests_per_minute=self.config.scraper.max_rpm
                )
            
            return self.domain_limiters[domain]
    
    def wait_for_domain(self, domain: str, retry_after: Optional[float] = None) -> float:
        """Wait for rate limit on specific domain"""
        limiter = self.get_limiter(domain)
        return limiter.wait_for_token(retry_after=retry_after)
    
    def get_domain_stats(self) -> Dict[str, Dict]:
        """Get statistics for all domains"""
        with self.lock:
            return {
                domain: limiter.get_stats()
                for domain, limiter in self.domain_limiters.items()
            }
    
    def cleanup_inactive_domains(self, inactive_threshold: float = 3600) -> int:
        """Remove rate limiters for domains inactive for more than threshold seconds"""
        now = time.time()
        removed_count = 0
        
        with self.lock:
            inactive_domains = []
            
            for domain, limiter in self.domain_limiters.items():
                if now - limiter.last_refill > inactive_threshold:
                    inactive_domains.append(domain)
            
            for domain in inactive_domains:
                del self.domain_limiters[domain]
                removed_count += 1
            
            if removed_count > 0:
                self.logger.info(
                    "Cleaned up inactive domain rate limiters",
                    removed_count=removed_count,
                    remaining_domains=len(self.domain_limiters)
                )
        
        return removed_count

# Global domain rate limiter
_domain_rate_limiter: Optional[DomainRateLimiter] = None

def get_domain_rate_limiter() -> DomainRateLimiter:
    """Get global domain rate limiter instance"""
    global _domain_rate_limiter
    if _domain_rate_limiter is None:
        _domain_rate_limiter = DomainRateLimiter()
    return _domain_rate_limiter
