import asyncio
import random
from datetime import datetime
import argparse
from pathlib import Path
from typing import List, Dict

from loguru import logger

from ..scrapers.hawamer_scraper import <PERSON><PERSON><PERSON><PERSON><PERSON>raper
from ..preprocessors.arabic_normalizer import ArabicTextProcessor
from ..embeddings.swan_embedder import SwanEmbedder
from ..analytics.topic_modeler import ArabicTopicModeler
from ..analytics.sentiment_analyzer import SentimentAnalyzer


class TradingPipeline:
    """
    Main orchestrator for the entire NLP trading system.
    """

    def __init__(self, headful: bool = False):
        # Initialise all components
        self.scraper = HawamerScraper()
        self._headful = headful
        self.preprocessor = ArabicTextProcessor()
        self.embedder = SwanEmbedder()
        self.topic_modeler = ArabicTopicModeler(self.embedder)
        self.sentiment_analyzer = SentimentAnalyzer()

        # Set up logging to file; logs directory will be created by setup.py
        logger.add(
            "logs/pipeline_{time}.log",
            rotation="1 day",
            retention="7 days"
        )

    async def run_pipeline(self, target_urls: List[str]):
        """
        Execute full pipeline from scraping to analysis.
        """
        logger.info(f"Starting pipeline run at {datetime.now()}")

        # Phase 1: Scraping
        logger.info("Phase 1: Web scraping")
        await self.scraper.initialize_browser(headless=not self._headful)

        scraped_data: List[Dict] = []
        for url in target_urls:
            try:
                thread_data = await self.scraper.scrape_thread(url)
                scraped_data.append(thread_data)

                # Respect rate limits with random delay
                await asyncio.sleep(random.uniform(3, 7))

            except Exception as e:
                logger.error(f"Failed to scrape {url}: {e}")

        # Phase 2: Preprocessing
        logger.info("Phase 2: Text preprocessing")
        processed_posts: List[Dict] = []
        for thread in scraped_data:
            for post in thread.get('posts', []):
                processed = self.preprocessor.preprocess(post['content'])
                processed['metadata'] = {
                    'thread_url': thread['url'],
                    'timestamp': post['timestamp'],
                    'author_hash': post['author']
                }
                processed_posts.append(processed)

        # Phase 3: Embedding Generation
        logger.info("Phase 3: Generating embeddings")
        texts = [p.get('clean_text', '') for p in processed_posts if isinstance(p.get('clean_text'), str) and p.get('clean_text').strip()]
        embeddings = None
        topic_results = None
        if texts:
            embeddings = self.embedder.embed_texts(texts)

            # Phase 4: Topic Modelling
            logger.info("Phase 4: Topic modelling")
            topic_results = self.topic_modeler.fit_transform(texts)
        else:
            logger.warning("No texts available after preprocessing; skipping embeddings and topic modelling.")
            topic_results = {
                'topics': [],
                'probabilities': [],
                'topic_info': {},
                'topic_drift': 0.0,
                'embeddings': []
            }
            embeddings = []

        # Phase 5: Feature Engineering
        logger.info("Phase 5: Feature engineering")
        features = self._engineer_features(processed_posts, embeddings, topic_results)

        # Save or return results as needed
        self._save_results({
            'scraped_data': scraped_data,
            'processed_posts': processed_posts,
            'topic_results': topic_results,
            'features': features,
            'pipeline_metadata': {
                'run_timestamp': datetime.now().isoformat(),
                'total_posts': len(processed_posts),
                'unique_tickers': self._extract_unique_tickers(processed_posts)
            }
        })

        logger.info("Pipeline completed successfully")

    def _engineer_features(self, posts: List[Dict], embeddings, topic_results) -> Dict:
        """
        Create trading‑relevant features.
        """
        # Compute sentiment volatility placeholder
        sentiment_scores = self.sentiment_analyzer.predict(embeddings)
        sentiment_volatility = max(sentiment_scores) - min(sentiment_scores) if sentiment_scores else 0

        features = {
            'topic_shock_flag': 1 if topic_results['topic_drift'] > 0.3 else 0,
            'sentiment_volatility': sentiment_volatility,
            'ticker_mentions': self._aggregate_ticker_mentions(posts),
            'volume_spike': self._detect_volume_spike(posts)
        }

        return features

    def _aggregate_ticker_mentions(self, posts: List[Dict]) -> Dict[str, int]:
        """
        Aggregate ticker mentions across posts.
        """
        counts: Dict[str, int] = {}
        for p in posts:
            for ticker in p['tickers']:
                counts[ticker] = counts.get(ticker, 0) + 1
        return counts

    def _detect_volume_spike(self, posts: List[Dict]) -> bool:
        """
        Detect a spike in posting volume relative to a baseline.

        This simplistic implementation flags a spike if there are more than
        100 posts in the batch.  In production, compare against a moving
        average or exponential moving average.
        """
        return len(posts) > 100

    def _extract_unique_tickers(self, posts: List[Dict]) -> List[str]:
        """
        Extract unique tickers from processed posts.
        """
        unique = set()
        for p in posts:
            unique.update(p['tickers'])
        return sorted(unique)

    def _save_results(self, results: Dict) -> None:
        """
        Persist results to disk or a database.  This default implementation
        writes a JSON file in the data directory.  Override to customise.
        """
        import json
        from pathlib import Path
        import numpy as np

        def _default(o):
            try:
                if isinstance(o, np.ndarray):
                    return o.tolist()
            except Exception:
                pass
            try:
                # Common types that aren't JSON-serializable by default
                import pandas as pd  # type: ignore
                if isinstance(o, (pd.Series, pd.DataFrame)):
                    return o.to_dict(orient='records') if isinstance(o, pd.DataFrame) else o.to_list()
            except Exception:
                pass
            return str(o)

        path = Path('data') / 'processed' / f"results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        path.parent.mkdir(parents=True, exist_ok=True)
        with open(path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=_default)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Saudi NLP Trading Pipeline Orchestrator")
    parser.add_argument("--urls", type=str, required=False, default=None,
                        help="Path to a text file containing Hawamer thread URLs (one per line)")
    parser.add_argument("--mode", type=str, choices=["test", "production"], default="test",
                        help="Run mode")
    parser.add_argument("--headful", action="store_true", help="Run browser in headful mode for visual debugging")
    args = parser.parse_args()

    async def _main():
        pipeline = TradingPipeline(headful=args.headful)
        target_urls: List[str] = []
        if args.urls:
            url_file = Path(args.urls)
            if url_file.exists():
                with open(url_file, "r", encoding="utf-8") as f:
                    target_urls = [ln.strip() for ln in f if ln.strip()]
        if not target_urls:
            logger.warning("No URLs provided; nothing to scrape. Exiting.")
            return
        await pipeline.run_pipeline(target_urls)

    asyncio.run(_main())
