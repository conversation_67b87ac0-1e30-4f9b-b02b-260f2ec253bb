# Saudi NLP Trading System Implementation Log

## Project Overview
System designed to analyse Arabic financial discourse from Hawamer.com forums and correlate with Tadawul (Saudi stock market) volatility patterns.

## Architecture Decisions

### 1. Scraping Strategy
* Chosen: Hybrid approach (H2 + H3)
* Playwright for dynamic content + API endpoint discovery
* Fallback to direct API calls when possible
* Cloudflare handling via stealth browser techniques

### 2. NLP Stack
* Primary: Swan embeddings for dialect‑aware vectorisation
* Preprocessing: CAMeL Tools for Arabic normalisation
* Topic Modelling: BERTopic with Swan embeddings
* Sentiment: Hybrid Swan + FinBERT approach

### 3. Data Pipeline
* Async/await pattern for concurrent scraping
* PostgreSQL for structured data
* S3 for raw HTML backup
* Redis for caching and rate limiting

## Implementation Steps Completed

1. ✅ Repository structure created  
2. ✅ Core scraper with anti‑detection  
3. ✅ Arabic preprocessing pipeline  
4. ✅ Swan embedding integration  
5. ✅ Topic modelling framework  
6. ✅ Test suite foundation  
7. ⏳ Sentiment analysis module (in progress)  
8. ⏳ Graph analytics for influence detection  
9. ⏳ GARCH volatility coupling  
10. ⏳ Real‑time monitoring dashboard  

## Critical Findings

### Cloudflare Challenges
* Standard requests blocked immediately
* Playwright with stealth scripts: 70 % success rate
* Residential proxy rotation essential
* 5–10 second delays between requests recommended

### Arabic Text Challenges
* Dialect variations significant in forum text
* Ticker extraction requires careful regex + validation
* Elongation removal critical for embedding quality
* Mixed Arabic‑English common in financial discussions

## Next Steps for Continuation

1. Complete sentiment analyser with FinBERT integration  
2. Implement graph‑based influence metrics  
3. Build GARCH model for volatility prediction  
4. Create real‑time monitoring dashboard  
5. Deploy distributed scraping infrastructure  
6. Fine‑tune Swan on Hawamer‑specific vocabulary  

## Performance Metrics (Initial Tests)

* Scraping success rate: 70 % (with retries: 95 %)  
* Arabic preprocessing speed: 1000 posts/minute  
* Embedding generation: 500 posts/minute  
* Topic modelling: 100 posts/second  
* End‑to‑end pipeline: ~10 minutes for 1000 posts  

## Resource Requirements

* RAM: 16 GB minimum (32 GB recommended)  
* GPU: CUDA‑capable for embeddings (optional but 10× faster)  
* Storage: 100 GB for data + models  
* Network: Saudi IP preferred for lower detection  

## Configuration Notes

* Set user agent rotation every 50 requests  
* Use headless=False initially for debugging  
* Cache embeddings aggressively (they don't change)  
* Monitor API rate limits closely  

## Contact for Questions

This system is designed for educational/research purposes.  Ensure compliance with all applicable laws and terms of service.
