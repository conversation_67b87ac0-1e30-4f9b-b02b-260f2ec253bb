"""
Base Scraper for Saudi NLP Trading

Foundation scraper with robots.txt respect, retry logic, rate limiting,
and structured logging. Designed for production-scale scraping.
"""

import time
import uuid
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Tuple
from urllib.parse import urlparse, urljoin
from datetime import datetime
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from ..config.settings import get_config, RIYADH_TZ
from ..utils.logging import get_logger, get_performance_tracker
from ..utils.robots import get_robots_checker, get_crawl_delay_manager
from ..utils.retry import get_retry_manager, RetryableError, NonRetryableError
from ..core.mode_manager import get_mode_manager

class ScrapingError(Exception):
    """Base exception for scraping errors"""
    pass

class FetchError(ScrapingError):
    """Error during HTTP fetch"""
    
    def __init__(self, message: str, status_code: Optional[int] = None, url: Optional[str] = None):
        super().__init__(message)
        self.status_code = status_code
        self.url = url

class ParseError(ScrapingError):
    """Error during content parsing"""
    pass

class RateLimitError(RetryableError):
    """Rate limit exceeded"""
    pass

class RobotBlockedError(NonRetryableError):
    """Blocked by robots.txt"""
    pass

class BaseScraper(ABC):
    """
    Base scraper with all safety nets and production features
    """
    
    def __init__(self, source_name: str):
        self.source_name = source_name
        self.config = get_config()
        self.logger = get_logger(f"{__name__}.{source_name}")
        self.performance_tracker = get_performance_tracker(f"{__name__}.{source_name}")
        self.mode_manager = get_mode_manager()
        self.robots_checker = get_robots_checker()
        self.crawl_delay_manager = get_crawl_delay_manager()
        self.retry_manager = get_retry_manager()
        
        # Initialize session with proper configuration
        self.session = self._create_session()
        
        # Metrics tracking
        self.metrics = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'robots_blocked': 0,
            'rate_limited': 0,
            'total_bytes_downloaded': 0,
            'total_processing_time': 0.0
        }
        
        self.logger.info(
            f"Initialized {source_name} scraper",
            source=source_name,
            max_rpm=self.config.scraper.max_rpm,
            max_concurrency=self.config.scraper.max_concurrency,
            respect_robots=self.config.scraper.respect_robots
        )
    
    def _create_session(self) -> requests.Session:
        """Create HTTP session with proper configuration"""
        session = requests.Session()
        
        # Set user agent
        session.headers.update({
            'User-Agent': self.config.scraper.user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'ar,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        # Configure retry strategy for the session
        retry_strategy = Retry(
            total=3,
            status_forcelist=[429, 500, 502, 503, 504],
            method_whitelist=["HEAD", "GET", "OPTIONS"],
            backoff_factor=1
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # Configure proxy if enabled
        if self.config.proxy.enabled and self.config.proxy.url:
            session.proxies = {
                'http': self.config.proxy.url,
                'https': self.config.proxy.url
            }
        
        return session
    
    def fetch_url(self, url: str, **kwargs) -> Tuple[str, Dict[str, Any]]:
        """
        Fetch URL with all safety checks and logging
        
        Returns:
            (content: str, metadata: dict)
        """
        
        start_time = time.time()
        attempt = kwargs.get('attempt', 0)
        
        # Update metrics
        self.metrics['total_requests'] += 1
        
        try:
            # Check robots.txt
            can_fetch, robot_policy, crawl_delay = self.robots_checker.can_fetch(url)
            
            if not can_fetch:
                self.metrics['robots_blocked'] += 1
                raise RobotBlockedError(f"URL blocked by robots.txt: {url}")
            
            # Wait for crawl delay
            wait_time = self.crawl_delay_manager.wait_if_needed(url)
            
            # Log request
            self.logger.info(
                "Fetching URL",
                url=url,
                attempt=attempt,
                robot_policy=robot_policy,
                crawl_delay=crawl_delay,
                wait_time=wait_time,
                stage="fetch"
            )
            
            # Make request with timeout
            response = self.session.get(
                url,
                timeout=(10, 30),  # (connect, read) timeout
                **kwargs
            )
            
            # Calculate metrics
            elapsed_ms = (time.time() - start_time) * 1000
            content_length = len(response.content)
            self.metrics['total_bytes_downloaded'] += content_length
            self.metrics['total_processing_time'] += elapsed_ms / 1000
            
            # Check for rate limiting
            if response.status_code == 429:
                self.metrics['rate_limited'] += 1
                retry_after = response.headers.get('Retry-After')
                if retry_after:
                    try:
                        retry_after = float(retry_after)
                    except ValueError:
                        retry_after = None
                
                raise RateLimitError(
                    f"Rate limited: {url}",
                    status_code=429,
                    retry_after=retry_after
                )
            
            # Check for other HTTP errors
            if response.status_code >= 400:
                if response.status_code in [500, 502, 503, 504]:
                    raise RetryableError(
                        f"Server error {response.status_code}: {url}",
                        status_code=response.status_code
                    )
                else:
                    raise FetchError(
                        f"HTTP {response.status_code}: {url}",
                        status_code=response.status_code,
                        url=url
                    )
            
            # Success
            self.metrics['successful_requests'] += 1
            
            # Prepare metadata
            metadata = {
                'url': url,
                'http_status': response.status_code,
                'content_length': content_length,
                'content_type': response.headers.get('Content-Type', ''),
                'elapsed_ms': round(elapsed_ms, 2),
                'attempt': attempt,
                'robot_policy': robot_policy,
                'scraped_at': datetime.now(RIYADH_TZ).isoformat(),
                'headers': dict(response.headers)
            }
            
            # Log success
            self.logger.info(
                "URL fetched successfully",
                url=url,
                http_status=response.status_code,
                bytes=content_length,
                elapsed_ms=round(elapsed_ms, 2),
                stage="fetch",
                event="success"
            )
            
            return response.text, metadata
            
        except (RobotBlockedError, NonRetryableError):
            # Don't retry these
            self.metrics['failed_requests'] += 1
            elapsed_ms = (time.time() - start_time) * 1000
            
            self.logger.error(
                "Non-retryable error fetching URL",
                url=url,
                elapsed_ms=round(elapsed_ms, 2),
                stage="fetch",
                event="non_retryable_error",
                exc_info=True
            )
            raise
            
        except Exception as e:
            # These might be retryable
            self.metrics['failed_requests'] += 1
            elapsed_ms = (time.time() - start_time) * 1000
            
            self.logger.error(
                "Error fetching URL",
                url=url,
                elapsed_ms=round(elapsed_ms, 2),
                exception_class=e.__class__.__name__,
                stage="fetch",
                event="error",
                exc_info=True
            )
            
            # Convert to appropriate exception type
            if isinstance(e, requests.exceptions.Timeout):
                raise RetryableError(f"Timeout fetching {url}") from e
            elif isinstance(e, requests.exceptions.ConnectionError):
                raise RetryableError(f"Connection error fetching {url}") from e
            else:
                raise FetchError(f"Error fetching {url}: {e}") from e
    
    def fetch_with_retry(self, url: str, **kwargs) -> Tuple[str, Dict[str, Any]]:
        """Fetch URL with automatic retry logic"""
        
        def fetch_func():
            return self.fetch_url(url, **kwargs)
        
        return self.retry_manager.execute_with_retry(fetch_func)
    
    @abstractmethod
    def extract_content(self, html: str, url: str) -> Dict[str, Any]:
        """
        Extract structured content from HTML
        
        Must be implemented by subclasses
        
        Returns:
            Dictionary with extracted content
        """
        pass
    
    @abstractmethod
    def get_pagination_urls(self, html: str, base_url: str) -> List[str]:
        """
        Extract pagination URLs from HTML
        
        Must be implemented by subclasses
        
        Returns:
            List of URLs for next pages
        """
        pass
    
    def scrape_url(self, url: str) -> Dict[str, Any]:
        """
        Complete scraping of a single URL
        
        Returns:
            Dictionary with scraped content and metadata
        """
        
        with self.performance_tracker.track_operation("scrape_url", url=url):
            try:
                # Fetch content
                html, fetch_metadata = self.fetch_with_retry(url)
                
                # Extract structured content
                extracted_content = self.extract_content(html, url)
                
                # Combine results
                result = {
                    'url': url,
                    'source': self.source_name,
                    'fetch_metadata': fetch_metadata,
                    'extracted_content': extracted_content,
                    'raw_html': html if self.config.scraper.save_raw_html else None
                }
                
                return result
                
            except Exception as e:
                self.logger.error(
                    "Failed to scrape URL",
                    url=url,
                    exception_class=e.__class__.__name__,
                    stage="scrape",
                    exc_info=True
                )
                raise
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get scraping metrics"""
        
        # Calculate derived metrics
        total_requests = self.metrics['total_requests']
        success_rate = (self.metrics['successful_requests'] / total_requests) if total_requests > 0 else 0
        avg_response_time = (self.metrics['total_processing_time'] / total_requests) if total_requests > 0 else 0
        
        return {
            **self.metrics,
            'success_rate': success_rate,
            'avg_response_time_seconds': avg_response_time,
            'total_mb_downloaded': self.metrics['total_bytes_downloaded'] / 1024 / 1024,
            'performance_metrics': self.performance_tracker.get_metrics_summary()
        }
    
    def close(self):
        """Clean up resources"""
        if hasattr(self, 'session'):
            self.session.close()
        
        self.logger.info(
            f"Closed {self.source_name} scraper",
            final_metrics=self.get_metrics()
        )
