{"timestamp":"2025-08-13T13:03:22.790857+03:00","phase":"INIT","event":"Run structure created","data":{"run_id":"1dcba44c","started_at":"2025-08-13T13:03:22.789857+03:00","seed":1337,"limiter":{"refill_rate_rps":0.5,"burst":5},"targets":{"threads":3,"est_pages":5},"env":{"python":"3.12","os":"windows","agent_version":"1.1"},"schema_version":"1.1"}}
{"timestamp":"2025-08-13T13:03:22.790857+03:00","phase":"RUN_0","event":"STARTING","data":null}
{"timestamp":"2025-08-13T13:03:22.814919+03:00","phase":"RUN_0","event":"COMPLETED","data":null}
{"timestamp":"2025-08-13T13:03:22.814919+03:00","phase":"RUN_1","event":"STARTING","data":null}
{"timestamp":"2025-08-13T13:03:33.879077+03:00","phase":"RUN_1","event":"COMPLETED","data":null}
{"timestamp":"2025-08-13T13:03:33.880078+03:00","phase":"RUN_2","event":"STARTING","data":null}
{"timestamp":"2025-08-13T13:03:33.881078+03:00","phase":"RUN_2","event":"COMPLETED","data":null}
{"timestamp":"2025-08-13T13:03:33.881078+03:00","phase":"RUN_3","event":"STARTING","data":null}
{"timestamp":"2025-08-13T13:03:33.883079+03:00","phase":"RUN_3","event":"COMPLETED","data":null}
{"timestamp":"2025-08-13T13:03:33.884077+03:00","phase":"RUN_4","event":"STARTING","data":null}
{"timestamp":"2025-08-13T13:03:33.908926+03:00","phase":"RUN_4","event":"COMPLETED","data":null}
{"timestamp":"2025-08-13T13:03:33.908926+03:00","phase":"RUN_5","event":"STARTING","data":null}
{"timestamp":"2025-08-13T13:18:34.206519+03:00","phase":"RUN_5","event":"FAILED","data":{"abort_reason":"Error rate too high: 5.8%"}}
