"""CAPTCHA solving utilities using the 2Captcha service.

This module wraps the 2captcha-python client to submit CAPTCHAs and retrieve
solutions.  It reads the API key from the environment via config.settings.
"""

import os
from twocaptcha import TwoCaptcha

from ..config.settings import CAPTCHA_API_KEY


class CaptchaSolver:
    """High‑level interface to the 2Captcha service."""

    def __init__(self):
        api_key = CAPTCHA_API_KEY or os.getenv('CAPTCHA_API_KEY')
        if not api_key:
            raise ValueError("CAPTCHA_API_KEY is not set")
        self.solver = TwoCaptcha(api_key)

    def solve(self, image_path: str) -> str:
        """Submit a CAPTCHA image and return the solution text."""
        result = self.solver.normal(image_path)
        return result['code']
