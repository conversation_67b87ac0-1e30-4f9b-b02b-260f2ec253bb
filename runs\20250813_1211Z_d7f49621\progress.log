{"timestamp":"2025-08-13T12:11:57.046166+03:00","phase":"INIT","event":"Run structure created","data":{"run_id":"d7f49621","started_at":"2025-08-13T12:11:57.045550+03:00","seed":1337,"limiter":{"refill_rate_rps":0.5,"burst":5},"targets":{"threads":3,"est_pages":5},"env":{"python":"3.12","os":"windows","agent_version":"1.1"},"schema_version":"1.1"}}
{"timestamp":"2025-08-13T12:11:57.046166+03:00","phase":"RUN_0","event":"STARTING","data":null}
{"timestamp":"2025-08-13T12:11:57.087997+03:00","phase":"RUN_0","event":"COMPLETED","data":null}
{"timestamp":"2025-08-13T12:11:57.087997+03:00","phase":"RUN_1","event":"STARTING","data":null}
{"timestamp":"2025-08-13T12:12:08.229524+03:00","phase":"RUN_1","event":"COMPLETED","data":null}
{"timestamp":"2025-08-13T12:12:08.229524+03:00","phase":"RUN_2","event":"STARTING","data":null}
{"timestamp":"2025-08-13T12:12:08.230494+03:00","phase":"RUN_2","event":"COMPLETED","data":null}
{"timestamp":"2025-08-13T12:12:08.230494+03:00","phase":"RUN_3","event":"STARTING","data":null}
{"timestamp":"2025-08-13T12:12:08.232487+03:00","phase":"RUN_3","event":"COMPLETED","data":null}
{"timestamp":"2025-08-13T12:12:08.234058+03:00","phase":"RUN_4","event":"STARTING","data":null}
{"timestamp":"2025-08-13T12:12:08.261529+03:00","phase":"RUN_4","event":"COMPLETED","data":null}
{"timestamp":"2025-08-13T12:12:08.261529+03:00","phase":"RUN_5","event":"STARTING","data":null}
{"timestamp":"2025-08-13T12:27:08.535081+03:00","phase":"RUN_5","event":"FAILED","data":{"abort_reason":"Error rate too high: 5.8%"}}
