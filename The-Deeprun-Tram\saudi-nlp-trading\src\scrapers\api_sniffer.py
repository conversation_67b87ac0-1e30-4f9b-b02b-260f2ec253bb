"""
API sniffer utility for reverse‑engineering Hawamer's hidden endpoints.

This module can be used independently of the full scraper to inspect
network traffic from the forum and identify REST or GraphQL endpoints
that return structured JSON.  Use mitmproxy or Playwright event hooks
to capture the necessary requests.
"""

from typing import List, Dict
from loguru import logger


class APISniffer:
    """
    Placeholder class for API sniffing logic.
    """

    def __init__(self):
        self.endpoints: List[Dict] = []

    def record_call(self, url: str, method: str, headers: Dict):
        """
        Store information about an API call.
        """
        entry = {
            "url": url,
            "method": method,
            "headers": headers,
        }
        logger.debug(f"Recorded API call: {entry}")
        self.endpoints.append(entry)

    def save_endpoints(self, path: str) -> None:
        """
        Persist discovered endpoints to a JSON file.
        """
        import json
        with open(path, 'w', encoding='utf-8') as f:
            json.dump(self.endpoints, f, indent=2, ensure_ascii=False)
        logger.info(f"Saved {len(self.endpoints)} endpoints to {path}")
