 PDF To Markdown Converter
Debug View
Result View
Statistical Predictions of Trading Strategies in
Electronic Markets
Forthcoming in Journal of Financial Econometrics
<PERSON><PERSON> ́ 3,4, <PERSON>,3,4, <PERSON>,3, <PERSON><PERSON>,3,4,
<PERSON><PERSON><PERSON>-Betancourt
1,3,4,*
, and <PERSON>
2
1
Alan Turing Institute, British Library, 96 Euston Rd., London NW1 2DB, England.
2
Authority for the Financial Markets, Vijzelgracht 50, 1017 HS Amsterdam, The
Netherlands.
3
Oxford-Man Institute for Quantitative Finance, University of Oxford, Eagle House,
Walton Well Road, Oxford, OX2 6ED, England.
4
Mathematical Institute, University of Oxford, Andrew Wiles Building, Woodstock Rd,
Oxford OX2 6GG, England.
*
Principal author and corresponding author; e-mail: sanchez<PERSON><EMAIL>;
address: Mathematical Institute, University of Oxford, Andrew Wiles Building, Woodstock
Rd, Oxford OX2 6GG, England.
September 20, 2024
Abstract
We build statistical models to describe how market participants choose the direction, price, and vol-
ume of orders. Our dataset, which spans sixteen weeks for four shares traded in Euronext Amsterdam,
contains all messages sent to the exchange and includes algorithm identification and member identifi-
cation. We obtain reliable out-of-sample predictions and report the top features that predict direction,
price, and volume of orders sent to the exchange. The coefficients from the fitted models are used to
cluster trading behaviour and we find that algorithms registered as Liquidity Providers exhibit the widest
range of trading behaviour among dealing capacities. In particular, for the most liquid share in our study,
we identify three types of behaviour that we call (i) directional trading, (ii) opportunistic trading, and
(iii) market making, and we find that around one third of Liquidity Providers behave as market markers.
Keywords: agent-based models, algorithmic trading, limit order book, supervision, statistical prediction.
Disclaimer: The views expressed in this article are those of the authors, and not necessarily those of
the Autoriteit Financi ̈ele Markten. This work was supported by The Alan Turing Institute.
1 Introduction
Modelling economic agents is a fundamental part of understanding financial markets. The increasing use
of electronic trading and the vast quantities of data recorded provide opportunities for modelling agents on
a fine scale. However, privacy concerns and data access restrictions have led to limited academic research
using such data. In this paper we employ a unique dataset to understand trading behaviour. The dataset
contains member identification and algorithm identification and comprises all activity (orders, transactions,
cancellations, and amendments) in Euronext Amsterdam. We model the trading behaviour of each indi-
vidual algorithm that sends limit orders (passive and aggressive) to the market. We obtain an accurate
description of how algorithms choose direction, price, and volume and identify important market-observable
and idiosyncratic features that predict this decision. Our work provides a detailed empirical description of
how algorithms respond to market conditions, their own inventory, and their presence in the limit order
book (LOB). Lastly, we find clusters of trading behaviour and compare them to their dealing capacity in
Euronext Amsterdam. A dealing capacity is a contractual arrangement between the trading venue and the
trading member, specifying the trading behaviour allowed by the member when acting in that capacity. This
classification is determined by the trading venue, not the regulator.
The models we build have a variety of applications, both in academic and regulatory contexts. From
an academic perspective, our findings provide statistical evidence for many microstructural stylised facts
discussed in the literature, and our study is the first to confirm these facts using agent-based models trained
on a dataset that contains both member and algorithm identification. Furthermore, by understanding
the behaviour of algorithms, our models can inform mathematical and algorithmic modelling of market
participants. In an agent-based modelling context, our results provide guidance for calibration of models of
the behaviour of agents at a microstructure level, rather than through their aggregate behaviour in a market.
Our results assess the stability of agents through time, and report the key market and agent variables which
need to be modelled.
From a regulatory perspective, our models provide guidance for surveillance practices. In particular,
our models show the most important features affecting individual trading behaviour. Supervisors could use
this information to improve their anomaly detection, focusing on outliers in features that are especially
important predictors of individual trading behaviour. Also, our work is a starting point to build an agent-
based simulator where market dynamics can be replicated at a granular level to understand how each message
to the order book may affect individual behaviour and therefore affect collective dynamics. Our statistical
framework is devised to capture the fine microstructural facts that drive the trading decisions of individual
algorithms, which is key to understanding how trading algorithms react to new market information and to
messages to the LOB. With such a market simulator, firms will be able to test their trading algorithms
and regulators will be able to study the effect of new trading algorithms on the quality of the market.
Similarly, financial authorities will have a tool to build counterfactual trading tapes to gain insights into
market behaviour in the absence of certain trading algorithms or with potential new entrants in the market.
1.1 Insights
We build models to predict the behaviour of individual trading algorithms with a dataset of beyond-level-
three LOB data that contains the activity of all market participants active in Euronext Amsterdam. For each
ticker and for each algorithm in our study, we propose models to predict the three principal characteristics of
an incoming order: direction (buy or sell), price, and volume. By implication, the price, the time-in-force and
state of the LOB, determine whether orders are aggressive or passive. We build a comprehensive feature space
comprising information available in the market and information available to the algorithm at the moment of
making decisions. We do not account for the latency of market participants when constructing the features;
instead, we employ information from the time “just before” the order reaches the market.
1
Specifically, we
endow each algorithm with 53 features that are updated in continuous time and that describe the state of
the LOB, the algorithm’s past actions, and the member’s past actions. The main contributions of this paper
are:
(i) To obtain high out-of-sample accuracies when one predicts (a) the direction (buy or sell) of limit orders,
(b) the price limit of the limit order (irrespective of its time-in-force), and (c) the volume of the order.
(ii) To show the decrease in accuracy of predictions when we build features that do not use the identity of
the member and do not use the identity of the algorithm.
(iii) To provide a detailed description of the features that are important to predict the direction, price, and
volume of limit orders sent by algorithms. For example: (a) the imbalance of the algorithm’s limit
(^1) We expect that accounting for latency when constructing the features would improve the out-of-sample accuracies we obtain.

See Cartea and S ́anchez-Betancourt (2021) for examples of how one can compute implied latency at a market participant level.
orders resting in the LOB, the best quotes, and the intraday accumulated inventory variables, are the
most important features determining the direction of the order; (b) the volumes at the best bid and
ask prices, the imbalance of the algorithm’s limit orders resting in the LOB, and the bid-ask spread
in the LOB are the most important features determining the price of the order, and (c) the intraday
accumulated inventory and cash variables of both algorithms and members are the most important
features determining the volume of the order.
(iv) To show three clusters of ‘stylised trading behaviour’. We use regression coefficients to build the clusters
and to show how these clusters compare with the registered dealing capacity of market participants.
2
For the most liquid share considered, we find that:
(a) Liquidity Providers have the widest range of trading behaviour in the dataset.
(b) One third of the Liquidity Provider algorithms fall within a cluster that we label “market markers”.
These algorithms tend to maintain a balanced provision of liquidity in the LOB, provide liquidity
inside the spread, revert their inventories to zero, and often provide liquidity at the best quotes
available in the book.^3 The other two thirds of Liquidity Providers are split roughly in half
according to their trading behaviour and fall in clusters that we call “opportunistic traders” and
“directional traders”.
(c) Across clusters, the wider the bid-ask spread the more likely algorithms are to send eager-to-trade
orders. In this paper, an “eager-to-trade” order is either a buy limit order with limit price higher
than the best bid price, or a sell limit order with limit price lower than the best ask price –
essentially (as we see in Subsection 4.5.8), these are orders that either cross the spread or provide
liquidity inside the spread. Similarly, an increase in the number of messages over the last 1 to
100 milliseconds reduces the probability of posting at-the-touch in either direction. In this paper,
an “at-the-touch” order is either a buy limit order with a limit price equal to the best bid price,
or a sell limit order with limit price equal to the best ask price. Lastly, for most horizons used
to compute the quadratic variation of transaction prices, the larger the quadratic variation, the
less likely algorithms will show aggressive behaviour, and the more likely it is that they will post
liquidity inside the spread.
i. Algorithms in the first cluster (directional traders) have the lowest order to trade ratios,
do not maintain a balanced provision of liquidity in the LOB, seem indifferent to their own
imbalance in the book when they decide to send an eager-to-trade order, have a small baseline
probability of posting at-the-touch orders or improving the bid-ask spread, and often send
orders in the direction of their intraday accumulated inventories (i.e., buy orders when they
have a positive intraday accumulated inventory; sell orders when they have a negative intraday
accumulated inventory). More than 80% of their eager-to-trade orders trade aggressively
against the other side of the LOB. This behaviour is consistent with execution or position
building algorithms; the split of aggressive and passive trading shows how algorithms choose
to deploy their execution schedule.
4
ii. Algorithms in the second cluster (opportunistic traders) trade aggressively (73% of their
transactions are liquidity taking, compared to 51% for cluster 1 and 26% for cluster 3),
and similar to algorithms in cluster 1, more than 80% of their eager-to-trade orders trade
aggressively against the other side of the LOB. These algorithms do not mean-revert their
intraday accumulated inventory and their own imbalance in the book is an indicator of their
trading direction. These algorithms have the lowest (across clusters) percentage of eager-
to-trade orders that improve the spread, the lowest baseline probability of posting orders
at-the-touch, and the highest baseline probability of posting limit orders deeper in the LOB.
These algorithms post most of their orders away from the best quotes in an attempt for higher
but less frequent profits.
(^2) The three dealing capacities of market participants in our study are (i) Liquidity Provider, (ii) House, and (iii) Client; we

return to this in Subsection 2.2.
(^3) An agent has a balanced provision of liquidity in the LOB if the volume that the agent has posted on the ask side of the

LOB is similar to the volume that the agent has posted on the bid side of the LOB.
(^4) See Cartea and Jaimungal (2015) for a model of how traders optimally deploy such an execution schedule using aggressive

and passive orders.
iii. Algorithms in the third cluster (market makers) have the highest order to trade ratio across
clusters, are keen to maintain a balanced provision of liquidity in the LOB, have the highest
baseline probability of posting at-the-touch (when compared with the algorithms in the other
two clusters), and show eagerness to revert their intraday accumulated inventory to zero.
Algorithms in this cluster have the highest baseline probability of sending an order that
provides liquidity inside the spread. More precisely, 81% of the eager-to-trade orders sent by
algorithms in this cluster were limit orders that provided liquidity inside the spread. This
behaviour is consistent with that of market makers who are averse to holding inventories (e.g.,
due to asymmetry of information).
5
Using the above we discuss how supervisors can use the results of our study for the benefit of orderly
trading and to have oversight on the integrity of the market.
1.2 Connection with survey data
The recent article AFM (2023) reports the results of a survey conducted with a sample of Dutch proprietary
trading firms using algorithmic trading.
6
The article reports that (i) more than 80% of their trading algo-
rithms use machine learning, (ii) their trading algorithms use features such as quantities in the LOB, price
trends, volatility, and volume imbalances to make short-term predictions, (iii) model parameters or model
weights are updated frequently, (iv) information of the recent past has predictive power in the short future,
(v) the list of possible actions that an algorithm can take after a prediction is limited and hard coded, and
(vi) firms tend to use simple models, such as linear and logistic regressions, to make predictions.
In this paper we find evidence for a number of these items. For example, our analysis demonstrates
that one can predict their trading actions based on features containing quantities in the LOB, price trends,
volatility, and their accumulated intraday inventory. We validate that more recent information has higher
predictive power than older information, and that simple models, such as the one predicting the most used
price range in the past or the most used volume range in the past, have high accuracies. This lends strong
support to the assertion that the actions available to algorithms are hard coded and the number of actions
limited. Lastly, we show that (multinomial) logistic regression models have high out-of-sample accuracies,
which lends support to algorithms using simpler, faster models.
1.3 Existing literature
The LOB plays a crucial role in the microstructure of many modern financial markets. A LOB contains
information available on a specific market and reflects the past trading decisions of its participants. Abergel
et al. (2016) discuss several models for the LOB covering important techniques such as agent-based modelling.
Bouchaud et al. (2002) investigate empirical statistical properties of equity LOBs. Cont et al. (2010) propose
a model for LOB dynamics that recovers aspects of the empirical properties satisfied by LOBs; further work
in this direction is in Hambly et al. (2020) where the authors provide a probabilistic description of LOB
dynamics. Gould et al. (2013) present a detailed account of LOBs, together with statistical analyses of
historical LOB data. They discuss how LOB models provide insights into a number of economic questions
(e.g., questions regarding market efficiency, price formation, or the rationality of traders), but poorly resemble
real LOBs and that several empirical facts have yet to be reproduced satisfactorily.
Agent-based models comprise a number of decision-makers (agents) interacting through prescribed rules.
When applied to the economy as a whole, agents can be as diverse as needed — from consumers to policy-
makers to investment banks. These models do not assume that the economy will move towards a predeter-
mined equilibrium state, as other models do; see e.g., Farmer and Foley (2009). Instead, at any given time,
each agent acts according to its current situation, the state of the environment, and the rules governing its
behaviour.
Farmer et al. (2005) apply agent-based modelling to financial markets. They use data from the London
Stock Exchange to test a simple model in which zero intelligence agents place orders to trade at random. The
model recovers simple stylised facts about the arrival rates of orders; see Lehalle et al. (2011) for an overview
(^5) See the early work Amihud and Mendelson (1980), Ho and Stoll (1981), Kyle (1985), Glosten and Milgrom (1985), and the

more recent work Avellaneda and Stoikov (2008), Gu ́eant et al. (2013), and Cartea et al. (2015), and Gu ́eant (2016).
(^6) Trading firms dealing exclusively on own account.

of the distinction between a zero-intelligence approach to LOB modelling and agent-based models. Byrd et al.
(2019) introduce the design and implementation of ABIDES, a multi-agent equity market simulator. Assefa
et al. (2020) discuss the importance of having reliable synthetic data generators for financial markets. Cohen
et al. (2021) discuss the role of synthetic data in a modern machine-learning finance context. Vyetrenko
et al. (2020) survey the literature and collect statistics and stylised facts seen in real markets that a market
simulator should be able to recreate. They show that these models explain a large part of the variance in
the bid-ask spread and price diffusion rate. In our work, the coefficients of the regression models open the
door to taking agent-based modelling one step further; we enable building a market model with agents whose
actions (direction, price, and volume) are learned from data.
Agent-based modelling can also provide relevant insights for supervisors looking at market manipulation.
Wang et al. (2021) present an agent-based model where agents spoof the LOB to manipulate prices: submit
spurious orders to mislead market participants. They demonstrate that traders who use historical data to
predict prices improve price discovery and social welfare, but their existence in equilibrium renders a market
vulnerable to manipulation. That is, spoofing strategies can effectively mislead traders, distort prices, and
reduce total surplus. Our study provides the foundations to create more realistic agent-based models, because
we show that the trading decisions of agents can be modelled and predicted well. Furthermore, we show
which features are most important in predicting the direction, price and volume of an order; see also Cartea
et al. (2020), Tao et al. (2022), Williams and Skrzypacz (2020).
Ro ̧su (2009) presents a model of an order-driven market in which fully strategic, symmetrically informed
liquidity traders dynamically choose between limit and market orders. The model makes a number of
empirical predictions, such as (i) higher trading activity and higher trading competition cause smaller spreads
and lower price impact, and (ii) market orders lead to a temporary price impact larger than the permanent
price impact. Sirignano and Cont (2019) find evidence of a universal relationship between order flow and the
direction of price moves – they find stable out-of-sample accuracies across shares and periods. Our results
also show stable out-of-sample accuracies when predicting direction, price, and volume of orders sent to the
exchange by market participants. The models we build in this paper can be used to answer questions in
the two aforementioned studies. In particular, we report how the probability of posting liquidity inside the
spread and the probability of crossing the spread change according to key features (e.g., volume imbalances).
We find that the higher the recent activity the more likely algorithms are to submit orders that are eager to
trade.
Bouchaud et al. (2003) uses trades and quotes data from the Paris stock market to show that the random
walk nature of traded prices results from an interplay between two opposite tendencies: long-range correlated
market orders and mean reverting limit orders. They define and study a model where the price is the result
of the impact of all past trades. A ̈ıt-Sahalia et al. (2022) use machine learning to study the predictability
of ultra high-frequency share returns. They find that, contrary to low-frequency and long horizon returns,
predictability of high-frequency returns is large, systematic, and pervasive over short horizons. Our study
employs a number of the features they use, specifically by taking features over various short time horizons. We
show how these features remain predictive, even when one takes into account algorithm-specific information
that is not visible to the market.
Hendershott et al. (2011) focus on the impact of algorithmic trading on market quality, and find that
algorithmic trading narrows spreads, reduces adverse selection, and reduces trade-related price discovery.
Brogaard et al. (2014) find that high-frequency traders help to stabilise prices during transitory shocks.
Menkveld (2016) argues that increased speed in processing information reduces information asymmetry, at
least for informed investors who obtain their information advantage from public news. A ̈ıt-Sahalia and
Saglam (2013) find that if a market maker can predict the direction of future market orders, then the
liquidity provided by the market maker improves. Cartea and Penalva (2012) propose that greater speed
could allow fast traders to profitably intermediate between liquidity demanders and liquidity suppliers; see
also Cohen and Szpruch (2012). This additional intermediation layer would increase execution costs and
microstructure volatility. Furthermore, Cartea et al. (2019) find that an increase in ultra-fast machine-
driven activity is associated with lower intraday market quality (greater quoted and effective spreads and
lower depth). Closest to our work is that by Goldstein et al. (2023), who employ five LOB features to
predict whether a submitted order would be a passive execution, a limit order submission, an amendment,
or a cancellation. They also conduct a number of additional regressions to describe variables such as order
imbalance and probability of fills. In our paper, we build regression models to predict the direction, price,
and volume attached to an incoming order based on market features and private information at the time just
before the order is processed by Euronext. Unlike in Goldstein et al. (2023), we model passive execution and
limit order submission together, and we exclude amendments and cancellations. We show that our models
obtain reliable out-of-sample accuracies and we discuss how our models can be used to build agent-based
market simulators.
Our paper focuses on the quoting behaviour of trading algorithms, so it complements research into high-
frequency trading (HFT).
7
Menkveld (2016) groups traders into high-frequency traders (HFTs) and other
traders. HFTs are further grouped into high-frequency market makers, who trade passively by submitting
bid and ask quotes, and high-frequency bandits, who trade aggressively by taking out stale quotes. Here,
we identify a cluster of algorithms that shows market making behaviour; the median resting time of orders
sent by algorithms in this cluster is the lowest of all clusters. Also relevant to our paper is that by Brogaard
et al. (2019), who document that the majority of price discovery occurs with quote updates as opposed to
trades. Hasbrouck (2018) argues that regular quote changes are likely the result of HFTs undercutting each
other after market orders remove price quotes from the book. Megarbane et al. (2017) study the behaviour
of HFTs and their role in liquidity provision under market stress scenarios such as high-volatility periods
surrounding news announcements. Hoffmann (2014) analyses a model where HFTs quickly cancel their
outstanding limit orders after news. Hagstr ̈omer and Nord ́en (2013) use data from Nasdaq-OMX Stockholm
to distinguish between two types of HFTs: market makers and opportunistic traders. In our paper, we find
evidence that the range of trading behaviour of Liquidity Providers is wide. In particular, we find that one
third of Liquidity Providers behave as market makers and the other two thirds are split into opportunistic
traders and directional traders.
For a review on statistical approaches to modelling high-frequency trading data see Dutta et al. (2022);
they focus on modelling the aggregated publicly available data as opposed to individual agents. There are
strands of the literature that find that deeper layers of the order book contain useful information; see, e.g.,
Libman et al. (2021). In our models, the volume posted deeper in the LOB has predicting power within
certain clusters; this confirms that the results of Libman et al. (2021) are applicable to particular types of
market participants. We also find that around one third of Liquidity Provider algorithms behave as market
makers and improve the spread around 86% of the times they send an eager-to-trade order. In comparison,
directional traders and opportunistic traders improve the spread only 17% and 10% (respectively) of the
times they send an eager-to-trade order.
This paper also builds upon previous research on the clustering of agents. Mankad et al. (2013) develop
a dynamic machine learning method that buckets 15,686 traders in the E-mini S&P 500 futures into five
persistent categories: high-frequency traders (14 traders), market makers (271 traders), opportunistic traders
(7,126), fundamental traders (254), and small traders (8,021). Wright et al. (2022) introduce machine learning
methods that cluster active traders in the market, which can be used for intraday classification. With similar
data to ours, Ruan et al. (2023) classify short-term strategies and identify market marker and directional
trading clusters. Cont et al. (2023) present a granular representation of the LOB that accounts for the origins
of different orders. They describe client order flow from a large broker, in particular, they segment clients into
different clusters, for which they identify representative prototypes. Our paper provides a complementary
analysis; we cluster the trading behaviour of algorithms, and compare these clusters with the dealing capacity
in Euronext Amsterdam.
1.4 Key contributions
This paper constructs models that describe, with high out-of-sample accuracies, how individual agents choose
the (i) direction, (ii) price, and (iii) volume, of limit orders sent to Euronext. We compute the decrease in
prediction accuracy when we build features that do not use member identification and do not use algorithm
identification. For example, without algorithm and member identifications, the out-of-sample accuracy of
predicting direction is 4% higher than random guessing (which is 50%), whereas we obtain 70% order-
weighted accuracy when we build features that use both member and algorithm identification.
We use the coefficients of our models to cluster the behaviour of algorithms. We identify three behavioural
clusters (directional, opportunistic, and market making) and provide a number of insights that, to the best
of our knowledge, have few analogues in the literature. For example, we find that the range of behaviour
(^7) See Menkveld (2016) for a review.

of Liquidity Providers is the widest among dealing capacities – their algorithms are spread over the three
clusters, where around one third are clustered as market making algorithms. Additional contributions are
(i) to provide a comprehensive list of feature importance when predicting direction, price, and volume, (ii)
to discuss the implications of our models for regulators, and (iii) to show how to use our models to build
agent-based market simulators.
2 Data description
We use data from Euronext Amsterdam spanning the period 11 October 2021 to 30 January 2022 (
trading days in 16 weeks) in four shares with tickers ASML, INGA, AD, TOM2 – we refer to these shares
as ASML, ING, AHOLD, and TOMTOM respectively. The data is labelled with algorithm identification
and member identification, and contains transactions, orders, cancellations, and amendments processed by
Euronext Amsterdam over the relevant period. Orders are matched via price-time-priority and are displayed
in a central LOB.^8 The data are timestamped with nanosecond accuracy. The AFM receives the data in
real time from the data systems in Euronext Amsterdam.
2.1 Assets studied
In our study, the companies represent different sectors in the capital markets (technology, finance, and
consumer goods) and their market capitalisation ranges from large to small. The shares differ in the number
of trading venues on which they trade, the number of derivatives written on the share, and the number of
indices in which the share is a constituent. Euronext Amsterdam is the principal market in which the four
shares trade. Specifically,
(i) ASML Holding (ticker ASML) specialises in the development and manufacturing of machines that
produce computer chips, its market capitalisation is approximatelye200B, it is traded on various
trading venues, and it is a constituent in a number of indices.^9 We focus on ASML in the main text of
the paper; results for the other shares are in the Appendix.
(ii) ING Groep N.V. (ticker INGA) is a Dutch multinational banking and financial services corporation
with headquarters in Amsterdam, its market capitalisation is approximatelye50B, it is traded on
various trading venues, and it is a constituent in a number of indices.
10
(iii) Koninklijke Ahold Delhaize N.V. (ticker AD) is a Dutch multinational retail and wholesaling company,
its market capitalisation is approximatelye30B, it is traded on various trading venues, and it is a
constituent in a number of indices.
11
(iv) TomTom (ticker TOM2) is a Dutch multinational developer and creator of location technology and
consumer electronics, its market capitalisation is approximatelye1B, it is traded on fewer trading
venues, and it is a constituent in a few indices.
12
Table 1 presents summary statistics of the four shares between 11 October 2021 and 30 January 2022.
(^8) See Euronext (2023d, p. 43) for information about the main trading session and p. 44–46 for market mechanisms.
(^9) Traded on more than 100 trading venues in 2022 according to AFM MIFID-II transaction data, where 47% of transactions

in the period of our study are on Euronext Amsterdam, 21% on Cboe Europe (DXE order books), etc.; see Euronext (2023b)
for more information on ASML.
(^10) Traded on more than 100 trading venues in 2022 according to AFM MIFID-II transaction data, where 49% of transactions

in the period of our study are on Euronext Amsterdam, 19% on Cboe Europe (DXE order books), etc.; see Euronext (2023c)
for more information on ING.
(^11) Traded on more than 100 trading venues in 2022 according to AFM MIFID-II transaction data, where 44% of transactions

in the period of our study are on Euronext Amsterdam, 23% on Cboe Europe (DXE order books), etc.; see Euronext (2023a)
for more information on AHOLD.
(^12) Traded on more than 50 trading venues in 2022 according to AFM MIFID-II transaction data, where 55% of transactions

in the period of our study are on Euronext Amsterdam, 16% on Cboe Europe (DXE order books), etc.; see Euronext (2023e)
for more information on TOMTOM.
avg daily
tradecount
avg daily traded
volumee
lowest price
periode
highest price
periode
market share
(Euronext) %
ASML 41,847 543,088,447 566.10 770.50 47
ING 21,469 186,226,078 11.58 13.62 49
AHOLD 10,535 78,665,021 27.54 31.35 44
TOMTOM 1,385 4,329,991 6.00 9.23 55
Table 1: Statistics for ASML, ING, AHOLD, and TOMTOM between 11 October 2021 and 30 January
2022.
The tick size of each share traded on Euronext Amsterdam depends on the price range in which the share
trades. These rules are unique for each share. ASML can trade in nineteen possible tick sizes; for example,
tick size ise0.05 if the share trades betweene200 ande500, and it ise0.1 if it trades betweene500 and
e1000.
The tick sizes of the shares do not change between 11 October 2021 and 30 January 2022. During this
period, the tick size of ASML ise0.1, the tick size of ING ise0.002, the tick size of AHOLD ise0.005, and
that of TOMTOM ise0.005. We observe that the tick sizes of ASML, ING, and AHOLD are similar tick
sizes as a percentage of their share price (between 1.3 and 1.8 basis points), while the tick size of TOMTOM
is larger as a percentage of its share price.^13
2.2 Euronext: members and algorithms
In Euronext’s rule book (Euronext (2023d)), a member is any individual, corporation, partnership, associ-
ation, trust, or entity who has been admitted to Euronext Securities Membership or Euronext Derivatives
Membership and whose membership has not been terminated. Only members can trade on Euronext Ams-
terdam. A member can trade on its own account or for clients. The latter includes retail-brokers who process
so-called “retail orders”. A member can trade under one or multiple dealing capacities.
One of the dealing capacities is “retail liquidity provider”. Orders of members acting as retail liquidity
providers can be matched only with orders submitted by “retail” members – see Euronext (2023d, p. 14).
Other dealing capacities (each with its own set of obligations) are (i) Liquidity Provider, (ii) House, and (iii)
Client. This classification is determined by Euronext, not the regulator.
In this study, an “agent” is the concatenation of the membership, the dealing capacity, and the “entity”
that sends the order. We provide a stylised example: suppose company ABC is a member of the trading
venue and trades in two dealing capacities: Liquidity Provider and House.
When company ABC trades as a Liquidity Provider, it employs either: algorithm X, or algorithm Y.
When it trades as a House, it employs algorithm Z. The agents within company ABC are (i) ABC-LP-X,
which refers to algorithm X within the Liquidity Provider dealing capacity of ABC, (ii) ABC-LP-Y, which
refers to algorithm Y within the Liquidity Provider dealing capacity of ABC, and (iii) ABC-H-Z, which refers
to algorithm Z within the House dealing capacity of ABC.
Euronext data shows which entity within the member executes a given order, but not if this entity is
(i) a human trader, or (ii) a trading algorithm. However, by cross-checking with regulatory data we know
that all Liquidity Provider agents, and most of the agents within House and Client, are trading algorithms.
Therefore, we use the terms “agent” and “algorithm” interchangeably.
Figure 1 shows the number of algorithms associated with each of the members trading in ASML, grouped
by dealing capacity (Liquidity Provider, House, and Client). We show algorithms that were active each week
throughout the sixteen weeks of data. We order members according to the number of orders they sent to
Euronext; highest to lowest order count appears from top to bottom.
We label algorithms with the identifier reported by the firm using the algorithm. This is equivalent to
the so-called “execution within firm” field in the MIFID-II reported transactions — field 59 in EU (2016a).
In our dataset, from one month to the next, there are on average 7.5 new algorithms sending orders, and on
average, 7.7 algorithms stop sending orders.
14
Although these numbers might appear high, their effect on
order submission is small. New algorithms account, on average, for 0.31% of all orders sent per month.
(^13) There is an extensive literature on the effect of tick sizes on liquidity; see Verousis et al. (2018) or Penalva and Tapia (2021)

for relevant references.
(^14) For ASML there are 96 algorithms active each week during the 16 weeks of data.

0 1 2 3 4 5 6 7 8 9 10
2
1
1
5
3
8
2
5
3
10
4
5
1
1
1
2
1
4
4
1
4
2
1
1
1
1
1
1
8
7
2
1
1
1
Number of algorithms
Member
Client
House
Liquidity Provider
Figure 1: Number of algorithms and dealing capacities per member on Euronext Amsterdam in ASML. The
members are ordered from top to bottom using order count. We show 96 algorithms active each week for
the 16 weeks of data.
Roughly, the algorithms that send the highest number of orders to Euronext are those with a Liquidity
Provider dealing capacity. Out of the twelve members that have algorithms registered as Liquidity Providers,
ten of these members trade exclusively with the Liquidity Provider dealing capacity.
2.3 Data filters and the orders we study
In this study, we apply a number of filters to the data. First, we focus on messages entered between 09:05:
and 17:25:00. This excludes the first and last five minutes of the main trading session and also omits messages
sent during the pre-opening phase and closing phase.
Second, we ignore any undisclosed volume in the LOB when constructing the features of a given algorithm.
This consists of volumes in the order book that – similar to iceberg orders – are not visible to market
participants. We note, however, that under 0.1% of all orders in any of our shares include undisclosed
volume.
Third, in creating our features, we ignore all orders, deletions, and amendments by agents classified as
“retail liquidity providers” (retail LPs). This specific dealing capacity has the unusual behaviour that their
orders, deletions, and amendments (including the resulting transactions and volumes in the order book) are
visible only to “retail” members. Retail clients are able to trade with both retail LPs and the rest of the
order book. In effect, this creates two LOBs, one for retail members and one for all other agents. In our
dataset, we exclude retail LPs’ orders, and all retail member behaviour which matches against these orders,
because we are not concerned with retail trading behaviour. The remaining orders of retail members are
included in our dataset, for the purposes of determining the information available to the market, but we will
not attempt to fit models for the behaviour of retail members.
2.3.1 Order types
The order types available in Euronext are (i) limit orders, (ii) market orders, (iii) stop orders, (iv) pegged
orders, and (v) mid-point orders. Table 2 shows the number of trades in the first four weeks of data for
ASML for the following combinations: (i) the buy order is a limit order and the sell order is a limit order,
(ii) the buy order is a market order and the sell order is a limit order, (iii) the buy order is a limit order and
the sell order is a market order, and (iv) all other combinations.
order type trade count
buy side sell side number percentage (%)
limit order limit order 752,922 97
market order limit order 6,686 1
limit order market order 4,974 1
all other combinations 10,446 1
Table 2: Order types of transactions in ASML between 11 October 2021 and 7 November 2022.
The algorithms in our dataset mostly use limit orders, instead of market orders, to take liquidity. Approx-
imately 97% of all transactions result from matching two limit orders. Specifically, in 97% of transactions
one leg is a limit order that rests in the LOB and the other leg is an incoming limit order with a limit price
generous enough to match the first leg.
2.3.2 Time-in-force
Orders submitted to Euronext have a validity parameter – also known as the time-in-force of the order. Table
3 shows the trade count grouped by the validity parameters of the buy and sell orders that were matched.
More precisely, we show the number of transactions in the first four weeks of data for ASML for the following
combinations: (i) the buy order has validity parameter immediate-or-cancel (“IoC”) and the sell order has
validity parameter good-for-day (“DAY”), (ii) the buy order has validity parameter DAY and the sell order
has validity parameter IoC, (iii) the buy order has validity parameter DAY and the sell order has validity
parameter DAY, and (iv) all other combinations (for example, fill-or-kill).
validity parameter trade count
buy side sell side number percentage (%)
IoC DAY 283,545 37
DAY IoC 280,615 36
DAY DAY 195,691 25
all other combinations 15,177 2
Table 3: Validity types of transactions in ASML between 11 October 2021 and 7 November 2022.
In the first two rows of Table 3, liquidity is consumed with an IoC order. In the third row, liquidity is
taken with a DAY order. IoC and DAY orders account for 98% of all transactions. Naturally, in Table 3, it
is always the case that one of the two legs has DAY as a time-in-force, which represents the liquidity resting
in the LOB waiting to be matched; the other leg is IoC roughly two-thirds of the time and DAY roughly
one-third of the time.
2.3.3 The orders that we model
In this paper we model limit orders (including all possible validity parameters). As shown in the two tables
above, these orders account for almost all trades in Euronext.
Table 4 shows the order and validity types of all limit orders sent during the first four weeks of data for
ASML. This constitutes a four-week snapshot of the orders that we model in this paper.
order type validity type order count percentage (%)
DAY 7,350,535 97
limit order IoC 252,744 3
other 10,210 0
Table 4: Order and validity types of orders in ASML between 11 October 2021 and 7 November 2022.
Limit orders with validity type DAY are 97% of all limit orders sent, and orders with validity type IoC
account for only 3%. We also observe that IoC orders are often filled by more than one DAY order, which
explains the difference between the figures in Tables 3 and 4.
Table 5 uses the first four weeks of data for ASML and reports the (contribution to) order count, trade
count, and volume traded of groups of algorithms. More precisely, the table summarises the aggregate
contribution to (i) order count, (ii) trade count, and (iii) volume traded of the top five, top ten, top twenty,
top fifty, and all algorithms. The ranking of algorithms is based on the order count of algorithms during the
first four weeks of data. We note that each trade requires the involvement of two participants, leading to a
double-counting of the number of trades in Table 5.
order count order count trade count trade count volumee volume
percentage of all % percentage of all % percentage of all %
Top 5 2,612,307 34 382,040 25 2,332,746,616 19
Top 10 4,055,626 53 478,985 31 2,972,717,749 25
Top 20 5,652,783 74 679,310 44 4,431,976,153 37
Top 50 7,275,195 96 1,047,174 68 8,086,989,709 67
All 7,613,489 100 1,550,056 100 11,998,022,616 100
Table 5: Descriptive statistics for algorithms trading in ASML between 11 October 2021 and 7 November
2022.
The top ten algorithms account for more than 50% of the orders, are involved in more than 30% of the
trades, and are involved in more than 20% of the total traded volume. While algorithms below the top 50
provide only 4% of the orders, they are involved in 32% of all trades (and 33% of all traded volume by value).
In the Appendix we report the above statistics for ING, AHOLD, and TOMTOM in Tables A1, A2, and A
respectively.
3 Modelling approach
3.1 Features
In this section, we describe the variables we use in the study. Brogaard et al. (2014) find evidence that public
information influences the direction of the orders submitted by HFTs, for example, news announcements,
price movements, and LOB imbalances. Cont et al. (2023) suggest one should also consider the time of
day and other market conditions such as momentum and volatility. A ̈ıt-Sahalia et al. (2022) use three
clocks to construct predictor variables over non-overlapping lookback windows. AFM (2023) indicates that
proprietary trading firms report that their trading algorithms use features such as order book imbalance,
volume in the order book, and price trends, all measured over various time horizons. Furthermore, firms
report that the most recent data has the most predictive power – e.g., messages in the last few milliseconds
have more predictive power than older messages.
In this paper, we use 53 features that reflect (i) the state of the LOB, (ii) recent activity in the LOB, (iii)
the current presence of the algorithm in the LOB, (iv) the current presence of the member in the LOB, (v) the
cash and inventory of the algorithm, and (vi) the cash and inventory of the member. By inventory we mean
a transformationf(x) = sign(x) log(1 +|x|), where|·|is the absolute value, of the intraday accumulated
position measured in number of shares, and the starting value of the accumulated position at the beginning
of each trading day is zero by definition. Similarly, cash is the accumulated expense (in EUR) of purchases
and sales of inventory; we work with expenses (so purchasing inventory increases the cash variable), to ensure
that inventory and cash typically have the same sign. We also apply the above transformationfto the cash
variable. Naturally, there is a strong positive relationship between these two variables. Given our data, we
only consider transactions which occur on Euronext Amsterdam. All features are measured at the time just
before the order enters the market.
To describe our features more precisely, letA={a 1 ,a 2 ,..., aM}be the identifiers for the algorithms
on Euronext, andB={b 1 ,b 2 ,..., bN}be the identifiers for the trading members of Euronext – we have
thatN≤M, i.e., a trading member can trade with multiple trading algorithms. The features we use for
algorithmai∈ Afrom trading memberbi∈ Bare: (i) volumes in the LOB excluding those from trading
algorithmai, (ii) volumes in the LOB posted by trading algorithmai, (iii) imbalance in the LOB excluding
volumes posted by algorithmai, (iv) imbalance in the LOB of volume posted by algorithmai, (v) logarithm
of the volume at the best bid and the best ask, (vi) bid-ask spread, (vii) returns of transaction prices over
various time windows in the past, (viii) volatility of transaction prices over various time windows in the past,
(ix) number of messages over various time windows in the past, (x) number of aggressive buy orders, number
of aggressive sell orders, and net aggressive order flow over various time windows in the past,
15
(xi) logarithm
of volume of last transaction, (xii) inventory of trading algorithmai, (xiii) inventory of trading memberbj,
(xiv) cash of trading algorithmai, and (xv) cash of trading memberbj. In Appendix B we provide formulae
for each of the above features. The categories (i) to (xv) include between one and six variables each and
account for a total of 53 features.
The features we employ are measurable to regulators and trading venues. Even if variables such as
inventory of trading member (i.e., intraday accumulated inventory of trading member) are not used in the
decision making process of an algorithm, it is nonetheless a variable, visible to the regulator or trading
venue, that can be used to predict an algorithm’s trading behaviour. Effectively, our variables can be seen
as proxies for some of the private signals used by trading firms.
There is correlation between many of the above features; for example, it is well-known that there is
positive correlation between the bid-ask spread and any of the measures of volatility we consider; see e.g.,
Grossman and Miller (1988), Glosten and Milgrom (1985), O’Hara (1998). For each share in our study, we
use the first four weeks of data to fit a PCA transformation and project the 53 features down to 30 features,
see Figure 2. This captures more than 90% of the variation in the standardised features for all shares.^16
For each share, we apply the transformation obtained for the first four weeks to the remaining twelve weeks.
We refer to the features after the PCA transformation as the PCA-transformed features and we refer to the
features before the PCA transformation as the original features. Unless stated otherwise, we work with the
PCA-transformed features.
(^15) We use the terms “aggressive” and “liquidity taking” interchangeably.
(^16) The exact percentages of the variation captured with 30 features is: 93% for ASML, 92% for ING, 93% for AHOLD, and

93% for TOMTOM.
•
•
•










• •
• •










• •
• •
• •










...
norders (^53) ... norders

53 30 30
PCA-transformed
features
Original features PCA transformation
· =
· =
Figure 2: Transforming the original features to PCA-transformed features.
3.2 Output variable
Consider an algorithm which sends a limit order at timet. We model the choice of (a)direction, (b)limit
price, and (c)volumeof the limit order sent to the market. Recall that Table 2 shows that around 97% of
all trading activity employs limit orders. Our models accommodate both provision of liquidity and taking
of liquidity. We return to this point in Subsection 4.5.8 when we consider a conditional model for liquidity
taking activity.
We consider all limit orders regardless of their time-in-force. To formalise the problem, let (Dt,Pt,Vt;ai,bj)
be a limit order sent by algorithmai∈ Aand trading memberbj∈ Barriving at the exchange at timet,
whereDt∈{− 1 , 1 }is the direction of the order (Dt= 1 if the order is to buy andDt=−1 if the order is to
sell),Pt∈Ris the price limit attached to the order andVt∈R
+
its volume.
17
The signed difference from
best quote is given by
Pt=
(
Pt−S
b
t ifDt= 1,
Sta−Pt ifDt=− 1 ,
(1)
whereS
a
t andS
b
tare the best ask price and best bid price in Euronext, respectively, and
Vt= log(1 +Vt), (2)
is the log-volume of the order. For an order to buy (Dt= 1) we have thatPt=Pt−S
b
t, which is: (i) greater
than zero if the order has a limit price that is more generous (higher than) than the best bid price, is (ii)
equal to zero if the order has a limit price that is as generous as (equal to) the best bid price, and (iii) less
than zero if the order has a limit price that is less generous (lower than) than the best bid price. Similarly,
for an order to sell (Dt=−1) we have thatPt=Sat−Pt, which is: (i) greater than zero if the order has a
limit price that is more generous (lower than) than the best ask price, is (ii) equal to zero if the order has
a limit price that is as generous as (equal to) the best ask price, and (iii) less than zero if the order has a
limit price that is less generous (higher than) than the best ask price.
We study three regimes of the variablePtand use them to define the price bucket variablePtas follows:
Pt=



1 ifPt∈(0,∞) (eager-to-trade orders – more generous than current best limit price),
2 ifPt∈{ 0 } (at-the-touch orders – at the current best limit price),
3 ifPt∈(−∞,0) (orders deeper in the LOB).
(3)
For ASML,Pt= 1 for 9.6% of orders,Pt= 2 for 42.8% of orders, andPt= 3 for 47.6% of orders.; see Table
13.
With four weeks of data from 11 October 2021 to 5 November 2021, we compute the (up to nine) unique
population deciles of the variableVt, and use these population deciles to define the (up to ten) buckets
associated with this variable. We denote byvcthe number of buckets for the log-volume. The bucket version
ofVtis denoted byVt∈ { 1 , ... , vc}. We apply this bucketing to each of the twelve subsequent weeks of
data. The variables we model are
(Dt,Pt,Vt), (4)
(^17) Accounting for latency is out of the scope of this analysis and is left for future research. However, we mitigate some of the

effects of latency in the features that agents observe, by measuring features over various time-intervals.
and we call them (i) direction of the order, (ii) price bucket, and (iii) volume bucket, respectively.
As discussed above, our models capture both aggressive and passive behaviour depending on the price
bucket and the time-in-force of the order. For example, note that all aggressive behaviour falls withinPt= 1.
More precisely,Pt= 1 includes orders that (i) cross the spread and trade against the opposite side of the
LOB (aggressive orders), (ii) post liquidity inside the spread (generous liquidity provision), and (iii) are
cancelled by the exchange upon entry (missed attempts) because their price limit is not generous enough
to trade and their time-in-force precludes them from resting in the LOB; we return to this point below in
Subsection 4.5.8 where we study a conditional model for liquidity taking activity.
3.3 Regression models
For each sharecwe employW= 16 datasets{Dkc}Wk=1, each corresponding to a given week of orders in the
period 11 October 2021 to 30 January 2022. For each weekk, the dataset is given byDck={(xkt,ykt)}
Tk
t=T 1 ,
where,xkt∈R
K ̃
are the features, andykt∈{− 1 , 1 }×{ 1 , 2 , 3 }×{ 1 ,...,vc}are the order details, given byytk=
(D
k
t,P
k
t,V
k
t). The vectorx
k
t∈R
K ̃
contains the features we use for the regressionsx
k
t= (x
k, 1
1 ,...,x
k,K ̃
1 ).
Here,K ̃= 30 when we use PCA-transformed features. Note that the featuresx
k
t implicitly depend on the
algorithm placing the order. We use logistic regressions to model the decisions of agents, that is
(R1) P(D
k
t= 1) =
1
1 + exp
βD,k+βD,k·xkt
, onDck, (5)
(R⋆) P(Y
k
t =y) =
exp
β
Y,k
y +β
Y,k
y ·x
k
t

1 +
Pyc− 1
j=1 exp

β
Y,k
j +β
Y,k
j ·x
k
t
, onDkc, (6)
whereY ∈ {P,V}and the operator·denotes the dot product between two vectors. WhenY isPthen
R⋆= R2,y∈{ 1 , 2 , 3 }, andy
c
= 3. Similarly, whenYisVthen R⋆= R3,y∈{ 1 ,...,v
c
}, andy
c
=v
c
.
We model (D
k
t,P
k
t,V
k
t) with the vector of featuresx
k
t. We also model price bucket and volume bucket
conditional on the directionD
k
t; for this, we perform logistic regressions
(R∗) P(Y
k
t =y|D
k
t=d) =
exp
βyY,k,d+βyY,k,d·xkt

1 +
Pyc− 1
j=1 exp

β
Y,k,d
j +β
Y,k,d
j ·x
k
t
, onD
c,d
k , (7)
where we have the following four combinations: (i) whenY isPandd= +1 then R∗= R4,yc= 3,
D
c,d
k =D
c,+
k , andy∈ {^1 ,^2 ,^3 }; (ii) whenY isPandd=−1 then R∗= R5,y
c= 3,Dc,d
k =D
c,− 1
k , and
y∈ { 1 , 2 , 3 }; (iii) whenYisVandd= +1 then R∗= R6,y
c
=v
c
,D
c,d
k =D
c,+
k , andy∈ {^1 ,...,v
c
}; (iv)
whenYisVandd=−1 then R∗= R7,y
c
=v
c
,D
c,d
k =D
c,− 1
k , andy∈ {^1 ,...,v
c
}. We also have that
β
P,k,+
3 ,β
P,k,+
3 ,β
P,k,− 1
3 ,β
P,k,− 1
3 ,β
V,k,+
vc ,β
V,k,+
vc ,β
V,k,− 1
vc ,β
V,k,− 1
vc are all zero. Here we use
D
c,+
k ={(x
k
t,y
k
t)∈D
c
k:Dt= 1}, (8)
D
c,− 1
k ={(x
k
t,y
k
t)∈D
c
k:Dt=−^1 }. (9)
For each algorithm we fit the above models. Section 4.2 reports the out-of-sample accuracies and out-
performances over alternative methods for ASML. The results for the other three tickers are reported in
Appendix C.1.
3.4 Machine learning models
3.4.1 Random Forests
We also use random forests – introduced by Breiman (2001) – as a benchmark to assess the performance of
the logistic regressions. Random forests go beyond linear models but lack explainability. We return to this
point below when we discuss data privacy concerns.
We train random forests with fifty trees in the forest and a maximum depth of five for a given tree. To
avoid overfitting and to obtain these parameter values, we tune hyperparameters to balance the in-sample
and out-of-sample accuracies. We employsklearn.ensemblein Python, and the same features as in our
logistic regressions.
3.4.2 Algorithm clusters
The regression coefficients from our logistic regressions describe trading behaviour. Indeed, the value of the
regression coefficients model the probability with which algorithms choose direction, price, and volume. In
this paper, we use the regression coefficients of the models for direction (Dt) to cluster trading behaviour.
The clustering exercise employs hierarchical agglomerative clustering; see Section 21.2 in Murphy (2022).
In particular, we use complete linkage, cosine affinity, and a target of three clusters, to compare with the
dealing capacities of algorithms – recall that we study three dealing capacities in this paper: (i) Liquidity
Provider, (ii) House, and (iii) Client. We employAgglomerativeClusteringfromsklearn.cluster.
3.5 Limitations
The models we discuss below have some limitations. First, our data covers trading in Euronext; thus, the
models that follow do not account for trading in other venues, so we do not have information about the
inventory of the members and their activity in other markets (e.g., stock market and derivatives market).^18
Also, the features we employ are ticker-specific. For instance, when modelling ASML, we do not use data
from any of the other tickers in our Euronext dataset.
In addition to incomplete information, we do not study cancellation or modification of limit orders
submitted to Euronext. Modifications are, according to Euronext, cancellations followed by a new order
submission that keeps the same order ID as the original order. In total we discard∼ 1 .1 million modifications
of orders.
19
Lastly, there are a number of features mentioned in the literature that we do not include. For example,
the queue size at best quotes,
20
proxies for the latency of algorithms, and the latency of the Euronext’s
matching engine.
21
In principle, if we had the trading activity of members in other venues, the accuracies that we report
should improve. However, we remark that with the non-exhaustive feature list we employ, the models obtain
reliable out-of-sample accuracies when predicting the direction, the price, and the volume of a new order.
We think of these accuracies as a lower bound of what could be achieved in future research.
4 Model fit
4.1 Train and deploy structure
As described in Section 2, we use sixteen weeks of data in the study. The models are trained over four
consecutive weeks of data and applied to the week after.
22
For example, we use weeks 1 to 4 (11 October
2021 to 5 November 2021) to train the models, then obtain out-of-sample predictions for week 5 (8 November
2021 to 12 November 2021), train models from weeks 2 to 5 (18 October 2021 to 12 November 2021), then
obtain out-of-sample predictions for week 6 (15 November 2021 to 19 November 2021), and so on. We refer
to each such run as a ‘train-and-deploy’ exercise – see Figure 3.
(^18) For similar issues see Hansch et al. (1998) who examine specialist inventory dynamics in shares where inventory is computed

on the primary exchange only.
(^19) The article of Bouchaud et al. (2002) provides statistics on cancellations. For example, they state that roughly 10% of the

orders are cancelled or modified before being executed. For ASML, 1.1 million orders is roughly 12.6% of the grand total of 8.
(1.1 plus 7.6) million orders. Future avenues of research include the modelling of cancellations or modifications, together with
the resting time of limit orders.
(^20) For ASML, there are on average 4.19 orders on the best bid price level, and on average 4.25 orders on the best ask price

level. This average is computed by sampling the number of orders in the queue at best quotes every minute for the complete
time frame of the dataset we employ.
(^21) See the work of Yao and Ye (2018) where the authors show the extent to which the queue is important for market makers,

and the work of Aquilina et al. (2022); Cartea and S ́anchez-Betancourt (2021) for the latency aspect; for example, Cartea and
S ́anchez-Betancourt (2021) shows how to use marketable limit orders sent by traders to compute the implied latency of their
trading activity.
(^22) The accuracies that we obtain are robust to increasing the training periods in this modelling choice.

1 2 3 4 5 6 ... 16
Train
Deploy
Train
Deploy
weeks
Figure 3: The models are trained on four consecutive weeks of data and applied to the week after.
With the sixteen weeks of data we perform twelve train-and-deploy exercises. Thus, there are twelve
weeks of data for which we perform out-of-sample predictions. For each train-and-deploy exercise we obtain
seven accuracies per algorithm (one for each model in R1 to R7; see Section 3.3). Next, we compute the
order-weighted accuracy of the predictions per algorithm over the twelve weeks of data and report the results
in Table 6. With these accuracies, we compute (i) the average, plus and minus the standard deviation, of
the accuracies across groups of algorithms and we report it in the first five rows, and (ii) the order-weighted
accuracy for all algorithms and report it in the bottom row.
4.2 Out-of-sample accuracy of predictions
Table 6 reports out-of-sample accuracies of the predictions made by the logistic regression models for twelve
train-and-deploy exercises on ASML. The direction of orderDtcan take two values, the price bucketPtcan
take three values, and the volume bucketVtcan take nine values.
The ordering of algorithms in Table 6 is based on the order count over all deploy-weeks of the train-and-
deploy exercises. For example, the “Top 5” row refers to the top five algorithms that sent most orders during
the twelve deploy-weeks. The same ordering method is applied to Table 7 and the tables for other tickers.
R1 R2 R3 R4 R5 R6 R
Dt Pt Vt Pt|Dt= 1 Pt|Dt=− 1 Vt|Dt= 1 Vt|Dt=− 1
Top 5 65 ± 7 80 ± 9 60 ± 23 85 ± 6 84 ± 6 61 ± 21 60 ± 22
Top 10 66 ± 7 86 ± 9 68 ± 22 89 ± 6 89 ± 6 69 ± 21 69 ± 22
Top 20 68 ± 9 87 ± 12 62 ± 23 89 ± 10 89 ± 10 63 ± 22 63 ± 22
Top 50 73 ± 12 86 ± 15 60 ± 25 86 ± 18 87 ± 18 59 ± 26 60 ± 25
All 77 ± 15 80 ± 19 48 ± 25 78 ± 24 77 ± 25 46 ± 25 46 ± 26
order-weighted average 70 85 62 88 88 62 62
Table 6: Accuracies of the logistic regression models for ASML, calculated over twelve train-and-deploy
exercises. Accuracies are reported in % with±the standard deviation. Here,Dtis the direction of the
order,Ptthe price bucket of the order, andVtthe volume bucket of the order.
Accuracy of predictions is high and stable over time for all algorithms.
23
The accuracies of the logistic
regression models are stable for all shares – for example, when predicting the direction of the orderDt,
accuracies range between 67% and 70% according to the order-weighted accuracy for all algorithms; see
Appendix C.1. The order-weighted average of the regressions forPtthat condition on direction of order
(^23) These accuracies are for the methodology outlined in Figure 3. We tested the robustness of these accuracies when one

increases the length of the training window. We find that the results in Table 6 are robust to the modelling choice in Figure
3. In particular, if we consider training on ‘expanding windows’, that is, for deployment in a given week, we train the models
on the data from all the weeks before, we find that the order-weighted average accuracies of the models are similar to those in
Table 6. More precisely, we obtain an order-weighted accuracy of 70% forDt, 84% forPt, and 65% forVtwhen training on
expanding windows, which is close to the ones reported in Table 6.
perform better than regression R2. This is not the case forVtwhere we have a similar performance for R2,
R6, and R7.
We compare the accuracies of the logistic regression models against a benchmark where the predicted
bucket is the bucket most frequently used by the algorithm in the training data. Table 7 reports the
outperformance of the logistic regression models over this benchmark.
Dt Pt Vt Pt|Dt= 1 Pt|Dt=− 1 Vt|Dt= 1 Vt|Dt=− 1
Top 5 15 ± 7 8 ± 10 5 ± 6 14 ± 14 13 ± 14 6 ± 8 5 ± 7
Top 10 15 ± 7 4 ± 9 2 ± 5 7 ± 12 7 ± 12 3 ± 6 2 ± 6
Top 20 17 ± 10 4 ± 7 3 ± 7 7 ± 10 6 ± 10 4 ± 8 3 ± 7
Top 50 20 ± 13 3 ± 6 3 ± 6 5 ± 8 4 ± 8 3 ± 7 3 ± 7
All 21 ± 17 3 ± 10 0 ± 8 3 ± 10 4 ± 11 1 ± 10 0 ± 11
order-weighted average 18 4 3 8 7 3 3
Table 7: Outperformance over benchmark for ASML, calculated over twelve train-and-deploy exercises.
Here,Dtis the direction of the order,Ptthe price bucket of the order, andVtthe volume bucket of the
order.
On average, the logistic regression models outperform the benchmark. The outperformance is largest for
predicting the direction of an order, at 18% on average. When predicting volume, we observe the smallest
outperformance over the benchmark. See Appendix C.1 for comparable results for other tickers.
Table 8 reports the outperformances of random forests when compared with the same benchmark as that
used in Table 7, and compared with the logistic regressions in Table 6.
outperformance
over benchmark over logistic
Dt Pt Vt Dt Pt Vt
Top 5 21 ± 7 7 ± 8 9 ± 10 6 ± 7 − 1 ± 3 4 ± 4
Top 10 19 ± 7 4 ± 7 5 ± 8 4 ± 6 − 1 ± 2 3 ± 3
Top 20 23 ± 11 3 ± 6 6 ± 8 6 ± 7 − 1 ± 2 2 ± 5
Top 50 24 ± 13 2 ± 5 4 ± 7 4 ± 6 − 1 ± 2 1 ± 4
All 22 ± 17 3 ± 7 2 ± 7 1 ± 7 0 ± 6 3 ± 5
order-weighted average 23 4 5 5 0 2
Table 8: Outperformance of random forests over benchmark and over logistic regressions for ASML, calcu-
lated over twelve train-and-deploy exercises. Here,Dtis the direction of the order,Ptthe price bucket of
the order, andVtthe volume bucket of the order.
Overall, random forests outperform the benchmark by 23% when predicting whether algorithms send a
buy or a sell order – this is 5% higher than the outperformance achieved by the logistic regression models.
The outperformance over the benchmark is stable across shares; in all cases, random forests outperform
logistic regression models. When predicting the price bucket, random forests outperform the benchmark by
4%, on average, which is the same outperformance attained by the multinomial logistic regression models.
Lastly, when predicting the volume bucket, the outperformance of random forests over the benchmark is
5%, which is 2% higher than the outperformance of the logistic regression over the benchmark. On average,
random forests tend to slightly outperform logistic regressions.
Going beyond logistic regressions comes at a cost. Machine learning models are known to have the
potential to leak parts of the training data when one discloses the model (weights or coefficients obtained)
as investigated in Shokri et al. (2017). That is, some models are vulnerable to privacy attacks – see Rigaki
and Garcia (2020) for a comprehensive survey. For many applications of our study this suggests it is more
appropriate to use logistic regressions because of their interpretability, their simplicity, and their privacy
features. We therefore focus on logistic models in the remainder of this study.
4.3 Out-of-sample accuracy of predictions: blind to member and algorithm
identification
In this section, we show the out-of-sample accuracy of the predictions when we train the models with features
that are built without using the identification of the member and of the algorithm.
Therefore, here we exclude features (i)-(v) in Appendix B, and replace them by features built without
member and algorithm identification. For example, we replace available volumes in the LOB excluding
trading algorithmaiand available volumes in the LOB by trading algorithmai, with one feature that
measures the total available volumes in the LOB. We do the same for features that measure the imbalance,
and features that measure the change in imbalance. We also exclude inventory features.
The train-and-deploy structure is the same as that described in Section 4.1. Table 9 shows the out-of-
sample accuracies of our models for the three target variables. We train and deploy only one model for all
agents per target variable because we exclude any agent-specific features and, for this exercise, one cannot
distinguish between algorithms.
Recall that the direction of orderDtcan take two values, the price bucketPtcan take three values, and
the volume bucketVtcan take nine values. The out-of-sample accuracy for predictingDtis 54%, which
is 16% lower than the accuracies obtained using the logistic regressions on the agent-labelled data. The
out-of-sample accuracy for predictingPtis 53%, which is 32% lower than the accuracies obtained using
the logistic regressions on the agent-labelled data, and the out-of-sample accuracy for predictingVtis 24%,
which is 38% lower.
Therefore the added value of using agent-labelled data in the predictions is large; much larger (one order
of magnitude higher) than the outperformance gained from going beyond linear models.
blinded features
accuracies decrease
Dt Pt Vt Dt Pt Vt
order-weighted average 54 53 24 16 32 38
Table 9: Out-of-sample accuracies of the logistic regressions trained with features that are built without
the member identification and without algorithm identification, and performance against logistic regression
trained on the original, agent-labelled features. Here,Dtis the direction of the order,Ptthe price bucket of
the order, andVtthe volume bucket of the order. The logistic regressions using the agent-labelled features
outperform the models that do not use the identity of member and identity of the algorithm by 16% for
predicting direction, 32% for price, and 38% for volume.
4.4 Feature importance
Feature importance encompasses a set of machine learning techniques that orders features according to their
contribution in explaining a given output variable. In this section, we assess the level of importance of each
of the features we employ to predictDt,Pt, andVt– see Section 3.1 for the complete list of features used
in our study.
We employ the ‘permutation feature importance’ approach in Breiman (2001). For a given feature, this
model-agnostic technique randomly permutes the values of the feature in the training set and computes the
change in model score by taking the difference between (i) the accuracy using the original data and (ii) the
accuracy with the permuted values of the feature. The changes in score indicate the sensitivity of the model
to the feature. The permutation of values is random, so we repeat this process ten times for each feature.
Permutation feature importance attempts to quantify the contribution of an individual feature to the
predictive model. This implies that if a variable is highly correlated with others, it will typically be assigned
a low importance, as the model is able to use other variables to obtain the same information.
4.4.1 Logistic regressions
We repeat the train-and-deploy exercises as described in Section 4.1 on the original features described in
Section 3.1 instead of the PCA-transformed features. The use of the original features as opposed to the
PCA-transformed features in the logistic regressions is restricted to the feature importance results.
24
The
results are reported below for each of the three regressions (R1, R2, R3).
First, we look at the most important feature to predictDt,Pt,Vtfor each of the top ten algorithms.
The ranking of the top ten algorithms is based on the order count over all deploy-weeks of the train-and-
deploy exercises. The most important feature is the feature with the highest median importance across the
twelve train-and-deploy exercises, the ten algorithms, and the ten random shuffles per feature (giving us
12 × 10 ×10 = 1,200 data points per feature describing their importance). See Figure 4 for the results.
25
imbalance
of algo top
five levels
inventory
of algo
best volumes
0
1
2
3
4
5
6
7
Dt(direction)
spread num
messages
0.1ms
inventory
of algo
cash of
algorithm
0
1
2
3
4
5
6
7
Pt(price)
inventory
of member
inventory
of algo
cash of
member
0
1
2
3
4
5
6
7
Vt(volume)
Figure 4: Most important feature for the top ten algorithms in ASML
Although there are differences in the importance of features across predictions (Dt,Pt, andVt), there is
significant overlap on the most important feature within each of the predictions. For example, the imbalance
of the volumes posted by the algorithm in the first five levels of the LOB is the most relevant feature for
seven out of the top ten algorithms when predicting the direction of the orderDt. This is similar to other
empirical results in the literature; see Cartea et al. (2018) where the authors find that the imbalance of
volumes resting in the LOB is a good predictor of the sign of the next liquidity taking order sent to the
exchange, that is, whether the next liquidity taking order will be to buy or to sell.
26
We find that, for
most algorithms, when deciding on the direction of the orderDt, the imbalance of the volumes posted by
themselves is more important than the imbalance of the volumes posted by other algorithms.
To predict the price bucket and volume bucket of orders, the bid-ask spread and intraday accumulated
inventory are respectively among the most important variables. Intuitively, as the spread widens (tightens),
the algorithm is more likely to post orders further from (closer to) the midprice. However, as seen earlier,
volumes of orders typically vary little per algorithm. We do not observe ‘directional’ variables, for example
an algorithm’s current exposure in the LOB, contributing to the price model, but we see that these play a
role in the price conditioned on direction model; see Subsection 4.5.7.
Next, Figure 5 shows a box-plot of the distribution of feature importances for the top ten algorithms
computed over twelve train-and-deploy exercises, and ten repetitions of random permutations. We sort
features according to their median importance and plot the top features – each feature contains 1,200 data
points corresponding to 12 sets of four consecutive weeks, 10 algorithms, and 10 repetitions.^27 The results in
the box-plots differ from the results in Figure 4 in that Figure 5 shows the distribution of feature importances
between algorithms instead of the most important feature for a given algorithm.
(^24) See Appendix C.2 for a robustness check where we compute the feature importance with the PCA-transformed features

and we show that the results are consistent.
(^25) In the figures that follow, we abbreviate the feature names for displaying purposes: “excl” is “excluding”, “chg” is “change”,

“m” is “minutes”, “s” is “seconds”, “ms” is “milliseconds”, “quad var” is “quadratic variation”, “num” is “number”, “agg” is
“aggressive”, and “algo” is “algorithm”.
(^26) Cartea et al. (2020) show that volume imbalance helps to predict the direction of the next (non-aggressive) limit order.
(^27) The results we obtain are robust to increasing the number of repetitions. In particular, the ordering does not change when

increasing the number of repetitions to 15.
0 0. 1 0. 2
ask volume of algo 0-
ask volume excl algo 0-
best ask volume
best bid volume
bid volume of algo 0-
bid volume excl algo 0-
imbalance of algo 0-
chg imbalance excl algo 0-
inventory of algo
inventory of member
cash of algorithm
cash of member
num messages 0.1ms
num messages 1ms
quad var 5m
spread
direction
0 0. 1 0. 2
price|buy
0 0. 1 0. 2
price|sell
0 0. 1 0. 2
volume|buy
0 0. 1 0. 2
volume|sell
Figure 5: Importance of features to explain the directionDtof an order, the price bucketPtof an order
conditional on the directionDt, and the volume bucketVtof an order conditional on the directionDt, for
ASML. We use permutation importance and logistic regressions, and only show the features that are in the
top ten most important features for at least one target variable.
The left column in Figure 5 shows the results for ASML when predicting the direction of the orderDt.
Again, the imbalance of volumes posted by the algorithm in the first five levels of the LOB is the most
important variable when predicting whether the algorithm will send a buy order or a sell order, followed by
the intraday accumulated inventory of the algorithm and the intraday accumulated inventory of the trading
member, and the best volumes posted in the LOB. This is similar for all shares; the same set of features
appears in each of the top ten features reported. This shows that inventory related features, volumes posted
on best quotes, and imbalances by the algorithm near the top of the LOB are important features to predict
the direction of the order.
The second and third panels in Figure 5 show the most important features to predict the price bucketPt
conditional on the direction of the order, i.e., respectivelyDt= 1 (buy), andDt=−1 (sell). Spread is the
most important feature to predict the price bucketPt, which is in line with the results in Figure 4. There
is consistency across shares – five of the top ten features in ASML also feature in the top ten of the three
other shares. The order of importance is consistent: spread is the most important predictor for three of our
four shares, and intraday accumulated inventory and cash of algorithm appear in the top three for ASML,
ING and AHOLD. Other important features are the number of messages sent in the last 100 microseconds
(i.e., 0.1 millisecond) and the volume posted by the algorithm on the first five levels of the LOB – both bid
and ask side. Also, the best bid volume is more important than the best ask volume to explain the price
bucket of buy orders, while for sell orders the best ask volume is more important than the best bid volume.
We draw attention to the differences and similarities between the top features that predict the direction
of the order and the price bucketPt. The main difference is the importance of spread, which is important
to predict the price of an order, yet does not appear in the top ten features to predict the direction of the
order.
28
Another difference is that the number of messages sent by all market participants in the last 100
microseconds is not important for predicting the direction of the order but it is important to predict price
bucketPt.
Lastly, the fourth and fifth panels in Figure 5 report the important features to predict the volume bucket
Vtconditional on the direction of the order, i.e., respectivelyDt= 1 (buy), andDt=−1 (sell). We see
little difference in feature importance between buy orders and sell orders. For all shares, the four inventory
variables are the most important features to predict the volume of an order. Also, the volume quoted by
the algorithm on the first five levels, on both the buy side and the sell side, is an important feature for all
shares. We see that the number of messages in the last second is an important feature for ING, AHOLD,
and TOMTOM. Contrary to the other output variables, we observe that quadratic variation – measured
over various intervals, is an important feature for ASML, ING, and AHOLD.
Inventory related features are important for predicting the three variables (Dt,Pt,Vt). Also, among
order book features, those that describe behaviour closer to the top of the LOB have more predictive power
than features that describe behaviour deeper in the LOB. Similarly, the number of messages sent in the
recent past (e.g., in the last 100 microseconds) tends to be more important than the number of messages
contained in longer horizons. This is consistent with the information that trading firms report to the AFM;
see AFM (2023).
We also observe that features describing the algorithm’s presence in the LOB tend to be more predictive
than features describing the presence of other participants. For example, imbalances of the algorithm and
volumes of the algorithm tend to be among the most important features, yet imbalances and volumes of
other agents are not – this is apart from best bid volume and best ask volume.
4.5 Clustering of algorithms
The model coefficients from predicting (i) direction of the orderDtwith a logistic regression, (ii) price bucket
Ptwith a multinomial logistic regression, and (iii) volume bucketVtwith a multinomial logistic regression,
capture information about the trading behaviour of an algorithm. More precisely, the coefficients show how
the features affect the probability that an agent selects a given value for the variables (Dt,Pt,Vt). Here, we
use these coefficients to cluster algorithms.
We revert to using the logistic regression models on the PCA-transformed features because of their higher
predicting power. In particular, we employ the model parameters from the logistic regressions to predict
trade directionDt. We focus onDtbecause results are easy to interpret and becauseDtis the output
variable where outperformances are highest. Recall that forDtwe have only one coefficient per feature,
instead of one coefficient per bucket per feature when predictingPtandVt.
Arguably, the clusters that arise within the regression coefficients are clusters that identify similarity
in trading behaviour. In Subsection 4.5.2 we compare these clusters of trading behaviour with the dealing
capacity of algorithms in Euronext Amsterdam – recall that the dealing capacities are (i) Liquidity Provider,
(ii) House, and (iii) Client and are determined by the exchange, not the regulator.
We employ the clustering technique described in Section 3.4 with the coefficients of the regressions
applied to the PCA-transformed features and a target of three clusters.
29
We do this over twelve sets of four
consecutive weeks of data each and refer to this as clustering exercises. Each clustering exercise includes
only the 96 algorithms present in all of the twelve clustering exercises we perform.
Figure 6 shows the size of the clusters we obtain in each clustering exercise. We include the transitions of
algorithms from a given cluster in one clustering exercise to another cluster in the next clustering exercise.
(^28) The spread is the cost of immediacy in the LOB and it is the same cost for buy and sell aggressive orders.
(^29) We choose a target of three clusters as it agrees with the number of primary dealing capacities in Euronext and it provides

a trade-off between stability of clusters over time and the discretisation of trader behaviour. Using a larger number of clusters
does not give significantly better separation between groups – we believe that this clustering should be better interpreted as a
quantisation of the distribution of trader behaviour, rather than identifying truly discrete trader types.
Figure 6: Clusters of trading behaviour for ASML for the twelve clustering exercises. The first column
represents the clustering exercise on weeks 41-44 of 2021, the second on weeks 42-45, etc. Cluster 1 is at the
top (blue), cluster 2 is in the middle (orange), and cluster 3 is at the bottom (green). The size of the bars
corresponds to the number of algorithms in the clusters — they add up to 96 algorithms in each of the twelve
clustering exercises. The grey areas connecting consecutive clustering exercises represent the transition of
algorithms from a cluster in one clustering exercise to a cluster in the next clustering exercise.
The number of algorithms in each of the three clusters varies for each of the twelve clustering exercises.
Given that the clusters aim to capture fundamental trading behaviour, it is crucial that they exhibit a degree
of consistency over time. Below, we study this point in more detail.
4.5.1 Stability through time
We explore the stability of the clusters across time. For this, given two consecutive clustering exercises
A and B where the last week in B is one week after the last week in A, we compute the probability that
two randomly chosen algorithms exhibit one of the following two properties: (i) they belong to the same
cluster in exercise A and belong to the same cluster in exercise B, or (ii) they do not belong to the same
cluster in exercise A and do not belong to the same cluster in exercise B. The blue line in Figure 7 shows
the probability of (i) or (ii) along the twelve clustering exercises we perform across time. That is, for two
consecutive clustering exercises, we count the pairs of algorithms that were clustered together and remained
clustered together, and those that were clustered differently and remained in different clusters, over all pairs
of algorithms. This calculation is a probability, so it lies in [0,1]. The red dash-line is the theoretical value
of the probability when algorithms are randomly shuffled and reassigned into the clusters while keeping the
number of algorithms in each cluster fixed as those we find for the first four weeks of data.
Intuitively, if the blue line is close to 1, then the clustering does not change over time, so the clusters are
stable. On the other hand, if the blue line is close to the red dash-line, then the clustering resembles that of
random reshuffling of algorithms.
45 46 47 48 49 50 51 52
1 2 3
0. 5
0. 6
0. 7
0. 8
0. 9
1
week
Figure 7: Stability of clusters for ASML across the twelve clustering exercises. They-axis shows the prob-
ability of two randomly selected agents in two consecutive clustering exercises A and B (i) being members
of the same cluster in A and B or (ii) different clusters in A and B. Thex-axis shows the last week of the
clustering exercise B.
The blue line lies above the red dotted line, which indicates stability of algorithm clusters. However,
there is a still a fair amount on instability in the clusters over time, as indicated by the gap between the blue
line and 1. Part of the instability might be due to algorithms updating their model parameters frequently,
as shown in the results of the survey (AFM (2023)). Recall that our clustering exercises include only the 96
algorithms present in all clustering exercises. Indeed, if any of these algorithms updates the model parameters
they use to send orders, then we expect that the parameters we obtain will change too, resulting in unstable
clusters.
We also study the link between the level of activity of the algorithms and the stability of their regression
coefficients. We use the logistic regression coefficients of the 53 original features described in Section 3.1,
and assign each algorithm according to the clustering obtained on the coefficients of the logistic regressions
on the PCA-transformed features. The first column of Table 10 lists the most important features to predict
the directionDtof an order. The second and third columns report the temporal deviation, which for a given
feature is defined as the standard deviation of the regression coefficients over time. We report the average
temporal deviation for the top ten algorithms (second column) and for the bottom ten algorithms (third
column) – the ranking is according to the number of orders sent during the first four weeks of data.
Top 10 Bottom 10
algorithms algorithms
imbalance of algo top five levels 0.27 0.33
inventory of algo 0.25 0.84
ask volume of algo top five levels 0.19 0.36
bid volume of algo top five levels 0.15 0.26
best bid volume 0.04 0.21
best ask volume 0.04 0.18
chg in imbalance excl algo 0-5 0.03 0.14
spread 0.03 0.22
net agg buy-sell last 1s 0.02 0.15
number of messages 1ms 0.02 0.21
number of messages 0.1ms 0.02 0.22
Table 10: Average temporal deviation of coefficients. The temporal deviation is the standard deviation
of the regression coefficients over twelve train-and-deploy exercises when predicting the directionDtof an
order.
Given the large number of data points used to fit the models of the top 10 algorithms, we expect that
the observed temporal deviation is principally due to changes in the algorithms themselves, for example
due to re-calibration of parameters. Conversely, for the bottom 10 algorithms, the number of observed
actions is relatively small, so the systematically higher temporal deviation for these algorithms may be due
to statistical estimation error, rather than systematic differences between the top and bottom algorithms.
Regardless of the reason for the large temporal deviation of the coefficients for the bottom algorithms, Table
10 lends support to the claim that the instability of our clusters is due primarily to variation in the estimated
coefficients of the smaller algorithms.
4.5.2 Types of market participants
Here, we explore how the clusters relate to the dealing capacity of market participants. Recall that we
study three dealing capacities: (i) Liquidity Providers (LP), (ii) House, and (iii) Client. Figure 8 shows the
confusion matrix between dealing capacity and clusters.
Cluster 1 Cluster 2 Cluster 3
Client
House
LP
20 3 1
16 7 0
18 15 16
Figure 8: Confusion matrix between the clusters obtained with the parameters from the first four weeks of
data and the dealing capacity for ASML.
The L-shaped confusion matrix in Figure 8 lends support to the claim that Liquidity Providers exhibit
the highest variability in trading behaviour.
30
Cluster 1 consists of an almost equal number of Liquidity Providers, Houses, and Clients. Furthermore,
the majority of algorithms with type House or Client are in cluster 1. Therefore, one would expect this
cluster to show trading behaviour associated with directional trading firms, such as hedge funds or pension
funds – see Subsection 4.5.5. Clusters 2 and 3 consist mostly of Liquidity Providers. Hence, one would
expect these clusters to exhibit behaviour most often associated with traditional market makers. The
statements above are speculative and comment on what (arguably) one would expect. In what follows, we
do not inform our understanding of trading behaviour with the dealing capacity in our dataset. We remark
that our understanding of trading behaviour comes from the coefficients of the regressions we build and the
clustering exercises we perform.
4.5.3 Clusters and members
Figure 9 shows the number of algorithms that each of the members have, grouped by cluster. The sorting
of members is similar to that of Figure 1 (by order count); we reproduce Figure 1 on the left-hand side for
comparison and convenience.
(^30) Here, L-shaped refers to the higher values in Figure 8 being arranged along the left column and bottom row.

0 2 4 6 8 10
2
1
1
2
1
5
4
1
1
1
2
2
3
1
1
1
5
1
1
3
3
1
1
1
2
1
4
1
4
7
1
9
2
1
5
2
3
5
1
2
1
Member
Cluster 1
Cluster 2
Cluster 3
10 8 6 4 2 0
2
1
1
5
3
8
2
5
3
10
4
5
1
1
1
2
1
4
4
1
4
2
1
1
1
1
1
1
8
7
2
1
1
1
Number of algorithms
Client
House
Liquidity Provider
Figure 9: Number of algorithms per dealing capacity (left) and cluster (right) per member in ASML. The
members are ordered from top to bottom using order count. We show 96 algorithms active each week for
the 16 weeks of data.
Figure 9 adds an extra dimension (member identification) to the confusion matrix in Figure 8. The figure
shows the variability of trading behaviour among members. Only three members (the first, third, and fourth
from top to bottom) have algorithms in each of the three clusters, five members have algorithms in two of
the three clusters, and the remaining twenty-two members have algorithms in only one cluster.^31 Comparing
the left-hand side to the right-hand side of Figure 9 we see that the three members with algorithms in each
of the three clusters are members with algorithms that are all registered as Liquidity Providers in Euronext.
(^31) Out of the twenty-two members with only one cluster of trading behaviour, twelve have only one algorithm.

4.5.4 Summary of results about trading behaviour
Consistently, across exercises in the following subsections, algorithms in cluster 3 show behaviour associated
with inventory-averse market makers. We describe the algorithms in this cluster as “market makers”. These
algorithms are keen to maintain a balanced presence in the LOB (e.g., they are more likely to post an order
on the bid side of the LOB if their volumes on the ask side of the LOB are higher than those of the bid side
of the LOB), they revert their inventories to zero, improve the spread, post liquidity at-the-touch, and do
not exhibit aggressive behaviour unless their inventory is large.
In contrast, algorithms in cluster 2 do not show inventory aversion, they send orders in the direction of
the imbalance of the volumes posted at the best quotes (send sell orders if the volume in the bid is much
smaller than the volume in the ask), have the lowest percentage (across clusters) of eager-to-trade orders
that provide liquidity inside the spread and have the highest percentage of eager-to-trade orders that traded
aggressively. We describe the algorithms in cluster 2 as “opportunistic traders’.
Lastly, algorithms in cluster 1 have position-building behaviour in terms of their own imbalances and the
way they choose direction as a function of its inventory and its imbalance in the LOB. These algorithms
have propensity to send eager-to-trade orders and at-the-touch orders. Around 82% of their eager-to-trade
orders trade aggressively. We describe the algorithms in cluster 1 as “directional traders”.
It should be recognised that this is only a description of these algorithms’ behaviour on this particular
exchange – some of them may be taking an opposite directional position in a different market, for which we
do not have data.
4.5.5 Clustered market behaviour
Table 11 provides summary statistics about the trading activity of the algorithms within the clusters.
32
To facilitate the reading of the tables that follow, we highlight in bold the numbers that are used for the
discussion in the main text.
Cluster 1 Cluster 2 Cluster 3
(directional traders) (opportunistic traders) (market makers)
number of algorithms 54 25 17
order count 1,639,267 1,901,239 3,712,355
trade count 573,417 343,874 461,874
order to trade ratio 2 .86 5.53 8. 04
order count % 23 26 51
trade count % 42 25 33
liquidity taking trade count 291,545 251,785 120,353
liquidity taking trade count % 51 73 26
average algo volume in ask side of LOB 0-5 1 .43 2.07 2. 47
average algo volume in bid side of LOB 0-5 1 .49 2.04 2. 48
average absolute imbalance of algo 0-5 1 .93 1.12 0. 77
average ask volume of algo 11-20 0.43 1.69 1.07
average bid volume of algo 11-20 0.44 1.68 1.08
average daily directionality 0 .53 0.46 0. 14
median resting time milliseconds 2 ,923 2,013 691
Table 11: Statistics for algorithms in clusters 1, 2, and 3 for ASML. First four weeks of data. Numbers in
bold are discussed in the main text.
Although algorithms in cluster 3 send the most orders (51%), they do fewer transactions (33% compared
to 42% for cluster 1 and 25% for cluster 2). This results in a higher order to trade ratio for cluster 3
(8.04) than for clusters 1 and 2 (2.86 and 5.53 respectively). Furthermore, algorithms in cluster 3 have
a relatively low fraction of liquidity taking transactions; 26% of their transactions are liquidity taking,
versus 51% and 73% for clusters 1 and 2. This suggests that algorithms in cluster 3 behave as traditional
market makers. Algorithms in clusters 2 and 3 post more volume (on average) on the first five levels of the
LOB than algorithms in cluster 1. Similarly, algorithms in clusters 2 and 3 have more balanced volumes
(^32) Recall that a single aggressive order may result in multiple trades (depending on the number of limit orders it executes

against). The number of orders in Table 4 is larger than the number of orders in Table 11 because the later only includes the
algorithms present in all of the twelve clustering exercises we perform.
posted in the LOB when compared to algorithms in cluster 1.
33
The second to last entry of Table 11
shows the average directionality per day by algorithms of the relevant clusters. We use the definition of
directionality in Van Kervel and Menkveld (2019), i.e., the absolute net volume divided by total volume
traded by the algorithm. Algorithms in cluster 3 are the least directional and algorithms in cluster 1 are
the most directional. This reinforces the understanding that algorithms in cluster 3 behave as traditional
market makers, and algorithms in cluster 1 behave as directional traders. The last entry of Table 11 shows
the median resting time of orders per cluster.
34
Algorithms in cluster 3 have the lowest median resting time
with 691 milliseconds, versus 2,013 milliseconds for cluster 2, and 2,923 milliseconds for cluster 3.
In the next sections, we investigate how the various features we consider affect the average behaviour of
algorithms in each of these clusters. We focus on the impact of features on direction and price of orders,
as the model for volume does not display much benefit over a naive model (as seen in Table 7). More
precisely, in Subsection 4.5.6 we study the average coefficients associated with the regressions for predicting
the directionDt, then, Subsection 4.5.7 discusses the average coefficients for predicting the price bucketPt
conditional on direction, and Subsection 4.5.8 explores liquidity taking activity and liquidity provision in
more detail.
4.5.6 Direction: average behaviour
We show the average regression coefficients per cluster for the most important features to explain the direction
of the orderDt. Table 12 reports the average coefficients per feature computed on the first four weeks of data.
For each of the clusters, the average is taken over all algorithms in the cluster. To obtain the coefficients of
the 53 features, for each of the 96 algorithms, we perform a matrix multiplication of the coefficients of the
logistic regressions on the PCA-transformed features (96×30 matrix) and the principal components matrix
(30×53 matrix), both computed in Section 4.1. We represent the effects of the volume in the book with the
total volume and the imbalance (rather than the separate volumes on the bid and ask side).
In the tables which follow, we exclude features where the magnitude of the average coefficients is smaller
than or equal to 0.1 for all clusters. We order features by their average absolute coefficient size.
Cluster 1 Cluster 2 Cluster 3
(directional traders) (opportunistic traders) (market makers)
imbalance of algo 0-5 1. 48 − 1. 04 − 0. 42
imbalance of algo 11-20 0.21 − 0. 97 0.81
imbalance of algo 6-10 0.48 −1.00 −0.09
cash of algorithm 0 .70 0. 13 − 0. 30
inventory of algo 0 .70 0. 12 − 0. 30
best ask volume −0.26 −0.20 0.23
best bid volume 0.24 0.19 −0.24
volume of algo 11-20 0.16 − 0. 05 0.40
volume of algo 0-5 −0.07 −0.04 −0.28
chg imbalance excl algo 0-5 0.04 0.16 0.05
net agg buy-sell last 1s −0.03 0.07 0.10
return 1s 0.01 0.08 0.10
volume of algo 6-10 0.01 − 0. 11 0.04
chg imbalance excl algo 6-10 0.00 0.03 0.10
Table 12: Average regression coefficients per cluster on first four weeks of training data for ASML when
predicting directionDt. Numbers in bold are discussed in the main text.
We recall the formulation for the logistic regression in equation (5), whereDt= 1 means that the order is
to buy andDt=−1 means that the order is to sell. Thus, given that all features are normalised and centred
around zero, all else being equal, if an average coefficient is positive (negative) it means that positive values
of the relevant feature increase (decrease) the probability of a given order being a buy order; and vice-versa
for negative values of the relevant feature.
Table 12 provides a number of stylised facts about the difference among clusters to understand differences
in trading behaviour.
(^33) Recall that if the imbalance of the volumes posted by the algorithm is close to zero then the volume posted by the algorithm

on the bid side is close to the volume posted by the algorithm on the ask side.
(^34) Resting time is the difference between time of cancellation and time of entry of limit orders.

(D-i) Cluster 1 is the only cluster with an average positive value for the coefficient for the imbalance of the
algorithm in the first five levels – recall that when imbalance is positive the algorithm has more volume
posted in the bid than in the ask. Thus, algorithms in cluster 1 are inclined to send a buy order if they
have already posted more volume in the bid than in the ask (and similarly with the roles of bid and
ask, and buy and sell reversed). Conversely, algorithms in clusters 2 and 3 are more likely to revert
their imbalance back to a level where the volume provided in the ask side of the LOB is close to that
provided in the bid side of the LOB. This suggests that clusters 2 and 3 behave in some ways like
traditional market makers, i.e., trading algorithms that provide liquidity in both sides of the LOB in
a balanced way. In contrast, algorithms in cluster 1 tend to post liquidity with a preferred direction,
which is consistent with most algorithms of House and Client type being in cluster 1.
(D-ii) Cluster 3 exhibits mean-reversion to zero in inventories at the algorithm level, that is, when the intraday
accumulated inventory is positive they are more likely to send sell orders, while the inventories of
algorithms in cluster 2 are not mean-reverting to zero.
35
Conversely, algorithms in cluster 1 have a
strong preference to send orders in the direction of their inventory (buy orders if inventory is positive
and sell orders if inventory is negative).
(D-iii) The estimation we perform through the PCA coefficients leads to an “averaging” effect over coefficients
for similar features. This is seen in the coefficients associated with the inventory and cash of the
algorithm which are similar.
In summary, algorithms in cluster 3 exhibit trading behaviours that the literature describes as market
markers. For example, algorithms in cluster 3 seem keen to maintain a balanced provision of liquidity and
take actions to revert their inventories to zero. On the other side of the spectrum we have the algorithms in
cluster 1. These algorithms do not balance the liquidity posted in the LOB, and their orders are more likely
to be in the same direction as their inventory (i.e., position building and directional trading). Algorithms
in cluster 2 lie somewhere in the middle of the behaviour observed by algorithms in clusters 1 and 3. More
precisely, out of the coefficients shown in Table 12, there are three instances in which the sign of the coefficient
associated with cluster 2 is different from both of the signs of the coefficients for clusters 1 and 3. The three
instances involve volumes deep in the LOB.
4.5.7 Price limit: average behaviour
To gain further insights into the trading behaviour of the algorithms in the above clusters we study how
they choose their price limits as measured through the price bucketPt.
Cluster 1 Cluster 2 Cluster 3 Total
(directional traders) (opportunistic traders) (market makers)
Pt= 1 178,333 128,174 391,016 697,523
Pt= 2 386,678 456,828 2,257,863 3,101,369
Pt= 3 1,074,256 1,316,237 1,063,476 3,453,969
Total 1,639,267 1,901,239 3,712,355 7,252,861
Table 13: Order count per price bucketPtand cluster using first four weeks of training data for ASML.
Recall that by the definition ofPtin (3), the first price bucket contains orders whose price limit is more
generous than the best quotes; that is, the limit price is lower than the best ask price for a sell order or the
limit price is greater than the best bid price for a buy order. The second price bucket contains orders whose
limit price equals that of the best quotes; that is, the limit price is the best ask price if the order is to sell
or the limit price is the best bid price if the order is to buy. The third price bucket contains orders deeper
in the LOB.
Thus, given that orders in the first price bucket either (i) trade with the opposite side of the LOB, (ii)
improve the spread, or (iii) get cancelled upon entry because their time-in-force precludes them from resting
(^35) See Chapter 10 in Cartea et al. (2015) for a mathematical model of the market making problem illustrating how risk aversion

plays a role in how market makers provide liquidity as a function of their inventory.
in the LOB and their price limit is better than best quotes, we refer to them as orders that show “eagerness
to trade”. Similarly, recall that we call price bucket one the “eager-to-trade” bucket. Next, we study the
average regression coefficients associated with orders in the “eager-to-trade” bucket (Pt= 1).
Dt= 1 (Buy) Dt=−1 (Sell)
Cluster 1 Cluster 2 Cluster 3 Cluster 1 Cluster 2 Cluster 3
intercept − 0. 21 − 0. 46 − 0. 74 − 0. 17 − 0. 48 − 0. 77
best bid volume 0 .35 0.30 1. 09 − 0. 21 − 0. 17 − 0. 26
best ask volume − 0. 20 − 0. 16 − 0. 30 0 .35 0.30 1. 05
imbalance of algo 0-5 − 0 .00 0.29 0. 68 0. 08 − 0. 32 − 0. 75
spread 0 .11 0.07 0. 56 0 .10 0.07 0. 54
volume of algo 0-5 − 0. 25 − 0 .12 0. 40 − 0. 29 − 0 .08 0. 30
Pt= 1 volume of algo 11-20 0. 04 − 0. 32 − 0. 22 0. 07 − 0. 26 − 0. 21
num messages 1ms 0 .17 0. 20 − 0. 08 0 .21 0. 15 − 0. 06
volume of algo 6-10 −0.13 −0.23 0.11 −0.13 −0.18 0.04
imbalance of algo 6-10 −0.07 0.04 −0.20 0.10 −0.08 −0.28
num messages 0.1ms 0 .12 0. 19 − 0. 08 0 .15 0. 13 − 0. 06
num messages 0.1s 0 .13 0.04 0. 07 0 .15 0.09 0. 07
imbalance of algo 11-20 −0.02 0.11 −0.15 0.02 −0.09 −0.05
Dt= 1 (Buy) Dt=−1 (Sell)
Cluster 1 Cluster 2 Cluster 3 Cluster 1 Cluster 2 Cluster 3
intercept − 0. 15 − 0 .58 1. 25 − 0. 13 − 0 .68 0. 94
volume of algo 0-5 0.13 0.52 0.16 0.24 0.52 0.21
volume of algo 6-10 −0.03 0.38 0.19 0.00 0.33 0.17
volume of algo 11-20 −0.19 0.06 0.14 −0.27 −0.01 0.06
imbalance of algo 0-5 − 0 .02 0.22 0. 09 0. 01 − 0. 21 − 0. 15
Pt= 2 imbalance of algo 11-20 −0.05 0.09 0.07 0.06 −0.07 0.13
best ask volume 0.08 0.03 0.04 −0.06 0.08 −0.12
imbalance of algo 6-10 −0.04 0.09 0.18 0.02 −0.05 −0.04
num messages 1ms − 0. 09 − 0. 01 − 0. 04 − 0. 11 − 0. 02 − 0. 05
num messages 0.1s − 0. 10 − 0. 02 − 0. 01 − 0. 12 − 0. 03 − 0. 03
spread −0.00 0.04 −0.10 −0.01 0.04 −0.09
Dt= 1 (Buy) Dt=−1 (Sell)
Cluster 1 Cluster 2 Cluster 3 Cluster 1 Cluster 2 Cluster 3
intercept 0.36 1. 04 −0.51 0.30 1. 16 −0.17
imbalance of algo 0-5 0.02 −0.51 −0.76 −0.09 0.52 0.90
best bid volume − 0. 31 − 0. 36 − 1. 00 0 .13 0.15 0. 22
best ask volume 0 .11 0.13 0. 26 − 0. 29 − 0. 38 − 0. 92
volume of algo 0-5 0.11 −0.40 −0.55 0.05 −0.44 −0.51
Pt= 3 spread −0.11 −0.11 −0.46 −0.09 −0.11 −0.45
volume of algo 11-20 0.14 0.26 0.09 0.19 0.26 0.15
volume of algo 6-10 0.16 −0.15 −0.30 0.13 −0.16 −0.21
imbalance of algo 6-10 0.11 −0.13 0.02 −0.11 0.14 0.31
num messages 0.1ms −0.07 −0.23 0.11 −0.09 −0.16 0.09
num messages 1ms −0.09 −0.20 0.12 −0.10 −0.13 0.11
imbalance of algo 11-20 0.07 −0.20 0.08 −0.08 0.16 −0.08
Table 14: Average regression coefficients for price bucket describing eager-to-trade orders (Pt= 1), at-the-
touch orders (Pt= 2), and orders deeper in the LOB (Pt= 3) per cluster, on first four weeks of training
data for ASML, conditioning on the direction of orderDt. Numbers in bold are discussed in the main text.
We observe the following stylised features of trading algorithms in each cluster:
(P-i) According to the intercept of the regressions, algorithms in cluster 3 are noticeably less likely than
those in clusters 1 and 2 to place orders that are eager to trade.
(P-ii) For algorithms in all clusters, the higher the imbalance of the volumes (by all algorithms) posted at
the best bid and ask, the more likely a buy order is eager to trade. This effect is most pronounced for
algorithms in cluster 3, where the bid volume has a much greater impact on orders to buy than the
ask volume; conversely for orders to sell.
(P-iii) Algorithms in cluster 1 seem indifferent to their own imbalance in the book when determining eagerness
to trade, whereas imbalance of their own volume indicates a moderate eagerness to trade for cluster
2 algorithms, and a strong eagerness for cluster 3. The direction of the order for cluster 3 is in the
direction of the imbalance (so if an algorithm has a higher bid than ask volume posted, which means
they have a positive imbalance, its buy orders are typically more eager to trade and its sell orders are
typically less eager to trade).
(P-iv) Regardless of the direction, the wider the bid-ask spread the more likely algorithms are to send orders
that are eager to trade.
36
This is particularly pronounced for cluster 3 (below we see that this cluster
often refills the book when the spread is above one tick).
(P-v) For algorithms in clusters 1 and 2, the higher their own posted volumes at levels 0 to 5 on either the
bid or ask side, the less likely they are to send an order (either to buy or to sell) that is eager to trade.
Algorithms in cluster 3 exhibit the opposite behaviour; already having volume at the first five levels of
the LOB makes it more likely for them to send eager-to-trade orders. For algorithms in clusters 2 and
3, having volume posted deeper in the book decreases the likelihood of sending eager-to-trade orders.
(P-vi) For algorithms in clusters 1 and 2, the higher the number of recent messages, the more likely an order
is eager to trade, in either direction.
(P-vii) For all algorithms, there is a reasonably clear symmetry when the roles of bid and ask, and buying and
selling, are reversed.
We also look at the second price bucket (Pt= 2), which describes orders adding volume to the LOB at
the current best bid or best ask (also known as posting ‘at-the-touch’), and at the third (Pt= 3, whose
coefficients are simply the negative sum of those forPt= 1,2), which describes orders adding volume deeper
within the LOB.
By looking at Table 14 forP= 2 andP= 3, we observe the following stylised facts for the typical
behaviour of each cluster. It should be emphasised that these observations are for a given direction of order,
and so the conclusions should be read in the light of Table 12.
(P-viii) Cluster 3 algorithms are much more likely to place their orders at-the-touch than either of the other
clusters (see the intercept of Table 14 forP= 2). Cluster 2 is less likely to place its orders at-the-touch
than cluster 1 (all other things being equal).
(P-ix) Algorithms in cluster 2 have the highest propensity to place orders deeper in the book.
(P-x) For all algorithms, the higher the volume at the best ask price and the lower the volume at the best
bid (i.e., the lower the imbalance of the best volumes in the LOB) the more likely that a buy order
will be placed deeper in the LOB; conversely for sell orders. This indicates that algorithms pre-empt
that when a large volume builds at the best ask (bid) price, then prices will likely move down (up),
hence why they choose to place their buy (sell) orders deeper in the LOB.
(P-xi) The likelihood that an algorithm in cluster 2 posts at-the-touch is affected by its own imbalance near
the touch. The likelihood is impacted in the direction of the imbalance (so if an algorithm has a higher
bid than ask volume posted, which corresponds to a positive imbalance, it is more likely that the
algorithm posts buy orders at the best bid price). A weaker effect is observed for cluster 3, but little
effect for cluster 1.
(P-xii) For all algorithms, an increase in the recent number of messages reduces the probability of posting
at-the-touch in either direction.
In summary, for algorithms in the first two clusters, the higher the number of recent messages (sent
to the exchange by all market participants) the more likely they are to send an eager-to-trade order (in
either direction), similarly, for algorithms in all clusters, the higher the spread the less likely an algorithm
is to place eager-to-trade or at-the-touch orders. Again, algorithms in cluster 1 seem indifferent to their
own imbalance in the book when deciding to send an eager-to-trade order. Algorithms in cluster 2 have
a moderate eagerness to trade according to their imbalance in the LOB (similar for posting at-the-touch).
(^36) We refine this result in Subsection 4.5.8. In particular, we find that the larger the bid-ask spread, the more likely all clusters

are to provide liquidity inside the spread, and the less likely they are to cross the spread.
Algorithms in cluster 3 have a strong eagerness to trade according to their imbalance in the LOB, and they
also have the highest baseline probability of posting at-the-touch. These results are consistent with our
previous findings about cluster 3 exhibiting behaviour associated with market makers.
The low value of the coefficients for the inventory of the algorithm (this predictor is absent from Table
14, indicating that all clusters had an average coefficient below 0.10 in magnitude) may suggest that this
has limited effect on the eagerness to trade. However, this may be due to the limitation of our statistical
model to linear-logistic relationships. The plots in Figure 10 show the percentage of orders which were
eager to trade, as a function of (transformed, intraday accumulated) inventory. In the rare cases where
transformed inventory exceeds 6 (in absolute value), there is a noticeable increase in the eagerness to trade
for all algorithms. For algorithms in cluster 3, this effect is smoother as inventory changes, and larger than
in clusters 1 or 2.
0 2 4 6 8
0%
5%
10%
15%
20%
25%
30%
35%
Cluster 1 (directional traders)
0 2 4 6 8
Cluster 2 (opportunistic traders)
0 2 4 6 8
Cluster 3 (market makers)
Figure 10: Percentage of eager-to-trade orders as a function of the absolute value of the transformed intraday
accumulated inventory of the algorithm. See Appendix B for the formula of the log-inventory of the algorithm.
4.5.8 Liquidity taking activity
We now focus on orders in price bucketPt= 1, that is, orders with prices better than the current best bid
or ask price. When such an order is sent, there are three possible scenarios, which we label by a variableAt
describing whether it takes liquidity, provides liquidity, or is a missed attempt.
(i) The caseAt= 0 encompasses aggressive trading, i.e., orders that cross the spread. These are buy
orders with limit price greater than or equal to the best ask price, or sell orders with limit price less
than or equal to the best bid price. Almost all orders within this category are executed upon entry
(or partially executed) against liquidity on the other side of the LOB depending on the time-in-force,
the limit price of the order, the volume of the order, and the liquidity available in the LOB.
37
From
Table 3 we know that orders with FoK time-in-force are negligible so for all practical purposesAt= 1
is aggressive behaviour.
(ii) The caseAt= 1 covers generous provision of liquidity. These are orders that provide liquidity inside
the spread. The time-in-force of the orders is DAY and their limit price is higher than the best bid
price and lower than the best ask price if the order is to buy; conversely for sell orders.
(iii) The caseAt= 2 describes missed attempts. These are orders that are cancelled upon entry because
their price limit is not generous enough to trade with the available liquidity in the LOB and their
time-in-force precludes them from resting in the LOB. This category covers (i) orders to buy with limit
price less than the best ask price and IoC time-in-force; it also covers (ii) sell orders with limit price
greater than the best bid price and IoC time-in-force. Missed attempts are likely aimed at (a) liquidity
(^37) We provide an example. Assume the best ask price in the LOB is 100 and the volume available is 50 units. An IoC order

arrives willing to buy with price limit of 100 and volume of 100, then, 50 units will get executed against the available liquidity
and the remaining 50 will be cancelled. Alternatively, if the order had FoK time-in-force then it is cancelled upon arrival.
that disappeared over the latency period, (b) hidden liquidity, or (c) new liquidity being added over
the latency period; see Cartea and S ́anchez-Betancourt (2023).
For example, during the first four weeks of data for ASML we find that algorithms in cluster 1 (respectively
cluster 2 and cluster 3) sent 178,333 orders (respectively 128,174 and 391,016 orders) to buy or to sell that
were eager to trade; around 82% of these orders (respectively 89% and 18%) traded against the opposite
side of the LOB, i.e., show aggressive behaviour, similarly, 17% of the orders (respectively 10% and 81%)
provided liquidity inside the spread, i.e., generous liquidity provision, and 1% of the orders (respectively 1%
and 1%) were cancelled by the exchange upon entry because of their lack of generosity and time-in-force,
i.e., missed attempts. These results are summarised in Table 15 together with the usage of IoC associated
with each of the clusters.
Cluster 1 Cluster 2 Cluster 3 Total
(directional traders) (opportunistic traders) (market makers)
number of ordersPt= 1 178,333 128,174 391,016 697,523
IoC orders 68% 84% 4% 245,988
At= 0 (aggressive trading) 82% 89% 18% 333,073
At= 1 (generous liquidity) 17% 10% 81% 364,650
At= 2 (missed attempt) 1% 1% 1% 2,800
Table 15: Percentage of orders sent withAt= 0 (aggressive trading),At= 1 (generous liquidity), and
At= 2 (missed attempts) out of all eager-to-trade orders, i.e., orders withPt= 1.
These percentages support our findings that algorithms in cluster 3 make markets for ASML. Algorithms
in cluster 3 have the lowest percentage of aggressive behaviour, and they have the highest proportion of
provision inside the spread. On the other hand, algorithms in clusters 1 and 2 render similar proportions
for aggressive behaviour and missed aggressive behaviour, but they differ in their proportions for providing
liquidity inside the spread. Here, algorithms in cluster 1 provide liquidity inside the spread more often than
algorithms in cluster 2. For both clusters 1 and 2, the vast majority of IoC orders successfully trade, and
only a small fraction are cancelled.
Next, we perform a regression similar to those above to predict if the order will take liquidity, conditioned
on direction and conditioned on the order being in the first price bucket. We recall that if an order sent at
timetconsumes liquidity we writeAt= 0, if the order provides liquidity inside the spread we writeAt= 1,
and we writeAt= 2 otherwise. Table 16 shows the average coefficients for the regressions associated with
At= 0 (aggressive trading),At= 1 (generous provision of liquidity), andAt= 2 (missed attempts).
Pt= 1, Dt= 1 (Buy) Pt= 1, Dt=−1 (Sell)
Cluster 1 Cluster 2 Cluster 3 Cluster 1 Cluster 2 Cluster 3
intercept 0 .73 0. 97 − 0. 00 0 .82 1.08 0. 10
spread − 1. 09 − 0. 95 − 1. 37 − 1. 29 − 0. 88 − 1. 35
best bid volume − 0. 36 − 0. 35 − 0. 50 − 0. 93 − 0. 65 − 0. 78
best ask volume − 0. 77 − 0. 65 − 0. 77 − 0. 44 − 0. 26 − 0. 47
volume of algo 0-5 −0.49 −0.84 0.08 −0.43 −0.77 −0.02
volume excl algo 0-5 0.30 0.31 0.45 0.30 0.29 0.42
volume of algo 6-10 −0.39 −0.58 0.10 −0.30 −0.58 0.03
quad var 5m − 0. 20 − 0. 13 − 0. 34 − 0. 27 − 0. 16 − 0. 36
quad var 1m − 0. 17 − 0. 12 − 0. 28 − 0. 21 − 0. 12 − 0. 30
quad var 60m 0 .14 0.25 0. 16 0 .16 0.19 0. 17
At= 0 quad var 15m − 0. 14 − 0. 04 − 0. 25 − 0. 19 − 0. 09 − 0. 26
last volume transacted −0.08 −0.12 −0.15 −0.10 −0.12 −0.12
imbalance of algo 0-5 0.12 −0.02 0.21 −0.12 −0.00 −0.20
num messages 0.1ms −0.21 −0.12 0.01 −0.16 −0.14 0.02
volume of algo 11-20 −0.18 −0.09 −0.00 −0.14 −0.19 −0.01
net agg buy-sell last 1s −0.09 −0.12 −0.05 0.13 0.10 0.10
imbalance excl algo 0-5 −0.09 −0.11 −0.01 0.11 0.17 0.08
num messages 0.1s 0.07 0.11 0.08 0.11 0.09 0.08
agg sell last 360s 0.10 0.09 0.10 0.08 0.05 0.06
agg sell last 60s 0.09 0.06 0.13 0.07 0.05 0.09
agg sell last 1s 0.03 0.06 0.02 −0.10 −0.10 −0.09
num messages 1ms −0.15 −0.03 0.01 −0.10 −0.05 0.03
agg buy last 60s 0.04 0.04 0.06 0.10 0.07 0.06
return 300s 0.04 0.04 0.12 0.02 0.01 0.08
Pt= 1, Dt= 1 (Buy) Pt= 1, Dt=−1 (Sell)
Cluster 1 Cluster 2 Cluster 3 Cluster 1 Cluster 2 Cluster 3
intercept 0. 05 − 0 .25 0. 94 0. 10 − 0 .24 0. 83
spread 0 .85 0.57 1. 25 0 .95 0.55 1. 25
best bid volume 0 .33 0.28 0. 50 0 .71 0.39 0. 71
best ask volume 0 .62 0.37 0. 70 0 .36 0.23 0. 49
volume of algo 0-5 0.21 0.68 −0.17 0.17 0.55 −0.15
volume of algo 6-10 0.09 0.47 −0.16 0.02 0.40 −0.13
volume excl algo 0-5 −0.12 −0.15 −0.36 −0.13 −0.15 −0.34
quad var 5m 0 .17 0.08 0. 32 0 .15 0.04 0. 29
quad var 60m − 0. 14 − 0. 16 − 0. 14 − 0. 20 − 0. 19 − 0. 18
quad var 1m 0 .13 0.07 0. 25 0 .11 0.05 0. 25
At= 1 imbalance excl algo 0-5 −0.14 −0.11 −0.08 0.20 0.12 0.11
quad var 15m 0 .11 0.02 0. 23 0. 07 − 0 .02 0. 20
num messages 1ms −0.17 −0.09 0.02 −0.22 −0.07 0.05
num messages 0.1ms −0.17 −0.04 0.01 −0.22 −0.05 0.04
inventory of algo 0.12 0.11 −0.03 0.05 0.11 0.05
cash of algorithm 0.12 0.11 −0.03 0.05 0.11 0.05
agg sell last 60s −0.07 −0.03 −0.08 −0.11 −0.04 −0.10
imbalance of algo 0-5 −0.06 −0.08 −0.12 0.02 0.00 0.12
net agg buy-sell last 1s −0.10 −0.07 −0.03 0.08 0.07 −0.03
volume excl algo 6-10 0.08 0.02 0.06 0.05 −0.02 0.13
volume of algo 11-20 0.02 0.11 −0.01 −0.04 0.12 0.04
net agg buy-sell last 360s −0.06 −0.03 0.03 0.11 0.07 0.06
Pt= 1, Dt= 1 (Buy) Pt= 1, Dt=−1 (Sell)
Cluster 1 Cluster 2 Cluster 3 Cluster 1 Cluster 2 Cluster 3
intercept −0.78 −0.72 −0.94 −0.92 −0.83 −0.93
spread 0 .24 0.39 0. 12 0 .35 0.33 0. 11
imbalance excl algo 0-5 0.23 0.23 0.09 −0.31 −0.29 −0.19
volume of algo 0-5 0.28 0.16 0.09 0.26 0.22 0.17
num messages 0.1ms 0.38 0.17 −0.01 0.38 0.18 −0.06
At= 2 volume of algo 6-10 0.30 0.11 0.06 0.28 0.17 0.10
num messages 1ms 0.32 0.12 −0.03 0.32 0.12 −0.08
net agg buy-sell last 1s 0.19 0.20 0.07 −0.20 −0.17 −0.07
volume excl algo 0-5 −0.18 −0.16 −0.10 −0.17 −0.13 −0.08
last volume transacted 0.12 0.11 0.09 0.16 0.13 0.06
best bid volume 0 .03 0. 07 − 0. 00 0 .21 0.25 0. 07
best ask volume 0 .15 0.28 0. 07 0 .08 0. 03 − 0. 02
agg sell last 1s −0.10 −0.13 −0.07 0.15 0.12 0.03
Table 16: Average regression coefficients of orders that (i) take liquidity upon arrival in the exchange
(At = 0), (ii) provide liquidity inside the spread upon arrival in the exchange (At = 1), and (iii) are
immediately cancelled by the exchange upon arrival (At= 2), conditioned on orders being eager to trade
and conditioned on the directionDtof orders. Numbers in bold are discussed in the main text.
We observe the following stylised features of trading algorithms in each cluster:
(LT-i) According to the intercept of the above tables, algorithms in clusters 1 and 2 are more likely to send
aggressive orders that consume liquidity from the opposite side of the LOB. Algorithms in cluster 3
have the highest propensity to send orders that provide liquidity inside the spread, whereas algorithms
in cluster 2 are unlikely to provide liquidity inside the spread. This is consistent with the proportions
reported at the beginning of this subsection, where algorithms in cluster 2 exhibit the lowest percentage
of liquidity provision inside the spread (10%).
(LT-ii) Once again, algorithms across clusters agree on their behaviour according to spread. Given they post
eager-to-trade orders, algorithms are more likely to provide liquidity inside the spread when the bid-ask
spread is wider. This is consistent with the literature, in that high-frequency traders are more likely
to add limit orders to the book when the spread is wide; see Carrion (2013), Hagstr ̈omer et al. (2014),
Jarnecic and Snape (2014). Similarly, algorithms are more likely to have missed attempts when the
spread is wider.
On the other hand, the wider the spread is, the less likely algorithms are to consume liquidity from the
other side of the LOB. This is consistent with Hendershott et al. (2009), who show that algorithms are
quick to post limit orders when spreads are wide, but are more likely to take liquidity when spreads
are narrow. This observation is unsurprising, as for ASML the spread is usually equal to one tick, in
which case it is not possible to provide liquidity inside the spread nor to miss a trade attempt, making
crossing the spread relatively more likely. This should also be compared with observation (P-iv); the
sums of the spread coefficients in Table 14 withPt= 1 and Table 16 withAt= 0 show that the
likelihood of an order crossing the spread is reduced when the spread is larger, unconditionally on
whether the order is eager to trade or not.
(LT-iii) The results we discuss for spread also hold for some of the variables describing recent activity (as one
would expect). For example, for most horizons of the quadratic variation, the larger the quadratic
variation, the less likely algorithms will show aggressive behaviour, and the more likely it is that they
will post liquidity inside the spread. For both cases, the quadratic variation over the last trading hour
has an inverse effect.
(LT-iv) The best available volumes in the LOB (best bid volume and best ask volume) also have a similar
consistent effect across algorithms in all three clusters. The higher the available volumes in the market,
the less likely algorithms are to consume liquidity from the opposite side of the LOB, the more likely
they are to provide liquidity inside the spread, and the less likely they are to miss aggressive attempts.
For liquidity taking, we observe that the magnitude of coefficients for the best ask volume is roughly
twice the magnitude of coefficients for the best bid volume when describing the aggressiveness of orders
to buy; conversely for orders to sell. This is consistent with the findings in (P-x); indeed, if the market
builds volume in the best ask price for example, then everything else being equal, one expects prices
to move down, this in turn lowers the probability of an aggressive order to buy as a consequence of the
expected price move; conversely for the case of sell orders.
(LT-v) For liquidity provision inside the spread, we observe that the coefficients for best bid volume and best
ask volume swap their importance when considering buys or sells. In particular, the best buy volume
is more important to determine if a sell order will be sent inside the spread; conversely for the best
ask volume and buy orders.
These results are consistent with what one expects from the usual models of trading activity.
Our work has focused on ASML, which is a liquid asset. Similar clustering results hold for ING, see
Appendix C.3. For shares with lower volumes traded (here AHOLD and TOMTOM), the results are less
clear, possibly due to lower levels of trading in these shares. See Tables A13–A18 for the clustered coefficients
for other shares.
5 Implications for supervision
The results above provide a starting point to formalise and to quantify the links between a number of
market features and the behaviour of trading algorithms. Our findings provide statistical evidence for many
microstructural stylised facts discussed in the literature, and our study is the first to confirm these facts
with a unique data set that contains both member and algorithm identification. One key contribution is to
show how algorithms make trading decisions as a function of 53 market features, some of which are visible
to all market participants and the others are idiosyncratic features of the algorithms and of each market
participant.
We believe the findings in the above sections are of special relevance to regulators and supervisors.
5.1 Surveillance
Given the vast number of messages that are processed every day by exchanges in electronic markets, it is
difficult to spot behaviour that intentionally or inadvertently may harm the integrity of markets. Either
way, the regulator must develop sophisticated tools to monitor the market and understand the impact of
individual algorithms on metrics of market quality.
Currently, financial regulators and supervisors use algorithms on transaction and order data to detect
practices that may harm the integrity of the market. For example, in electronic trading, the Dutch Authority
for the Financial Markets (AFM) relies on a combination of Suspicious Transaction and Order Reports from
market participants and custom made algorithms on transaction and order data to prevent and detect market
manipulation, see AFM (2021b). Similarly, the Financial Industry Regulatory Authority (FINRA) in the
US uses the SONAR system to monitor suspicious trading activity such as well-timed trades occurring just
before public announcements. These surveillance algorithms mine data to search for unusual patterns, some
of which could be designed to manipulate markets (e.g., pump and dump), to mislead the information in the
LOB (e.g., spoofing), or to trade with privileged information (e.g., front running, insider trading).
Most detection algorithms tend to look for “misleading” signals in the market.^38 To learn what is
misleading, one should first know which features agents take into account when making a trading decision.
As a substitute for misleading signals, supervisors often look for outliers or unusual trading behaviour. But
this ignores the fundamental issue at stake: is the unusual trading behaviour misleading market participants?
One should not mistake features that predict algorithmic trading behaviour for features that algorithms
use when making trading decisions. Anecdotally, trading firms often use a variety of non-market information
sources when making decisions, while our data is restricted to quantities which are based in the market (in
particular, which are observable to a regulator). This suggests that some of our conclusions will be affected
by confounding factors. For example, a firm with an off-market signal which encourages them to take a
directional position may often trade in a consistent direction – in this case, their intraday accumulated
inventory may be a good predictor of their trading behaviour in the near future, as it acts as a proxy for
their unobserved signal. This distinction is important when drawing causal conclusions from our results.
Our work has implications for both (i) data-driven detection of market manipulation, and (ii) supporting
claims about misleading behaviour in cases of market manipulation. With regards to (i), our findings lend
support to the claim that supervisors should look at “unusual” behaviour in the most important features
as listed in Section 4.4. Regarding (ii), supervisors have difficulties backing up why some trading behaviour
could be considered misleading. It is often difficult to show that some behaviour (e.g., creating an imbalance
in the LOB), causes a reaction in other agents. We believe our findings show how changes in the most
important features affect trading decisions, hence could be informative in cases of market manipulation.
More precisely, our models can be used to simulate the sequence of events that follow a case of potential
market manipulation. By comparing simulations including and excluding the potential manipulation, one
could estimate if and how market participants reacted to the potential manipulation, thereby quantifying
the effect of the potential manipulation.
(^38) See Article 12 “Market Manipulation” in EU (2014).

5.2 Testing of trading algorithms
Our results provide a unique starting point to build more sophisticated trading models than those used in the
extant literature. These models will be useful for market participants to build simulators to test strategies
and will help financial regulators to understand market dynamics, individual behaviour, and the impact of
trading algorithms and strategies on the integrity of markets.
Trading firms are required to test their trading algorithms to make sure they do not behave in an
unintended manner or contribute to disorderly trading conditions, see EU (2016c).^39 To do so, firms can use
their own testing environment or one provided by the trading venue. Trading venues are required to provide
members with simulation facilities which reproduce as realistically as possible the production (i.e., the real)
environment. The simulations should allow members to test a range of scenarios that they consider relevant
to their activity, and realistically reproduce disorderly trading conditions, see EU (2016b).
40
AFM (2021a) notes that there is room for improvement for both trading firms and trading venues in
testing trading algorithms. Current testing environments tend to range from one-off order books without
new orders entering the market over time, generated by the trading venue at the request of a trading firm,
to markets generating orders with a predetermined frequency and set parameters in real-time. Overall,
AFM (2021a) notes that trading venues have difficulty creating sufficiently realistic simulation facilities.
Furthermore, this report stresses that testing against disorderly trading conditions should be designed with
a view to addressing the reaction of the algorithm or strategy to conditions that may create a disorderly
market. Agent-based modelling is not currently being used widely in simulation facilities, nor agents trained
on real data. AFM (2021a) encourages trading venues to explore innovative ways to make their simulation
environments more realistic so that they are in line with the requirements.
This study is a starting point to build a simulator where market dynamics can be replicated at a granular
level to understand how each message to the order book may affect individual and therefore affect collective
dynamics. In particular, our statistical framework is devised to capture the fine microstructural facts that
drive the trading decisions of individual algorithms, which is key to understanding how trading algorithms
react to new market information and to messages to the LOB. With such a market simulator, firms will be
able to test their trading algorithms and regulators will be able to study the effect of new trading algorithms
on the quality of the market. Similarly, financial authorities will have a tool to build counterfactual trading
tapes to gain insights into market behaviour in the absence of certain trading algorithms or with potential
new entrants in the market.
5.3 Clustering
In terms of the clustering exercise, there are a number of relevant insights for supervisors. Irrespective of
the trading venue, a supervisor could impose its own criteria on what behaviour would be expected of any
Liquidity Provider, House or Client. Then, our clustering methodology can be used to highlight trading
firms whose algorithms might show behaviour contrary to what one would reasonable expect based on their
dealing capacity.
(^39) Article 5(4): The methodologies referred to in paragraph 1 shall ensure that the algorithmic trading system, trading

algorithm or algorithmic trading strategy:
(a) does not behave in an unintended manner;
(b) complies with the investment firm’s obligations under this Regulation;
(c) complies with the rules and systems of the trading venues accessed by the investment firm;
(d) does not contribute to disorderly trading conditions, continues to work effectively in stressed market conditions and, where
necessary under those conditions, allows for the switching off of the algorithmic trading system or trading algorithm.
(^40) Article 10(2)(a) Trading venues shall provide their members with access to a testing environment which shall consist of any

of the following:
(a) simulation facilities which reproduce as realistically as possible the production environment, including disorderly trading
conditions, and which provide the functionalities, protocols and structure that allow members to test a range of scenarios
that they consider relevant to their activity;
(b) testing symbols as defined and maintained by the trading venue.
6 Conclusions
We built models to predict how individual agents choose the direction, the price, and the volume of orders
they send to Euronext Amsterdam. These models achieved high and reliable out-of-sample accuracies and
were employed to cluster trading behaviour. We drew insights from the models that we fitted to data and
showed (i) the value of using member and algorithm identifications, (ii) the trading behaviour of the various
dealing capacities, and (iii) the feature importance in the prediction tasks. We discussed how regulators can
use the results of our models. We detailed the next steps to build agent-based market simulators that can
test counterfactuals, which may be used in cases of market manipulation.
Future work will address the limitations we discussed. In particular, we envisage the construction of
models that incorporate a large variety of features that capture events happening across markets and instru-
ments. Lastly, future work will explore other potential implications of the results we obtain. For example,
the consequences for market quality of a hypothetical update of members’ algorithms according to how the
remaining algorithms choose their direction, price, and volumes.
Acknowledgements
We thank the editor and two anonymous referees whose comments improved the contents and robustness of
the paper. We thank Steef Akerboom, Felix Flinterman, Ronald Verhoeven, Blanka Horvath, and Lukasz
Szpruch for helping to bring about the collaboration between the Alan Turing Institute and the Autoriteit
Financi ̈ele Markten. We are grateful to participants at the Alan Turing Institute Knowledge Share Day,
King’s College London financial mathematics internal seminar, Oxford-Man institute internal seminar, and
the SIAM Financial Mathematics and Engineering 2023 conference. We thank Micha Bender, Paul Besson,
Patrick Chang, Charles-Albert Lehalle, and Jose Penalva for comments.
Contributions
The authors contributed to the paper as follows: SC and RG initiated the collaboration;AC, SC, RG, and ́
LSB developed the modelling framework, with input from SL and LV; RG and LSB implemented the primary
modelling code, with input from SC, SL and LV;AC, SC, RG and LSB wrote the paper. ́
References
Abergel, F., Anane, M., Chakraborti, A., Jedidi, A., and Toke, I. M. (2016).Limit order books. Cambridge
University Press.
AFM (2021a). Algorithmic trading – governance and controls.https://www.afm.nl/en/sector/actueel/
2021/april/beheersing-controles-handelsalgoritmes. Accessed: 1/May/2023.
AFM (2021b). Prevention and detection of market abuse. https://www.afm.nl/en/sector/themas/
beurzen-en-effecten/afm-market-watch. Accessed: 18/April/2023.
AFM (2023). Machine learning in trading algorithms application by Dutch proprietary trading firms and pos-
sible risks.https://www.afm.nl/en/sector/actueel/2023/maart/her-machine-learning. Accessed:
7/March/2023.
A ̈ıt-Sahalia, Y., Fan, J., Xue, L., and Zhou, Y. (2022). How and when are high-frequency stock returns
predictable?Available at SSRN 4095405.
A ̈ıt-Sahalia, Y. and Saglam, M. (2013). High frequency traders: Taking advantage of speed. Technical
report, National Bureau of Economic Research.
Amihud, Y. and Mendelson, H. (1980). Dealership market: Market-making with inventory. Journal of
Financial Economics, 8(1):31–53.
Aquilina, M., Budish, E., and O’neill, P. (2022). Quantifying the high-frequency trading “arms race”.The
Quarterly Journal of Economics, 137(1):493–564.
Assefa, S. A., Dervovic, D., Mahfouz, M., Tillman, R. E., Reddy, P., and Veloso, M. (2020). Generating syn-
thetic data in finance: opportunities, challenges and pitfalls. InProceedings of the First ACM International
Conference on AI in Finance, pages 1–8.
Avellaneda, M. and Stoikov, S. (2008). High-frequency trading in a limit order book.Quantitative Finance,
8(3):217–224.
Bouchaud, J.-P., Gefen, Y., Potters, M., and Wyart, M. (2003). Fluctuations and response in financial
markets: the subtle nature ofrandom’price changes.Quantitative Finance, 4(2):176.
Bouchaud, J.-P., M ́ezard, M., and Potters, M. (2002). Statistical properties of stock order books: empirical
results and models.Quantitative Finance, 2(4):251.
Breiman, L. (2001). Random forests.Machine learning, 45(1):5–32.
Brogaard, J., Hendershott, T., and Riordan, R. (2014). High-frequency trading and price discovery. The
Review of Financial Studies, 27(8):2267–2306.
Brogaard, J., Hendershott, T., and Riordan, R. (2019). Price discovery without trading: Evidence from limit
orders.The Journal of Finance, 74(4):1621–1658.
Byrd, D., Hybinette, M., and Balch, T. H. (2019). ABIDES: Towards high-fidelity market simulation for AI
research.arXiv preprint arXiv:1904.12066.
Carrion, A. (2013). Very fast money: High-frequency trading on the NASDAQ.Journal of Financial Markets,
16(4):680–711.
Cartea,A., Donnelly, R., and Jaimungal, S. (2018). Enhancing trading strategies with order book signals. ́
Applied Mathematical Finance, 25(1):1–35.
Cartea,A. and Jaimungal, S. (2015). Optimal execution with limit and market orders. ́ Quantitative Finance,
15(8):1279–1291.
Cartea,A., Jaimungal, S., and Penalva, J. (2015). ́ Algorithmic and high-frequency trading. Cambridge
University Press.
Cartea,A., Jaimungal, S., and Wang, Y. (2020). Spoofing and price manipulation in order-driven markets. ́
Applied Mathematical Finance, 27(1-2):67–98.
Cartea,A., Payne, R., Penalva, J., and Tapia, M. (2019). Ultra-fast activity and intraday market quality. ́
Journal of Banking & Finance, 99:157–181.
Cartea,A. and Penalva, J. (2012). Where is the value in high frequency trading? ́ The Quarterly Journal of
Finance, 2(03):1250014.
Cartea,A. and S ́anchez-Betancourt, L. (2021). The shadow price of latency: Improving intraday fill ratios ́
in foreign exchange markets.SIAM Journal on Financial Mathematics, 12(1):254–294.
Cartea,A. and S ́anchez-Betancourt, L. (2023). Optimal execution with stochastic delay. ́ Finance and
Stochastics, 27(1):1–47.
Cohen, S. N., Snow, D., and Szpruch, L. (2021). Black-box model risk in finance. arXiv preprint
arXiv:2102.04757.
Cohen, S. N. and Szpruch, L. (2012). A limit order book model for latency arbitrage. Mathematics and
Financial Economics, 6:211–227.
Cont, R., Cucuringu, M., Glukhov, V., and Prenzel, F. (2023). Analysis and modeling of client order flow
in limit order markets.Quantitative Finance, pages 1–19.
Cont, R., Stoikov, S., and Talreja, R. (2010). A stochastic model for order book dynamics. Operations
Research, 58(3):549–563.
Dutta, C., Karpman, K., Basu, S., and Ravishanker, N. (2022). Review of statistical approaches for modeling
high-frequency trading data.Sankhya B, pages 1–48.
EU (2014). Market abuse regulation. Official Journal of the European Unionhttps://eur-lex.europa.
eu/legal-content/EN/TXT/PDF/?uri=CELEX:32014R0596&from=EN. Accessed: 9/March/2023.
EU (2016a). Annexes to the commission delegated regulation (eu) 600/2014. Official Jour-
nal of the European Union https://ec.europa.eu/finance/securities/docs/isd/mifid/rts/
160728-rts-22-annex_en.pdf. Accessed: 17/April/2024.
EU (2016b). Commission delegated regulation (eu) 2017/584. Official Journal of the European Union
https://eur-lex.europa.eu/legal-content/EN/TXT/PDF/?uri=CELEX:32017R0584&from=EN. Ac-
cessed: 1/May/2023.
EU (2016c). Commission delegated regulation (eu) 2017/589. Official Journal of the European Union
https://eur-lex.europa.eu/legal-content/EN/TXT/PDF/?uri=CELEX:32017R0589&from=EN. Ac-
cessed: 1/May/2023.
Euronext (2023a). AHOLD asset description. https://live.euronext.com/en/product/equities/
NL0011794037-XAMS. Accessed: 7/March/2023.
Euronext (2023b). ASML asset description. https://live.euronext.com/en/product/equities/
NL0010273215-XAMS. Accessed: 7/March/2023.
Euronext (2023c). ING asset description. https://live.euronext.com/en/product/equities/
NL0011821202-XAMS. Accessed: 7/March/2023.
Euronext (2023d). Rule book.https://www.euronext.com/en/media/1905. Accessed: 7/March/2023.
Euronext (2023e). TOMTOM asset description. https://live.euronext.com/en/product/equities/
NL0013332471-XAMS. Accessed: 7/March/2023.
Farmer, J. D. and Foley, D. (2009). The economy needs agent-based modelling.Nature, 460(7256):685–686.
Farmer, J. D., Patelli, P., and Zovko, I. I. (2005). The predictive power of zero intelligence in financial
markets.Proceedings of the National Academy of Sciences, 102(6):2254–2259.
Glosten, L. R. and Milgrom, P. R. (1985). Bid, ask and transaction prices in a specialist market with
heterogeneously informed traders.Journal of Financial Economics, 14(1):71–100.
Goldstein, M., Kwan, A., and Philip, R. (2023). High-frequency trading strategies. Management Science,
69(8):4413–4434.
Gould, M. D., Porter, M. A., Williams, S., McDonald, M., Fenn, D. J., and Howison, S. D. (2013). Limit
order books.Quantitative Finance, 13(11):1709–1742.
Grossman, S. J. and Miller, M. H. (1988). Liquidity and market structure.The Journal of Finance, 43(3):617–
633.
Gu ́eant, O. (2016). The Financial Mathematics of Market Liquidity: From optimal execution to market
making, volume 33. CRC Press.
Gu ́eant, O., Lehalle, C.-A., and Fernandez-Tapia, J. (2013). Dealing with the inventory risk: a solution to
the market making problem.Mathematics and Financial Economics, 7:477–507.
Hagstr ̈omer, B. and Nord ́en, L. (2013). The diversity of high-frequency traders.Journal of Financial Markets,
16(4):741–770.
Hagstr ̈omer, B., Nord ́en, L., and Zhang, D. (2014). How aggressive are high-frequency traders? Financial
Review, 49(2):395–419.
Hambly, B., Kalsi, J., and Newbury, J. (2020). Limit order books, diffusion approximations and reflected
spdes: from microscopic to macroscopic models.Applied Mathematical Finance, 27(1-2):132–170.
Hansch, O., Naik, N., and Viswanathan, S. (1998). Do inventories matter in dealership markets? evidence
from the london stock exchange.The Journal of Finance, 53(5):1623–1656.
Hasbrouck, J. (2018). High-frequency quoting: Short-term volatility in bids and offers.Journal of Financial
and Quantitative Analysis, 53(2):613–641.
Hendershott, T., Jones, C. M., and Menkveld, A. J. (2011). Does algorithmic trading improve liquidity?
The Journal of finance, 66(1):1–33.
Hendershott, T., Riordan, R., et al. (2009). Algorithmic trading and information. Manuscript, University
of California, Berkeley.
Ho, T. and Stoll, H. R. (1981). Optimal dealer pricing under transactions and return uncertainty.Journal
of Financial economics, 9(1):47–73.
Hoffmann, P. (2014). A dynamic limit order market with fast and slow traders. Journal of Financial
Economics, 113(1):156–169.
Jarnecic, E. and Snape, M. (2014). The provision of liquidity by high-frequency participants. Financial
Review, 49(2):371–394.
Kyle, A. S. (1985). Continuous auctions and insider trading. Econometrica: Journal of the Econometric
Society, pages 1315–1335.
Lehalle, C.-A., Gu ́eant, O., and Razafinimanana, J. (2011). High-frequency simulations of an order book: a
two-scale approach. Econophysics of Order-driven Markets: Proceedings of Econophys-Kolkata V, pages
73–92.
Libman, D., Haber, S., and Schaps, M. (2021). Forecasting quoted depth with the limit order book.Frontiers
in Artificial Intelligence, 4:667780.
Mankad, S., Michailidis, G., and Kirilenko, A. (2013). Discovering the ecosystem of an electronic financial
market with a dynamic machine-learning method.Algorithmic Finance, 2(2):151–165.
Megarbane, N., Saliba, P., Lehalle, C.-A., and Rosenbaum, M. (2017). The behavior of high-frequency
traders under different market stress scenarios.Market Microstructure and Liquidity, 3(03n04):1850005.
Menkveld, A. J. (2016). The economics of high-frequency trading: Taking stock.Annual Review of Financial
Economics, 8:1–24.
Murphy, K. P. (2022).Probabilistic machine learning: an introduction. MIT press.
O’Hara, M. (1998).Market Microstructure Theory. John Wiley & Sons.
Penalva, J. S. and Tapia, M. (2021). Heterogeneity and competition in fragmented markets: Fees vs speed.
Applied Mathematical Finance, 28(2):143–177.
Rigaki, M. and Garcia, S. (2020). A survey of privacy attacks in machine learning. arXiv preprint
arXiv:2007.07646.
Ro ̧su, I. (2009). A dynamic model of the limit order book.The Review of Financial Studies, 22(11):4601–
4641.
Ruan, R., Bacry, E., and Muzy, J.-F. (2023). Agent market orders representation through a contrastive
learning approach.arXiv preprint arXiv:2306.05987.
Shokri, R., Stronati, M., Song, C., and Shmatikov, V. (2017). Membership inference attacks against machine
learning models. In2017 IEEE symposium on security and privacy (SP), pages 3–18. IEEE.
Sirignano, J. and Cont, R. (2019). Universal features of price formation in financial markets: perspectives
from deep learning.Quantitative Finance, 19(9):1449–1459.
Tao, X., Day, A., Ling, L., and Drapeau, S. (2022). On detecting spoofing strategies in high-frequency
trading.Quantitative Finance, 22(8):1405–1425.
Van Kervel, V. and Menkveld, A. J. (2019). High-frequency trading around large institutional orders.The
Journal of Finance, 74(3):1091–1137.
Verousis, T., Perotti, P., and Sermpinis, G. (2018). One size fits all? high frequency trading, tick size
changes and the implications for exchanges: market quality and market structure considerations.Review
of Quantitative Finance and Accounting, 50:353–392.
Vyetrenko, S., Byrd, D., Petosa, N., Mahfouz, M., Dervovic, D., Veloso, M., and Balch, T. (2020). Get
real: Realism metrics for robust limit order book market simulations. InProceedings of the First ACM
International Conference on AI in Finance, pages 1–8.
Wang, X., Hoang, C., Vorobeychik, Y., and Wellman, M. P. (2021). Spoofing the limit order book: A
strategic agent-based analysis.Games, 12(2).
Williams, B. and Skrzypacz, A. (2020). Spoofing in equilibrium. Stanford University Graduate School of
Business Research Paper.
Wright, I. D., Reimherr, M., and Liechty, J. (2022). A machine learning approach to classification for traders
in financial markets.Stat, 11(1):e465.
Yao, C. and Ye, M. (2018). Why trading speed matters: A tale of queue rationing under price controls.The
Review of Financial Studies, 31(6):2157–2183.
A Description of shares
Similar to Table 5, we compute the order count, trade count, and volume traded of groups of algorithms
during the first four weeks of training data for ING, AHOLD, and TOMTOM. Table A1 displays the results
for ING, Table A2 for AHOLD, and Table A3 for TOMTOM.
order count order count trade count trade count volumee volume
percentage of all % percentage of all % percentage of all %
Top 5 2,326,236 42 190,042 24 970,242,121 19
Top 10 3,542,047 64 268,601 35 1,338,267,032 27
Top 20 4,495,680 81 328,265 42 1,675,327,497 34
Top 50 5,388,559 97 600,684 77 3,631,692,876 73
All 5,555,240 100 777,720 100 4,986,039,098 100
Table A1: Descriptive statistics for algorithms trading in ING between 11 October 2021 and 7 November
2022.
order count order count trade count trade count volumee volume
percentage of all % percentage of all % percentage of all %
Top 5 925,564 49 68,711 24 294,230,009 25
Top 10 1,250,004 66 97,494 34 378,566,543 33
Top 20 1,590,981 84 121,131 42 429,239,323 37
Top 50 1,837,989 97 228,241 80 918,281,638 79
All 1,891,709 100 285,440 100 1,163,250,600 100
Table A2: Descriptive statistics for algorithms trading in AHOLD between 11 October 2021 and 7 November
2022.
order count order count trade count trade count volumee volume
percentage of all % percentage of all % percentage of all %
Top 5 151,036 59 6,266 17 12,802,205 14
Top 10 193,241 75 9,173 25 24,460,828 28
Top 20 230,321 89 18,410 49 41,265,509 48
Top 50 253,284 98 32,989 88 75,158,861 87
All 257,902 100 37,284 100 86,580,402 100
Table A3: Descriptive statistics for algorithms trading in TOMTOM between 11 October 2021 and 7
November 2022.
B Feature specifications
In this section, we provide a detailed description of the variables we use in the study; these were introduced
in Section 3.1.
We denote time bytand let the trading horizon be [0,T] on any given day. At any timet∈[0,T], letS ̃t
be price of the last transaction that took place before timet; if the trade involved multiple orders,S ̃tis the
volume weighted average price of the transaction. LetSatbe the best ask price available in the LOB at time
tandVtathe aggregated volume at price levelSta. Similarly, letStbbe the best bid price available in the
LOB at timetandVtbthe aggregated volume at price levelStb. Thus, the spread at timetand the midprice
at timetare given by
St=S
a
t−S
b
t, St=
S
a
t+S
b
t
2
, (10)
respectively. For a given number of levelsn∈Ndenote byV
a,n
t the total volume available in the firstnlevels
of the LOB on the ask side; similarly, denote byV
b,n
t the total volume available in the firstnlevels of the
LOB on the bid side. Recall thatA={a 1 ,a 2 ,..., aM}are the identifiers for the algorithms on Euronext,
andB={b 1 ,b 2 ,..., bN}are the identifiers for the trading members of Euronext withN≤Mbecause a
trading member can have multiple trading algorithms. Forai∈ A(similar forbi∈ B) letV
a,n,−ai
t be the
volume available in the firstnlevels of the LOB on the ask side when weexcludeany volume posted by
algorithmai(similar definition forV
b,n,−ai
t ). Likewise, we useV
a,n,ai
t andV
b,n,ai
t for the volume posted by
algorithmai∈A. We define the imbalance between positive volumesV
a
andV
b
by
I(V
b
,V
a
) = log(1 +V
b
) −log(1 +V
a
),. (11)
For a time windowδ >0, we define the volatility of the transaction prices at timetof periodδby
V
δ
t=
s X
∆ logS ̃u̸=0 ;u∈[t−δ,t)
|∆ logS ̃u|^2 , (12)
where
∆ logS ̃u= logS ̃u−logS ̃u−, andS ̃u−= lim
v↗u
S ̃
v. (13)
The above definition extends to other stochastic processes in the paper, that isYu− = limv↗uYvfor any
cadlag stochastic processY. The return at timetof periodδis given by
R
δ
t= log

St−
St−δ

. (14)
The number of messages at timetand periodδis denoted byU
δ
tand records the number of messages in
the LOB during the window [t−δ,t). Here, a “message” is an order entry, an order deletion, or an order
amendment.
Lastly, for algorithmai∈ Aand the associated trading memberbj∈ B, we letQ
ai
t andQ
bj
t denote the
intraday accumulated inventory up to timetwith the assumption thatQ
ai
0 = 0 andQ
bj
0 = 0, and we letC
ai
t
andC
bj
t be the accumulated expense (in EUR) of purchases of inventory with the assumption thatC
ai
0 = 0
andC
bj
0 = 0. Note that forx∈ {ai,bj}, the variablesC
x
tandQ
x
t change in the same direction, that is, if
inventory goes up then cash goes up and vice-versa. Cash and inventory variables are computed for trading
in Euronext Amsterdam.
We are now able to describe the variables we use as features to describe the way algorithms make decisions
in the market. The set of variables that an algorithmai∈A(of trading memberbj∈B) has at its disposal
at timet∈[0,T] is given by:
(i) Available volumes in the LOB excluding trading algorithmai(six variables).
In particular, we use log(1+V) whereVis one of the following variables:V
x, 5 ,−ai
t− ,V
x, 10 ,−ai
t− −V
x, 5 ,−ai
t− ,
V
x, 20 ,−ai
t− −V
x, 10 ,−ai
t− , forx∈{a,b}.
(ii) Available volumes in the LOB by trading algorithmai(six variables).
In particular, we use log(1 +V) whereVis one of the following variables:V
x, 5 ,ai
t− ,V
x, 10 ,ai
t− −V
x, 5 ,ai
t− ,
V
x, 20 ,ai
t−
−V
x, 10 ,ai
t−
, forx∈{a,b}.
(iii) Imbalance in the LOB excluding trading algorithmai(three variables).
In particular, we use:I(V
b, 5 ,−ai
t−
,V
a, 5 ,−ai
t−
),I(V
b, 10 ,−ai
t−
−V
b, 5 ,−ai
t−
, V
a, 10 ,−ai
t−
−V
a, 5 ,−ai
t−
), andI(V
b, 20 ,−ai
t−
−
V
b, 10 ,−ai
t−
, V
a, 20 ,−ai
t−
−V
a, 10 ,−ai
t−
).
(iv) Imbalance in the LOB of trading algorithmai(three variables).
In particular, we use: I(V
b, 5 ,ai
t−
,V
a, 5 ,ai
t−
), I(V
b, 10 ,ai
t−
−V
b, 5 ,ai
t−
, V
a, 10 ,ai
t−
−V
a, 5 ,ai
t−
), andI(V
b, 20 ,ai
t−
−
V
b, 10 ,ai
t− , V
a, 20 ,ai
t− −V
a, 10 ,ai
t− ).
(v) Change of imbalance in the LOB excluding trading algorithmai(three variables).
In particular, we use:I(V
b, 5 ,−ai
t− ,V
a, 5 ,−ai
t− )−I(V
b, 5 ,−ai
s− ,V
a, 5 ,−ai
s− ),I(V
b, 10 ,−ai
t− −V
b, 5 ,−ai
t− , V
a, 10 ,−ai
t− −
V
a, 5 ,−ai
t− )−I(V
b, 10 ,−ai
s− −V
b, 5 ,−ai
s− , V
a, 10 ,−ai
s− −V
a, 5 ,−ai
s− ), andI(V
b, 20 ,−ai
t− −V
b, 10 ,−ai
t− , V
a, 20 ,−ai
t− −V
a, 10 ,−ai
t− )−
I(V
b, 20 ,−ai
s− −V
b, 10 ,−ai
s− , V
a, 20 ,−ai
s− −V
a, 10 ,−ai
s− ) wheres < tis the time of the last message beforetand
as usuals
−
is the time just befores.
(vi) Log volume of quantity at best bid and best offer (two variables).
In particular, we use log(1 +V) whereVis one of the following variables:V
x, 1
t− forx∈{a,b}.
(vii) Spread in basis points (one variable).
In particular, we useSt−/St−× 10 ,000.
(viii) Returns over a number of periods (four variables).
In particular, we use:R
1 s
t,R
5 s
t,R
60 s
t , andR
300 s
t.
(ix) Volatility over a number of periods (four variables).
In particular, we use:V
1 m
t ,V
5 m
t ,V
15 m
t , andV
60 m
t.
(x) Number of messages over number of periods (four variables).
In particular, we count the number of messages sent to the given instrument in Euronext in the last:
1 second, 100 milliseconds, 1 millisecond, 100 microseconds.
(xi) Aggressive buys minus aggressive sells over previous 1, 5, 60 and 360 seconds (four variables).
(xii) Aggressive buys over previous 1, 5, 60 and 360 seconds (four variables).
(xiii) Aggressive sells over previous 1, 5, 60 and 360 seconds (four variables).
(xiv) Volume of last transaction (one variable).
In particular, if the volume of the last transaction isV >0, we employ log(1 +V).
(xv) Inventory of trading algorithmQ
ai
t−(one variable).
In particular, we employ
sign

Q
ai
t−

×log

1 +
Q
ai
t−

. (15)
(xvi) Inventory of trading memberQ
bj
t−(one variable).
In particular, we employ
sign

Q
bj
t−

×log

1 +
Q
bj
t−

. (16)
(xvii) Cash of trading algorithmC
ai
t−
(one variable)
C
ai
t =
Zt
0
S ̃
udQ
ai
u, C
ai
0 = 0. (17)
In particular, we employ
sign

C
ai
t−

×log

1 +
C
ai
t−

. (18)
(xviii) Cash of trading memberC
bj
t−(one variable)
C
bj
t =
Zt
0
S ̃
udQ
bj
u, C
bj
0 = 0. (19)
In particular, we employ
sign

C
bj
t−

×log

1 +
C
bj
t−

. (20)
C Model fit results for other shares
C.1 Model performance
Similar to Table 6, Table 7, and Table 8, we show (i) the accuracies of the logistic regressions, (ii) the
outperformance of the logistic regressions over the most frequent bucket, and (iii) the outperformance of
random forests over the most frequent bucket and over the logistic regression; we do this for the shares ING,
AHOLD, and TOMTOM.
Table A4 shows the accuracies for ING, Table A5 shows the outperformance of the logistic regressions
over the most frequent bucket, and Table A6 shows the outperformance of random forests over the most
frequent bucket and over logistic regression. Here,Dthas two buckets, the variablePthas three buckets
andVthas nine buckets.
R1 R2 R3 R4 R5 R6 R7
Dt Pt Vt Pt|Dt= 1 Pt|Dt=− 1 Vt|Dt= 1 Vt|Dt=− 1
Top 5 64 ± 9 88 ± 9 55 ± 2 91 ± 6 90 ± 7 56 ± 2 56 ± 2
Top 10 65 ± 7 87 ± 12 64 ± 21 90 ± 10 90 ± 10 64 ± 21 64 ± 21
Top 20 71 ± 13 90 ± 12 63 ± 19 92 ± 11 92 ± 10 63 ± 19 63 ± 18
Top 50 75 ± 14 87 ± 15 55 ± 21 88 ± 15 89 ± 14 56 ± 21 55 ± 21
All 78 ± 15 78 ± 22 46 ± 23 75 ± 27 76 ± 28 43 ± 25 42 ± 25
order-weighted average 69 87 60 90 90 61 61
Table A4: Accuracies of the logistic regression models for ING, calculated over twelve train-and-deploy
exercises.
Dt Pt Vt Pt|Dt= 1 Pt|Dt=− 1 Vt|Dt= 1 Vt|Dt=− 1
Top 5 13 ± 8 1 ± 1 0 ± 8 3 ± 4 2 ± 4 0 ± 9 1 ± 9
Top 10 14 ± 7 3 ± 7 1 ± 6 6 ± 9 6 ± 9 1 ± 7 1 ± 7
Top 20 20 ± 13 3 ± 5 3 ± 7 5 ± 7 5 ± 7 3 ± 9 2 ± 7
Top 50 23 ± 13 4 ± 8 1 ± 6 5 ± 10 5 ± 9 2 ± 8 2 ± 7
All 25 ± 17 1 ± 12 − 3 ± 12 3 ± 11 3 ± 13 − 2 ± 13 − 1 ± 12
order-weighted average 18 3 0 5 5 1 1
Table A5: Outperformance over benchmark for ING, calculated over twelve train-and-deploy exercises.
outperformance
over benchmark over logistic
Dt Pt Vt Dt Pt Vt
Top 5 22 ± 8 0 ± 1 2 ± 9 9 ± 9 0 ± 1 2 ± 1
Top 10 20 ± 8 2 ± 4 2 ± 8 6 ± 8 − 1 ± 2 2 ± 3
Top 20 25 ± 12 2 ± 3 3 ± 9 4 ± 6 − 1 ± 2 0 ± 4
Top 50 26 ± 13 2 ± 6 2 ± 7 3 ± 6 − 1 ± 2 2 ± 4
All 25 ± 17 2 ± 7 1 ± 9 1 ± 8 1 ± 7 4 ± 8
order-weighted average 23 2 2 5 − 1 2
Table A6: Outperformance of random forests over benchmark and logistic regression for ING, calculated
over twelve train-and-deploy exercises.
Table A7 shows the accuracies for AHOLD, Table A8 shows the outperformance of the logistic regressions
over the most frequent bucket, and Table A9 shows the outperformance of random forests over the most
frequent bucket and over logistic regression. Here,Dthas two buckets, the variablePthas three buckets
andVthas nine buckets.
R1 R2 R3 R4 R5 R6 R7
Dt Pt Vt Pt|Dt= 1 Pt|Dt=− 1 Vt|Dt= 1 Vt|Dt=− 1
Top 5 63 ± 7 84 ± 8 58 ± 14 86 ± 6 87 ± 6 58 ± 13 58 ± 14
Top 10 66 ± 9 87 ± 10 66 ± 21 89 ± 9 89 ± 9 66 ± 21 66 ± 21
Top 20 71 ± 13 89 ± 11 67 ± 23 90 ± 10 91 ± 10 67 ± 23 66 ± 23
Top 50 74 ± 14 84 ± 16 53 ± 23 85 ± 15 84 ± 17 55 ± 22 53 ± 23
All 78 ± 16 77 ± 21 42 ± 24 73 ± 28 73 ± 28 39 ± 27 40 ± 25
order-weighted average 69 86 59 87 88 60 59
Table A7: Accuracies of the logistic regression models for AHOLD, calculated over twelve train-and-deploy
exercises.
Dt Pt Vt Pt|Dt= 1 Pt|Dt=− 1 Vt|Dt= 1 Vt|Dt=− 1
Top 5 13 ± 7 7 ± 9 5 ± 6 9 ± 10 9 ± 10 6 ± 7 5 ± 7
Top 10 16 ± 7 5 ± 7 4 ± 8 6 ± 8 7 ± 8 4 ± 9 4 ± 9
Top 20 20 ± 14 3 ± 6 4 ± 7 5 ± 7 5 ± 7 5 ± 8 3 ± 8
Top 50 22 ± 14 3 ± 7 2 ± 6 5 ± 8 3 ± 11 2 ± 8 3 ± 8
All 22 ± 21 0 ± 12 − 2 ± 12 3 ± 13 4 ± 12 − 1 ± 11 − 1 ± 11
order-weighted average 18 4 4 6 6 5 4
Table A8: Outperformance over benchmark for AHOLD, calculated over twelve train-and-deploy exercises.
outperformance
over benchmark over logistic
Dt Pt Vt Dt Pt Vt
Top 5 20 ± 6 5 ± 7 3 ± 9 7 ± 8 − 2 ± 3 − 2 ± 4
Top 10 21 ± 7 3 ± 5 2 ± 9 5 ± 6 − 2 ± 3 − 2 ± 3
Top 20 25 ± 13 2 ± 4 4 ± 9 5 ± 5 − 1 ± 2 0 ± 5
Top 50 25 ± 14 2 ± 5 3 ± 7 3 ± 5 − 1 ± 3 1 ± 3
All 22 ± 19 2 ± 7 2 ± 10 1 ± 11 2 ± 8 3 ± 7
order-weighted average 23 3 4 5 − 1 0
Table A9: Outperformance of random forests over benchmark and logistic regression for AHOLD, calculated
over twelve train-and-deploy exercises.
Table A10 shows the accuracies for TOMTOM, Table A11 shows the outperformance of the logistic
regressions over the most frequent bucket, and Table A12 shows the outperformance of random forests over
the most frequent bucket and over logistic regression. Here,Dthas two buckets, the variablePthas three
buckets andVthas ten buckets.
R1 R2 R3 R4 R5 R6 R7
Dt Pt Vt Pt|Dt= 1 Pt|Dt=− 1 Vt|Dt= 1 Vt|Dt=− 1
Top 5 68 ± 15 88 ± 15 57 ± 27 89 ± 15 89 ± 15 58 ± 27 58 ± 26
Top 10 66 ± 13 90 ± 13 61 ± 23 90 ± 13 91 ± 12 61 ± 23 62 ± 23
Top 20 70 ± 15 88 ± 13 62 ± 26 89 ± 12 89 ± 12 62 ± 25 62 ± 26
Top 50 73 ± 13 78 ± 19 51 ± 24 79 ± 18 77 ± 20 50 ± 24 50 ± 24
All 71 ± 24 70 ± 23 44 ± 28 54 ± 38 54 ± 39 31 ± 30 31 ± 30
order-weighted average 67 87 52 88 88 52 52
Table A10: Accuracies of the logistic regression models for TOMTOM, calculated over twelve train-and-
deploy exercises.
Dt Pt Vt Pt|Dt= 1 Pt|Dt=− 1 Vt|Dt= 1 Vt|Dt=− 1
Top 5 16 ± 15 2 ± 4 4 ± 9 3 ± 5 3 ± 4 5 ± 9 5 ± 9
Top 10 14 ± 13 1 ± 3 3 ± 7 2 ± 4 2 ± 3 2 ± 8 2 ± 7
Top 20 17 ± 16 0 ± 3 1 ± 6 1 ± 4 2 ± 3 1 ± 7 1 ± 6
Top 50 19 ± 13 1 ± 5 − 2 ± 7 2 ± 7 1 ± 5 − 3 ± 8 − 2 ± 9
All 13 ± 19 − 2 ± 15 − 6 ± 15 − 2 ± 16 − 3 ± 16 − 6 ± 17 − 6 ± 20
order-weighted average 15 0 3 2 2 3 3
Table A11: Outperformance over benchmark for TOMTOM, calculated over twelve train-and-deploy exer-
cises.
outperformance
over benchmark over logistic
Dt Pt Vt Dt Pt Vt
Top 5 23 ± 16 2 ± 3 7 ± 10 8 ± 9 − 1 ± 1 2 ± 2
Top 10 19 ± 15 1 ± 2 4 ± 8 5 ± 7 0 ± 1 1 ± 2
Top 20 22 ± 15 0 ± 2 2 ± 6 5 ± 9 0 ± 1 1 ± 3
Top 50 22 ± 14 1 ± 4 0 ± 6 3 ± 7 0 ± 3 3 ± 3
All 13 ± 19 0 ± 7 0 ± 8 0 ± 13 2 ± 13 6 ± 13
order-weighted average 24 0 5 9 0 2
Table A12: Outperformance of random forests over benchmark and logistic regression for TOMTOM, cal-
culated over twelve train-and-deploy exercises.
C.2 Feature importance
Below, we carry out a robustness check where we compute the feature importance with the PCA-transformed
features instead of using the original features. Figure A1 shows the importance of the principal components
(PCs) when predicting the directionDtin ASML.
0 0. 05 0. 1 0. 15 0. 2 0. 25
PC21
PC4
PC9
PC10
PC27
PC1
PC20
PC12
PC7
PC8
Figure A1: Importance of principal components to explain the directionDtof an order for ASML. We
use permutation importance and logistic regressions, and only show the top ten most important principal
components.
Next, in Figure A2 we look at the original features that have a higher influence in PC number 8.
0 0. 1 0. 2 0. 3 0. 4 0. 5
agg buy last 1s
ask volume of algo 6-10
net agg buy-sell last 1s
ask volume of algo 0-5
bid volume of algo 0-5
bid volume of algo 6-10
chg imbalance excl algo 6-10
imbalance of algo 11-20
imbalance of algo 0-5
imbalance of algo 6-10
Absolute weighting original features in principal component 8
Original features
Figure A2: Absolute weighting original features in principal component for ASML. We only show the top
10 original features with the highest absolute weighting.
The results are consistent with the previous finding that pointed at the imbalance of algorithms being
the most important to predict direction.
C.2.1 Feature importance: ING, AHOLD, and TOMTOM
We present the permutation feature importance using the logistic regressions. Similar to Figure 5 – where
we show the most important features for predictingDt,P, andV— Figure A3 reports the analogous for
ING, Figure A4 reports the analogous for AHOLD, and Figure A5 reports the analogous for TOMTOM.
Unlike the case for ASML, inventory or cash related features are the most important features when
predictingDtfor ING, AHOLD, and TOMTOM. Spread retains its place as most important feature when
predictingP, while inventory of algorithm and cash of algorithm are also important features for ING, and
AHOLD. Consistently across ING, AHOLD, and TOMTOM, the four most important features to predictV
are either inventory variables (inventory of algorithm and inventory of member) or cash variables (cash of
algoirthm and cash of member).
0 0. 1 0. 2
ask volume of algo 0-5,
ask volume excl algo 0-5
best ask volume
best bid volume
bid volume of algo 0-5
bid volume excl algo 0-5
imbalance of algo 0-5
chg imbalance excl algo 0-5
inventory of algo
inventory of member
cash of algorithm
cash of member
num messages 0.1ms
num messages 1ms
quad var 5m
spread
Direction
0 0. 2 0. 4
Price|buy
0 0. 2 0. 4
Price|sell
0 0. 1 0. 2
Volume|buy
0 0. 1 0. 2
Volume|sell
Figure A3: Importance of features to explain the directionDtof an order, the price bucketPtof an order
conditional on the directionDtof the order, and the volume bucketVtof an order conditional on the
directionDtof the order for ING. We use permutation importance and logistic regressions, and only show
the features that are in the top ten most important features for at least one target variable.
0 0. 1 0. 2
ask volume of algo 0-5
ask volume excl algo 0-5
best ask volume
best bid volume
bid volume of algo 0-5
bid volume excl algo 0-5
imbalance of algo 0-5
chg imbalance excl algo 0-5
inventory of algo
inventory of member
cash of algorithm
cash of member
num messages 0.1ms
num messages 1ms
quad var 5m
spread
Direction
0 0. 2 0. 4
Price|buy
0 0. 2 0. 4
Price|sell
0 0. 1 0. 2
Volume|buy
0 0. 1 0. 2
Volume|sell
Figure A4: Importance of features to explain the directionDtof an order, the price bucketPtof an order
conditional on the directionDtof the order, and the volume bucketVtof an order conditional on the
directionDtof the order for AHOLD. We use permutation importance and logistic regressions, and only
show the features that are in the top ten most important features for at least one target variable.
0 0. 1 0. 2
ask volume of algo 0-5
ask volume excl algo 0-5
best ask volume
best bid volume
bid volume of algo 0-5
bid volume excl algo 0-5
imbalance of algo 0-5
chg imbalance excl algo 0-5
inventory of algo
inventory of member
cash of algorithm
cash of member
num messages 0.1ms
num messages 1ms
quad var 5m
spread
Direction
0 0. 2 0. 4
Price|buy
0 0. 2 0. 4
Price|sell
0 0. 1 0. 2
Volume|buy
0 0. 1 0. 2
Volume|sell
Figure A5: Importance of features to explain the directionDtof an order, the price bucketPtof an order
conditional on the directionDtof the order, and the volume bucketVtof an order conditional on the
directionDtof the order for TOMTOM. We use permutation importance and logistic regressions, and only
show the features that are in the top ten most important features for at least one target variable.
C.3 Clustering of agents
This section presents the clustering results for the shares ING, AHOLD and TOMTOM according to the
methodology explained in Subsection 4.5. First, we show the size of the clusters we obtain in each of the
twelve clustering exercises. As before, for each of the clustering exercises, the first cluster has the most
algorithms, and the third cluster has the least number of algorithms.
Figure A6: Clusters of trading behaviour for ING for the twelve clustering exercises. The first column
represents the clustering exercise on weeks 41-44 of 2021, the second on weeks 42-45, etc. Cluster 1 is at the
top (blue), cluster 2 is in the middle (orange), and cluster 3 is at the bottom (green). The size of the bars
corresponds to the number of algorithms in the clusters — they add up to 96 algorithms in each of the twelve
clustering exercises. The grey areas connecting consecutive clustering exercises represent the transition of
algorithms from a cluster in one clustering exercise to a cluster in the next clustering exercise.
Figure A7: Clusters of trading behaviour for AHOLD for the twelve clustering exercises. The first column
represents the clustering exercise on weeks 41-44 of 2021, the second on weeks 42-45, etc. Cluster 1 is at the
top (blue), cluster 2 is in the middle (orange), and cluster 3 is at the bottom (green). The size of the bars
corresponds to the number of algorithms in the clusters — they add up to 91 algorithms in each of the twelve
clustering exercises. The grey areas connecting consecutive clustering exercises represent the transition of
algorithms from a cluster in one clustering exercise to a cluster in the next clustering exercise.
Figure A8: Clusters of trading behaviour for TOMTOM for the twelve clustering exercises. The first column
represents the clustering exercise on weeks 41-44 of 2021, the second on weeks 42-45, etc. Cluster 1 is at the
top (blue), cluster 2 is in the middle (orange), and cluster 3 is at the bottom (green). The size of the bars
corresponds to the number of algorithms in the clusters — they add up to 76 algorithms in each of the twelve
clustering exercises. The grey areas connecting consecutive clustering exercises represent the transition of
algorithms from a cluster in one clustering exercise to a cluster in the next clustering exercise.
Second, we show the stability of clusters through time using the technique described in Section 4.5.1 to
create Figure 7.
45 46 47 48 49 50 51 52
1 2 3
0. 5
0. 6
0. 7
0. 8
0. 9
1
week
Figure A9: Stability of clusters for ING across the twelve train exercises.
45 46 47 48 49 50 51 52
1 2 3
0. 5
0. 6
0. 7
0. 8
0. 9
1
week
Figure A10: Stability of clusters for AHOLD across the twelve train exercises.
45 46 47 48 49 50 51 52
1 2 3
0. 5
0. 6
0. 7
0. 8
0. 9
1
week
Figure A11: Stability of clusters for TOMTOM across the twelve train exercises.
Figure A10 and A11 show that the stability of the clusters we obtain for AHOLD and TOMTOM is not
good. In particular, we see that random reshuffling of algorithms produces a similar stability score. Therefore
we do not interpret the clustering results of these two shares. We believe the poor clustering might be due
to the clusters on the first four weeks of data having substantially different sizes than the clusters obtained
on other months of data of the same share (see Figures A7 and A8). If we were to take a clustering on the
second month of data, for example, with clusters whose size is more representative of other months, then
the stability metric would improve.
Cluster 1 Cluster 2 Cluster 3
Client
House
LP
14 4 5
13 6 2
27 14 11
Figure A12: Confusion matrix between dealing capacity and clusters obtained on the first four weeks of data
for ING.
Figure A12 shows an “L shape” similar to that in Figure 8. That is: the majority of algorithms are in
cluster 1, with types Liquidity Provider, House and Client all well represented. In cluster 2 we again observe
mostly algorithms with type Liquidity Provider, and the same goes for cluster 3.
Cluster 1 Cluster 2 Cluster 3
Client
House
LP
9 9 5
7 8 4
19 12 18
Figure A13: Confusion matrix between dealing capacity and clusters obtained on the first four weeks of data
for AHOLD.
Cluster 1 Cluster 2 Cluster 3
Client
House
LP
23 3 3
14 0 1
20 9 3
Figure A14: Confusion matrix between dealing capacity and clusters obtained on the first four weeks of data
for TOMTOM.
C.4 Direction: average behaviour
Here we report the average regression coefficients for ING, AHOLD, and TOMTOM using the same tech-
niques as those described in Subsection 4.5.6.
Cluster 1 Cluster 2 Cluster 3
imbalance of algo 0-5 1.25 −1.43 0.26
imbalance of algo 6-10 0.26 −0.55 0.98
cash of algorithm 0.58 0.25 −0.38
inventory of algo 0.58 0.25 −0.38
volume of algo 11-20 0.30 −0.11 0.44
best bid volume 0.41 −0.16 0.23
best ask volume −0.35 0.17 −0.24
imbalance of algo 11-20 0.01 −0.47 −0.11
volume of algo 6-10 0.08 −0.14 0.24
imbalance excl algo 0-5 −0.10 −0.00 −0.22
inventory of member 0.09 −0.09 0.15
cash of member 0.09 −0.08 0.15
num messages 0.1ms −0.02 −0.12 −0.10
num messages 1ms −0.02 −0.10 −0.08
return 1s 0.07 0.00 0.12
volume excl algo 0-5 0.10 0.01 −0.08
net agg buy-sell last 1s 0.03 −0.04 0.11
quad var 60m −0.11 −0.01 0.02
Table A13: Average regression coefficients per cluster on first four weeks of training data for ING when
predicting directionDt. All excluded features have average coefficients with a magnitude smaller than 0.1
for all clusters.
There are similarities between ASML and ING. For imbalance of the algorithm on the first five levels
we observe a strong positive coefficient for cluster 1, a strong negative coefficient for cluster 2, and we see
cluster 3 lie between the two. These results are consistent with what we observe in ASML. For inventory
of algorithm we observe a strong positive coefficient for cluster 1, a strong negative coefficient for cluster 3,
and cluster 2 is somewhere in between – again consistent with the clustering obtained for ASML, including
the signs of the coefficients. Lastly, we see that imbalances of the algorithm near the top of the LOB have
the largest positive (resp. negative) coefficients for both ASML and ING.
Cluster 1 Cluster 2 Cluster 3
imbalance of algo 0-5 1.58 −0.24 −0.12
cash of algorithm 0.50 0.43 0.56
inventory of algo 0.50 0.43 0.55
best bid volume 0.18 −0.08 0.81
best ask volume −0.16 0.11 −0.76
imbalance of algo 11-20 −0.04 0.02 −0.93
imbalance of algo 6-10 0.12 −0.16 −0.59
volume of algo 11-20 −0.33 0.26 −0.18
volume of algo 0-5 0.30 −0.08 0.24
cash of member 0.03 0.31 −0.02
inventory of member 0.03 0.31 −0.02
quad var 60m −0.08 0.18 −0.01
imbalance excl algo 11-20 0.09 0.12 −0.06
chg imbalance excl algo 0-5 −0.01 −0.08 0.17
return 1s 0.10 0.01 0.15
num messages 0.1ms 0.03 −0.19 −0.03
num messages 1ms −0.03 −0.17 −0.02
chg imbalance excl algo 11-20 0.08 0.02 0.11
imbalance excl algo 6-10 −0.00 0.06 −0.13
return 5s 0.05 0.03 0.11
volume excl algo 11-20 −0.02 0.14 −0.02
return 300s 0.02 0.03 −0.10
net agg buy-sell last 1s 0.01 0.03 0.10
imbalance excl algo 0-5 0.03 −0.00 −0.11
agg sell last 1s −0.01 −0.02 −0.10
quad var 15m 0.01 0.10 −0.00
Table A14: Average regression coefficients per cluster on first four weeks of training data for AHOLD when
predicting directionDt. All excluded features have average coefficients with a magnitude smaller than 0.1
for all clusters.
Cluster 1 Cluster 2 Cluster 3
imbalance of algo 0-5 0.58 −1.11 0.42
imbalance of algo 6-10 −0.05 −0.76 −0.13
cash of algorithm 0.44 0.11 −0.39
inventory of algo 0.43 0.10 −0.39
volume of algo 6-10 0.05 −0.26 −0.16
num messages 1ms −0.02 0.04 −0.36
imbalance of algo 11-20 −0.18 −0.06 −0.18
best bid volume 0.30 −0.06 0.03
num messages 0.1ms −0.03 0.05 −0.30
best ask volume −0.24 0.01 −0.12
return 1s 0.15 0.08 0.14
return 5s 0.13 0.09 0.12
agg buy last 5s 0.02 0.07 0.25
return 300s 0.10 0.05 −0.16
volume of algo 0-5 0.08 −0.21 −0.02
imbalance excl algo 11-20 0.19 0.05 −0.07
num messages 0.1s 0.03 −0.02 −0.26
quad var 5m 0.05 −0.04 −0.20
chg imbalance excl algo 6-10 −0.02 0.15 −0.11
agg buy last 1s 0.04 0.06 0.16
imbalance excl algo 6-10 0.04 0.09 −0.12
agg sell last 5s −0.06 −0.02 0.17
inventory of member 0.04 −0.03 0.18
spread −0.09 0.03 0.12
cash of member 0.04 −0.03 0.18
volume excl algo 6-10 0.06 0.03 −0.13
volume of algo 11-20 −0.05 0.08 0.10
volume excl algo 11-20 0.15 0.00 −0.05
num messages 1s 0.01 −0.03 0.13
imbalance excl algo 0-5 −0.01 0.02 −0.12
Table A15: Average regression coefficients per cluster on first four weeks of training data for TOMTOM
when predicting directionDt. All excluded features have average coefficients with a magnitude smaller than
0.1 for all clusters.
C.5 Price limit: average behaviour
Here we report the results of Subsection 4.5.7 for ING, AHOLD, and TOMTOM. We do not comment on
the coefficients of the tables below; the insights are similar to those discussed for ASML.
Dt= 1 (Buy) Dt=−1 (Sell)
Cluster 1 Cluster 2 Cluster 3 Cluster 1 Cluster 2 Cluster 3
intercept −0.09 −0.64 −0.20 −0.04 −0.64 −0.28
best bid volume 0.42 0.69 0.32 −0.22 −0.12 −0.16
best ask volume −0.24 −0.12 −0.16 0.38 0.66 0.30
volume of algo 0-5 −0.26 0.20 −0.28 −0.26 0.28 −0.33
volume of algo 6-10 −0.20 0.19 −0.33 −0.22 0.25 −0.35
spread 0.11 0.39 0.12 0.13 0.38 0.15
imbalance of algo 0-5 0.05 0.43 0.01 −0.03 −0.58 0.09
Pt= 1 num messages 1ms 0.19 0.12 0.09 0.16 0.18 0.12
volume of algo 11-20 −0.00 0.07 −0.28 −0.06 0.05 −0.27
num messages 0.1ms 0.16 0.10 0.06 0.13 0.17 0.09
num messages 0.1s 0.05 0.12 0.07 0.06 0.12 0.01
imbalance of algo 6-10 −0.06 0.05 −0.02 0.05 −0.17 0.04
imbalance excl algo 6-10 −0.03 0.01 0.02 0.07 0.10 0.05
inventory of member 0.03 0.01 0.10 −0.05 0.01 0.08
cash of member 0.03 0.01 0.10 −0.05 0.01 0.08
Dt= 1 (Buy) Dt=−1 (Sell)
Cluster 1 Cluster 2 Cluster 3 Cluster 1 Cluster 2 Cluster 3
intercept −0.07 0.08 0.09 −0.08 0.15 −0.02
volume of algo 0-5 0.17 0.26 0.27 0.25 0.27 0.34
volume of algo 11-20 −0.14 −0.23 −0.13 −0.21 −0.30 −0.10
imbalance of algo 0-5 0.01 0.53 0.02 0.02 −0.32 −0.03
volume of algo 6-10 0.05 0.10 0.12 0.09 0.06 0.19
Pt= 2 spread 0.03 −0.16 0.08 0.05 −0.17 0.06
best ask volume 0.06 −0.02 0.11 0.04 −0.19 0.10
num messages 0.1s −0.07 −0.08 −0.08 −0.07 −0.10 −0.08
imbalance of algo 6-10 0.00 0.24 0.00 0.02 −0.11 −0.00
best bid volume −0.01 −0.17 0.03 0.05 0.02 0.07
num messages 1ms −0.10 −0.04 −0.04 −0.10 −0.02 −0.03
Dt= 1 (Buy) Dt=−1 (Sell)
Cluster 1 Cluster 2 Cluster 3 Cluster 1 Cluster 2 Cluster 3
intercept 0.16 0.56 0.11 0.12 0.49 0.30
imbalance of algo 0-5 −0.06 −0.96 −0.03 0.01 0.90 −0.06
best ask volume 0.18 0.14 0.05 −0.41 −0.47 −0.40
best bid volume −0.41 −0.52 −0.35 0.17 0.09 0.09
volume of algo 11-20 0.14 0.15 0.41 0.27 0.24 0.38
Pt= 3 volume of algo 6-10 0.14 −0.29 0.21 0.13 −0.31 0.16
spread −0.14 −0.23 −0.20 −0.17 −0.21 −0.22
volume of algo 0-5 0.09 −0.46 0.02 0.00 −0.55 −0.01
imbalance of algo 6-10 0.06 −0.28 0.02 −0.07 0.28 −0.03
num messages 0.1ms −0.08 −0.12 −0.07 −0.08 −0.21 −0.11
num messages 1ms −0.08 −0.08 −0.06 −0.07 −0.16 −0.08
imbalance of algo 11-20 0.06 −0.03 −0.04 −0.01 0.14 −0.04
Table A16: Average regression coefficients for price bucket describing eager-to-trade orders (Pt= 1), at-the-
touch orders (Pt= 2), and orders deeper in the LOB (Pt= 3) per cluster, on first four weeks of training
data for ING, conditioning on the direction of orderDt.
Dt= 1 (Buy) Dt=−1 (Sell)
Cluster 1 Cluster 2 Cluster 3 Cluster 1 Cluster 2 Cluster 3
intercept −0.30 −0.59 0.11 −0.41 −0.47 0.12
best bid volume 0.45 0.76 0.40 −0.19 −0.08 −0.34
best ask volume −0.20 −0.10 −0.30 0.42 0.62 0.46
spread 0.20 0.51 0.17 0.16 0.43 0.18
volume of algo 11-20 −0.11 −0.08 −0.38 −0.14 −0.07 −0.39
volume of algo 6-10 −0.01 −0.08 −0.50 0.00 0.01 −0.49
Pt= 1 volume of algo 0-5 0.07 −0.02 −0.38 0.10 0.07 −0.39
imbalance of algo 0-5 −0.06 0.20 0.26 0.09 −0.11 −0.21
num messages 1ms 0.14 0.15 0.03 0.08 0.06 0.06
volume excl algo 0-5 −0.10 −0.08 −0.07 −0.02 −0.07 −0.06
num messages 0.1ms 0.12 0.12 0.01 0.08 0.06 0.04
imbalance excl algo 11-20 −0.11 −0.06 −0.08 −0.03 0.05 0.06
Dt= 1 (Buy) Dt=−1 (Sell)
Cluster 1 Cluster 2 Cluster 3 Cluster 1 Cluster 2 Cluster 3
intercept −0.22 0.10 0.11 −0.24 0.29 0.08
volume of algo 0-5 −0.03 0.18 0.33 −0.06 0.12 0.31
volume of algo 11-20 0.02 −0.09 −0.24 0.07 −0.11 −0.18
best bid volume −0.03 −0.26 −0.02 0.08 0.03 0.11
num messages 0.1s −0.10 −0.19 0.01 −0.07 −0.15 0.01
spread 0.00 −0.23 −0.01 0.05 −0.20 −0.03
Pt= 2 best ask volume 0.07 0.04 0.12 0.02 −0.23 −0.04
volume excl algo 0-5 0.10 0.15 0.02 0.09 0.09 0.03
volume of algo 6-10 −0.03 0.08 0.13 −0.03 0.02 0.14
num messages 1ms −0.13 −0.14 0.03 −0.06 −0.06 0.01
imbalance of algo 0-5 0.05 0.11 −0.07 −0.04 −0.09 0.02
num messages 0.1ms −0.11 −0.07 0.05 −0.04 −0.00 0.03
Dt= 1 (Buy) Dt=−1 (Sell)
Cluster 1 Cluster 2 Cluster 3 Cluster 1 Cluster 2 Cluster 3
intercept 0.52 0.50 −0.21 0.65 0.19 −0.20
volume of algo 11-20 0.08 0.17 0.62 0.08 0.19 0.56
best bid volume −0.42 −0.49 −0.37 0.11 0.05 0.22
best ask volume 0.13 0.06 0.18 −0.43 −0.40 −0.42
spread −0.21 −0.28 −0.16 −0.21 −0.23 −0.15
Pt= 3 imbalance of algo 0-5 0.01 −0.31 −0.18 −0.06 0.20 0.19
volume of algo 6-10 0.04 −0.00 0.38 0.03 −0.04 0.35
volume of algo 0-5 −0.04 −0.16 0.05 −0.03 −0.19 0.08
imbalance of algo 6-10 0.15 −0.10 −0.12 −0.03 0.03 0.02
imbalance excl algo 11-20 0.14 0.08 0.11 0.01 −0.05 −0.06
num messages 0.1s 0.01 0.12 −0.01 0.01 0.11 −0.02
Table A17: Average regression coefficients for price bucket describing eager-to-trade orders (Pt= 1), at-the-
touch orders (Pt= 2), and orders deeper in the LOB (Pt= 3) per cluster, on first four weeks of training
data for AHOLD, conditioning on the direction of orderDt.
Dt= 1 (Buy) Dt=−1 (Sell)
Cluster 1 Cluster 2 Cluster 3 Cluster 1 Cluster 2 Cluster 3
intercept −0.02 −0.71 −0.28 −0.04 −0.76 −0.31
volume of algo 0-5 −0.40 0.01 −0.55 −0.43 −0.15 −0.44
best bid volume 0.18 0.63 0.06 −0.15 −0.19 −0.07
best ask volume −0.16 −0.15 −0.09 0.11 0.52 0.03
num messages 1ms 0.14 0.19 0.15 0.12 0.04 0.14
imbalance of algo 0-5 −0.01 0.21 0.04 0.08 −0.30 0.01
volume excl algo 0-5 0.10 0.08 0.20 0.06 0.06 0.16
num messages 0.1ms 0.11 0.20 0.11 0.09 0.02 0.12
Pt= 1 spread −0.03 0.22 0.01 0.02 0.18 −0.00
num messages 0.1s 0.12 0.07 0.06 0.05 0.11 0.01
imbalance excl algo 6-10 −0.02 −0.02 0.18 0.04 0.05 0.11
volume of algo 6-10 −0.11 −0.02 −0.06 −0.07 0.09 −0.04
imbalance excl algo 11-20 −0.00 −0.04 −0.11 0.01 0.06 −0.12
imbalance of algo 11-20 0.04 0.12 −0.01 0.03 −0.10 0.03
last volume transacted 0.05 −0.01 −0.11 0.04 0.01 −0.07
volume of algo 11-20 −0.04 −0.03 0.07 0.00 0.10 0.05
imbalance of algo 6-10 −0.00 0.05 0.01 0.09 −0.10 −0.00
num messages 1s 0.02 −0.00 −0.04 −0.01 0.10 −0.07
Dt= 1 (Buy) Dt=−1 (Sell)
Cluster 1 Cluster 2 Cluster 3 Cluster 1 Cluster 2 Cluster 3
intercept 0.08 −0.36 −0.06 −0.02 −0.25 −0.03
volume of algo 0-5 0.13 −0.01 0.13 0.22 0.04 0.18
best bid volume −0.06 −0.26 −0.02 0.09 0.13 0.06
volume of algo 11-20 −0.14 0.09 −0.13 −0.10 −0.01 −0.14
best ask volume 0.11 0.12 0.03 0.01 −0.24 0.04
num messages 0.1s −0.05 −0.08 0.06 −0.06 −0.14 0.12
imbalance excl algo 0-5 0.04 0.06 −0.10 0.00 −0.08 −0.12
volume of algo 6-10 −0.04 −0.08 −0.07 −0.02 −0.13 0.06
imbalance excl algo 11-20 0.02 0.05 0.23 −0.02 −0.05 −0.01
Pt= 2 volume excl algo 0-5 −0.00 −0.03 −0.15 0.01 0.08 −0.09
num messages 1s −0.01 −0.06 0.04 −0.02 −0.13 0.10
volume excl algo 11-20 −0.05 0.07 0.15 −0.04 0.01 −0.04
last volume transacted −0.05 0.08 0.11 0.01 0.07 0.02
imbalance of algo 6-10 −0.03 0.14 −0.00 0.03 −0.11 −0.01
quad var 15m −0.06 −0.04 0.04 −0.02 −0.13 −0.02
imbalance of algo 0-5 0.01 0.09 −0.00 −0.02 −0.15 −0.04
num messages 1ms −0.08 −0.10 −0.00 −0.05 0.01 −0.06
quad var 60m −0.05 −0.04 0.03 −0.02 −0.14 0.00
volume excl algo 6-10 0.01 0.05 0.10 0.05 −0.00 −0.01
cash of member −0.00 −0.00 −0.03 −0.02 0.00 0.12
inventory of member 0.00 −0.00 −0.03 −0.02 0.00 0.12
Dt= 1 (Buy) Dt=−1 (Sell)
Cluster 1 Cluster 2 Cluster 3 Cluster 1 Cluster 2 Cluster 3
intercept −0.06 1.07 0.34 0.06 1.02 0.33
volume of algo 0-5 0.26 0.00 0.41 0.21 0.11 0.25
imbalance of algo 0-5 0.01 −0.30 −0.04 −0.06 0.46 0.03
best bid volume −0.11 −0.37 −0.04 0.06 0.06 0.01
best ask volume 0.05 0.03 0.05 −0.12 −0.28 −0.07
volume of algo 11-20 0.18 −0.06 0.06 0.10 −0.09 0.09
imbalance of algo 6-10 0.03 −0.19 −0.01 −0.12 0.22 0.02
volume of algo 6-10 0.15 0.10 0.13 0.09 0.04 −0.02
num messages 1ms −0.07 −0.09 −0.15 −0.07 −0.04 −0.08
volume excl algo 0-5 −0.09 −0.05 −0.05 −0.07 −0.14 −0.07
Pt= 3 spread −0.04 −0.15 −0.03 −0.07 −0.13 −0.04
imbalance excl algo 6-10 0.00 0.02 −0.23 −0.01 −0.10 −0.09
num messages 0.1ms −0.04 −0.11 −0.10 −0.07 −0.05 −0.03
imbalance of algo 11-20 −0.08 −0.13 −0.01 0.01 0.12 −0.03
num messages 0.1s −0.07 0.01 −0.12 0.02 0.02 −0.13
cash of algorithm 0.00 0.05 −0.02 0.05 −0.07 0.14
inventory of algo 0.00 0.05 −0.02 0.05 −0.07 0.14
volume excl algo 11-20 −0.04 −0.04 −0.11 −0.04 −0.02 0.06
imbalance excl algo 0-5 −0.08 0.02 0.04 0.02 0.11 0.03
imbalance excl algo 11-20 −0.02 −0.01 −0.12 0.01 −0.01 0.13
quad var 15m −0.01 0.12 0.03 0.00 0.10 0.03
quad var 60m −0.01 0.12 0.04 0.00 0.08 0.01
agg sell last 360s −0.04 0.01 −0.11 −0.04 −0.00 −0.06
Table A18: Average regression coefficients for price bucket describing eager-to-trade orders (Pt= 1), at-the-
touch orders (Pt= 2), and orders deeper in the LOB (Pt= 3) per cluster, on first four weeks of training
data for TOMTOM, conditioning on the direction of orderDt.
This is a offline tool, your data stays locally and is not send to any server!
Feedback & Bug Reports