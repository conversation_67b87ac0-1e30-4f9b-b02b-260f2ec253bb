"""
Simple BM25 + Arabic-BERT Embeddings

Focus: Fast, effective embeddings for Arabic financial text.
Start simple, measure retrieval performance, optimize based on results.
"""

import numpy as np
from typing import List, Dict, Tuple, Optional
import pickle
from pathlib import Path
import time

# Try to import ML libraries, fallback gracefully
try:
    from sentence_transformers import SentenceTransformer
    import torch
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    print("Warning: sentence-transformers not available. Install with: pip install sentence-transformers")
    TRANSFORMERS_AVAILABLE = False

try:
    from rank_bm25 import BM25Okapi
    from sklearn.metrics.pairwise import cosine_similarity
    SKLEARN_AVAILABLE = True
except ImportError:
    print("Warning: BM25/sklearn not available. Install with: pip install rank-bm25 scikit-learn")
    SKLEARN_AVAILABLE = False

class SimpleEmbedder:
    """
    Hybrid embedder combining BM25 and Arabic-BERT
    
    Design principles:
    1. Start with pre-trained models (no fine-tuning initially)
    2. Measure retrieval performance at each step
    3. Simple but effective combination strategy
    4. Fast inference for real-time use
    """
    
    def __init__(self, 
                 arabic_model: str = "aubmindlab/bert-base-arabertv2",
                 cache_dir: str = "cache/embeddings"):
        
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize Arabic-BERT
        self.dense_model = None
        if TRANSFORMERS_AVAILABLE:
            try:
                print(f"Loading Arabic-BERT model: {arabic_model}")
                self.dense_model = SentenceTransformer(arabic_model)
                self.embedding_dim = self.dense_model.get_sentence_embedding_dimension()
                print(f"Model loaded. Embedding dimension: {self.embedding_dim}")
            except Exception as e:
                print(f"Failed to load Arabic-BERT: {e}")
                TRANSFORMERS_AVAILABLE = False
        
        # Initialize BM25 (will be fitted on corpus)
        self.bm25 = None
        self.corpus_texts = []
        self.fitted = False
        
        # Performance tracking
        self.stats = {
            'embeddings_generated': 0,
            'cache_hits': 0,
            'total_inference_time': 0.0
        }
    
    def fit_bm25(self, texts: List[str]) -> bool:
        """
        Fit BM25 on corpus for sparse retrieval
        """
        if not SKLEARN_AVAILABLE:
            print("BM25 not available - skipping sparse fitting")
            return False
        
        if not texts:
            print("No texts provided for BM25 fitting")
            return False
        
        print(f"Fitting BM25 on {len(texts)} texts...")
        start_time = time.time()
        
        # Tokenize texts (simple whitespace tokenization for Arabic)
        tokenized_texts = [text.split() for text in texts]
        
        # Fit BM25
        self.bm25 = BM25Okapi(tokenized_texts)
        self.corpus_texts = texts
        self.fitted = True
        
        fit_time = time.time() - start_time
        print(f"BM25 fitted in {fit_time:.2f} seconds")
        
        return True
    
    def encode_dense(self, texts: List[str], batch_size: int = 32) -> np.ndarray:
        """
        Generate dense embeddings using Arabic-BERT
        """
        if not TRANSFORMERS_AVAILABLE or not self.dense_model:
            print("Dense embeddings not available")
            return np.array([])
        
        if not texts:
            return np.array([])
        
        start_time = time.time()
        
        # Check cache first
        cached_embeddings = []
        texts_to_encode = []
        cache_indices = []
        
        for i, text in enumerate(texts):
            cache_key = self._get_cache_key(text)
            cache_file = self.cache_dir / f"{cache_key}.npy"
            
            if cache_file.exists():
                cached_embeddings.append((i, np.load(cache_file)))
                self.stats['cache_hits'] += 1
            else:
                texts_to_encode.append(text)
                cache_indices.append(i)
        
        # Generate embeddings for uncached texts
        new_embeddings = []
        if texts_to_encode:
            print(f"Generating embeddings for {len(texts_to_encode)} texts...")
            
            embeddings = self.dense_model.encode(
                texts_to_encode,
                batch_size=batch_size,
                show_progress_bar=len(texts_to_encode) > 50,
                convert_to_numpy=True,
                normalize_embeddings=True
            )
            
            # Cache new embeddings
            for i, (text, embedding) in enumerate(zip(texts_to_encode, embeddings)):
                cache_key = self._get_cache_key(text)
                cache_file = self.cache_dir / f"{cache_key}.npy"
                np.save(cache_file, embedding)
                
                new_embeddings.append((cache_indices[i], embedding))
        
        # Combine cached and new embeddings in correct order
        all_embeddings = [None] * len(texts)
        
        for idx, embedding in cached_embeddings + new_embeddings:
            all_embeddings[idx] = embedding
        
        result = np.array(all_embeddings)
        
        # Update stats
        inference_time = time.time() - start_time
        self.stats['embeddings_generated'] += len(texts_to_encode)
        self.stats['total_inference_time'] += inference_time
        
        print(f"Generated {len(texts)} embeddings in {inference_time:.2f}s "
              f"({self.stats['cache_hits']} cache hits)")
        
        return result
    
    def search_bm25(self, query: str, top_k: int = 10) -> List[Tuple[int, float]]:
        """
        Search using BM25 sparse retrieval
        """
        if not self.fitted or not SKLEARN_AVAILABLE:
            return []
        
        # Tokenize query
        query_tokens = query.split()
        
        # Get BM25 scores
        scores = self.bm25.get_scores(query_tokens)
        
        # Get top-k indices
        top_indices = np.argsort(scores)[-top_k:][::-1]
        
        return [(idx, scores[idx]) for idx in top_indices if scores[idx] > 0]
    
    def search_dense(self, query: str, corpus_embeddings: np.ndarray, 
                    top_k: int = 10) -> List[Tuple[int, float]]:
        """
        Search using dense embeddings
        """
        if not TRANSFORMERS_AVAILABLE or corpus_embeddings.size == 0:
            return []
        
        # Generate query embedding
        query_embedding = self.encode_dense([query])[0]
        
        # Calculate cosine similarities
        similarities = cosine_similarity([query_embedding], corpus_embeddings)[0]
        
        # Get top-k indices
        top_indices = np.argsort(similarities)[-top_k:][::-1]
        
        return [(idx, similarities[idx]) for idx in top_indices]
    
    def search_hybrid(self, query: str, corpus_embeddings: np.ndarray,
                     top_k: int = 10, alpha: float = 0.7) -> List[Tuple[int, float, Dict]]:
        """
        Hybrid search combining BM25 and dense embeddings
        
        Args:
            query: Search query
            corpus_embeddings: Pre-computed dense embeddings
            top_k: Number of results
            alpha: Weight for dense scores (1-alpha for sparse)
        
        Returns:
            List of (index, combined_score, score_breakdown)
        """
        # Get dense results
        dense_results = self.search_dense(query, corpus_embeddings, top_k * 2)
        
        # Get sparse results
        sparse_results = self.search_bm25(query, top_k * 2)
        
        if not dense_results and not sparse_results:
            return []
        
        # Combine scores
        combined_scores = {}
        
        # Normalize and add dense scores
        if dense_results:
            dense_scores = [score for _, score in dense_results]
            dense_min, dense_max = min(dense_scores), max(dense_scores)
            dense_range = dense_max - dense_min if dense_max > dense_min else 1.0
            
            for idx, score in dense_results:
                normalized_dense = (score - dense_min) / dense_range
                combined_scores[idx] = {
                    'dense': normalized_dense,
                    'sparse': 0.0,
                    'combined': alpha * normalized_dense
                }
        
        # Normalize and add sparse scores
        if sparse_results:
            sparse_scores = [score for _, score in sparse_results]
            sparse_min, sparse_max = min(sparse_scores), max(sparse_scores)
            sparse_range = sparse_max - sparse_min if sparse_max > sparse_min else 1.0
            
            for idx, score in sparse_results:
                normalized_sparse = (score - sparse_min) / sparse_range
                
                if idx in combined_scores:
                    combined_scores[idx]['sparse'] = normalized_sparse
                    combined_scores[idx]['combined'] = (
                        alpha * combined_scores[idx]['dense'] + 
                        (1 - alpha) * normalized_sparse
                    )
                else:
                    combined_scores[idx] = {
                        'dense': 0.0,
                        'sparse': normalized_sparse,
                        'combined': (1 - alpha) * normalized_sparse
                    }
        
        # Sort by combined score
        sorted_results = sorted(
            combined_scores.items(),
            key=lambda x: x[1]['combined'],
            reverse=True
        )[:top_k]
        
        return [(idx, scores['combined'], scores) for idx, scores in sorted_results]
    
    def evaluate_retrieval(self, queries: List[str], 
                          relevant_docs: List[List[int]],
                          corpus_embeddings: np.ndarray,
                          k_values: List[int] = [1, 5, 10]) -> Dict:
        """
        Evaluate retrieval performance using recall@k
        
        Args:
            queries: List of query strings
            relevant_docs: List of relevant document indices for each query
            corpus_embeddings: Pre-computed corpus embeddings
            k_values: K values to evaluate
        
        Returns:
            Dictionary with recall@k scores
        """
        if not queries or not relevant_docs:
            return {}
        
        results = {
            'dense_recall': {k: [] for k in k_values},
            'sparse_recall': {k: [] for k in k_values},
            'hybrid_recall': {k: [] for k in k_values}
        }
        
        for query, relevant in zip(queries, relevant_docs):
            if not relevant:
                continue
            
            # Dense retrieval
            dense_results = self.search_dense(query, corpus_embeddings, max(k_values))
            dense_retrieved = [idx for idx, _ in dense_results]
            
            # Sparse retrieval
            sparse_results = self.search_bm25(query, max(k_values))
            sparse_retrieved = [idx for idx, _ in sparse_results]
            
            # Hybrid retrieval
            hybrid_results = self.search_hybrid(query, corpus_embeddings, max(k_values))
            hybrid_retrieved = [idx for idx, _, _ in hybrid_results]
            
            # Calculate recall@k for each method
            for k in k_values:
                # Dense recall@k
                dense_k = dense_retrieved[:k]
                dense_recall = len(set(dense_k) & set(relevant)) / len(relevant)
                results['dense_recall'][k].append(dense_recall)
                
                # Sparse recall@k
                sparse_k = sparse_retrieved[:k]
                sparse_recall = len(set(sparse_k) & set(relevant)) / len(relevant)
                results['sparse_recall'][k].append(sparse_recall)
                
                # Hybrid recall@k
                hybrid_k = hybrid_retrieved[:k]
                hybrid_recall = len(set(hybrid_k) & set(relevant)) / len(relevant)
                results['hybrid_recall'][k].append(hybrid_recall)
        
        # Average recall scores
        avg_results = {}
        for method in results:
            avg_results[method] = {}
            for k in k_values:
                scores = results[method][k]
                avg_results[method][k] = sum(scores) / len(scores) if scores else 0.0
        
        return avg_results
    
    def _get_cache_key(self, text: str) -> str:
        """Generate cache key for text"""
        import hashlib
        return hashlib.md5(text.encode('utf-8')).hexdigest()
    
    def save_model(self, path: str):
        """Save fitted BM25 model"""
        if not self.fitted:
            print("No fitted model to save")
            return
        
        save_data = {
            'bm25': self.bm25,
            'corpus_texts': self.corpus_texts,
            'stats': self.stats
        }
        
        with open(path, 'wb') as f:
            pickle.dump(save_data, f)
        
        print(f"Model saved to {path}")
    
    def load_model(self, path: str):
        """Load fitted BM25 model"""
        try:
            with open(path, 'rb') as f:
                save_data = pickle.load(f)
            
            self.bm25 = save_data['bm25']
            self.corpus_texts = save_data['corpus_texts']
            self.stats = save_data.get('stats', self.stats)
            self.fitted = True
            
            print(f"Model loaded from {path}")
            return True
        except Exception as e:
            print(f"Failed to load model: {e}")
            return False
    
    def get_stats(self) -> Dict:
        """Get performance statistics"""
        avg_time = (self.stats['total_inference_time'] / 
                   max(1, self.stats['embeddings_generated']))
        
        return {
            **self.stats,
            'avg_inference_time_per_text': avg_time,
            'cache_hit_rate': (self.stats['cache_hits'] / 
                              max(1, self.stats['cache_hits'] + self.stats['embeddings_generated'])),
            'fitted': self.fitted,
            'corpus_size': len(self.corpus_texts) if self.fitted else 0
        }
