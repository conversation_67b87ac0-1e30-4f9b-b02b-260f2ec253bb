"""
Hybrid embedding fusion for combining Swan and FinBERT representations.

This module defines a class that takes sentence embeddings from Swan
and sentiment probabilities from FinBERT (or another sentiment model)
and produces a single feature vector for downstream models.  This can
be achieved by concatenation, weighted averaging or a small neural
network.  The specifics are left for future implementation.
"""

import numpy as np
from typing import List


class HybridFusion:
    """
    Fuse semantic and sentiment representations into a single vector.
    """

    def __init__(self, semantic_dim: int, sentiment_dim: int = 1):
        self.semantic_dim = semantic_dim
        self.sentiment_dim = sentiment_dim

    def fuse(self, semantic_vectors: np.ndarray, sentiment_scores: List[float]) -> np.ndarray:
        """
        Concatenate semantic vectors with sentiment scores.
        """
        sentiment_array = np.array(sentiment_scores).reshape(-1, self.sentiment_dim)
        return np.hstack([semantic_vectors, sentiment_array])
