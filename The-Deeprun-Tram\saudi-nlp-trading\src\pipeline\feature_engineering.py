"""
Feature engineering utilities for the trading model.

This module can define a library of functions to compute financial features
such as hype metrics, influencer heat, graph metrics and volatility flags.
"""

from typing import List


def compute_hype_ratio(post_counts: List[int], ema_window: int = 30) -> float:
    """
    Compute the hype ratio: post_count[t] / EMA_30d.
    """
    if not post_counts:
        return 0.0
    import pandas as pd
    series = pd.Series(post_counts)
    ema = series.ewm(span=ema_window, adjust=False).mean().iloc[-1]
    return series.iloc[-1] / ema if ema else 0.0
