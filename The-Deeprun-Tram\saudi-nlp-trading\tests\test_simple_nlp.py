"""
Comprehensive tests for Simple NLP Pipeline

Focus: Ensure all components work correctly and produce valid trading signals.
"""

import unittest
import sys
from pathlib import Path
import numpy as np
import pandas as pd

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from nlp.simple_preprocessor import ArabicPreprocessor
from nlp.simple_sentiment import SimpleSentimentAnalyzer
from nlp.simple_pipeline import SimpleNLPPipeline

class TestArabicPreprocessor(unittest.TestCase):
    """Test Arabic preprocessing functionality"""
    
    def setUp(self):
        self.preprocessor = ArabicPreprocessor()
    
    def test_arabic_normalization(self):
        """Test Arabic text normalization"""
        # Test diacritics removal
        text_with_diacritics = "الرّاجحي سهم قويّ"
        normalized = self.preprocessor.normalize_arabic(text_with_diacritics)
        self.assertNotIn('ّ', normalized)
        self.assertIn('الراجحي', normalized)
        
        # Test elongation removal
        elongated_text = "الراجحيييييي صاااااعد"
        normalized = self.preprocessor.normalize_arabic(elongated_text)
        self.assertNotIn('ييييي', normalized)
        self.assertNotIn('اااا', normalized)
        
        # Test Arabic numeral conversion
        arabic_nums = "ارتفع ٥٪ اليوم"
        normalized = self.preprocessor.normalize_arabic(arabic_nums)
        self.assertIn('5%', normalized)
        self.assertNotIn('٥', normalized)
    
    def test_company_extraction(self):
        """Test Saudi company extraction"""
        text = "سهم الراجحي 1120 ارتفع اليوم"
        entities = self.preprocessor.extract_financial_entities(text)
        
        self.assertIn('الراجحي', entities['companies'])
        self.assertIn('1120', entities['tickers'])
    
    def test_percentage_extraction(self):
        """Test percentage extraction"""
        text = "السهم ارتفع +3.5% وتراجع -2%"
        entities = self.preprocessor.extract_financial_entities(text)
        
        self.assertIn('+3.5%', entities['percentages'])
        self.assertIn('-2%', entities['percentages'])
    
    def test_sentiment_scoring(self):
        """Test basic sentiment scoring"""
        bullish_text = "السهم صاعد وقوي جداً"
        score, indicators = self.preprocessor.calculate_sentiment_score(bullish_text)
        self.assertGreater(score, 0.5)
        self.assertTrue(any('bullish' in ind for ind in indicators))
        
        bearish_text = "السهم هابط وضعيف"
        score, indicators = self.preprocessor.calculate_sentiment_score(bearish_text)
        self.assertLess(score, 0.5)
        self.assertTrue(any('bearish' in ind for ind in indicators))
    
    def test_post_processing(self):
        """Test complete post processing"""
        post_content = "الراجحي سهم ممتاز! ارتفع 3% اليوم 📈"
        result = self.preprocessor.process_post(post_content, "test_user")
        
        self.assertTrue(result['valid'])
        self.assertTrue(result['has_financial_content'])
        self.assertIn('الراجحي', result['entities']['companies'])
        self.assertEqual(result['sentiment_label'], 'bullish')
        self.assertGreater(result['sentiment_score'], 0.5)

class TestSentimentAnalyzer(unittest.TestCase):
    """Test sentiment analysis functionality"""
    
    def setUp(self):
        self.analyzer = SimpleSentimentAnalyzer(use_model=False)  # Lexicon only for testing
    
    def test_bullish_sentiment(self):
        """Test bullish sentiment detection"""
        bullish_texts = [
            "السهم صاعد بقوة",
            "فرصة ممتازة للاستثمار",
            "أرباح قوية ونمو مستمر"
        ]
        
        for text in bullish_texts:
            result = self.analyzer.analyze_sentiment(text)
            self.assertEqual(result['sentiment'], 'bullish')
            self.assertGreater(result['score'], 0.6)
    
    def test_bearish_sentiment(self):
        """Test bearish sentiment detection"""
        bearish_texts = [
            "السهم هابط بقوة",
            "خسائر كبيرة وتراجع حاد",
            "توصية بيع فوري"
        ]
        
        for text in bearish_texts:
            result = self.analyzer.analyze_sentiment(text)
            self.assertEqual(result['sentiment'], 'bearish')
            self.assertLess(result['score'], 0.4)
    
    def test_neutral_sentiment(self):
        """Test neutral sentiment detection"""
        neutral_texts = [
            "السهم مستقر اليوم",
            "لا توجد حركة واضحة",
            "السوق هادئ"
        ]
        
        for text in neutral_texts:
            result = self.analyzer.analyze_sentiment(text)
            # Should be neutral or have low confidence
            self.assertTrue(
                result['sentiment'] == 'neutral' or 
                result['confidence'] < 0.5
            )
    
    def test_batch_analysis(self):
        """Test batch sentiment analysis"""
        texts = [
            "السهم صاعد",
            "السهم هابط", 
            "السهم مستقر"
        ]
        
        results = self.analyzer.analyze_batch(texts)
        self.assertEqual(len(results), 3)
        
        # Check that we get different sentiments
        sentiments = [r['sentiment'] for r in results]
        self.assertIn('bullish', sentiments)
        self.assertIn('bearish', sentiments)

class TestSimplePipeline(unittest.TestCase):
    """Test complete pipeline functionality"""
    
    def setUp(self):
        self.pipeline = SimpleNLPPipeline(
            enable_embeddings=False,  # Disable for testing
            enable_model_sentiment=False
        )
        
        # Sample test posts
        self.test_posts = [
            {
                'post_id': 'test_001',
                'content': 'الراجحي سهم ممتاز! ارتفع 3.5% اليوم',
                'author': 'trader1',
                'timestamp': '2024-08-09T10:00:00',
                'source': 'hawamer',
                'likes': 10,
                'shares': 2
            },
            {
                'post_id': 'test_002',
                'content': 'أرامكو تراجعت 2% بسبب أسعار النفط',
                'author': 'analyst1',
                'timestamp': '2024-08-09T11:00:00',
                'source': 'hawamer',
                'likes': 5,
                'shares': 1
            },
            {
                'post_id': 'test_003',
                'content': 'سابك حققت أرباح قوية في الربع الثاني',
                'author': 'investor1',
                'timestamp': '2024-08-09T12:00:00',
                'source': 'hawamer',
                'likes': 15,
                'shares': 3
            }
        ]
    
    def test_single_post_processing(self):
        """Test processing of single post"""
        post = self.test_posts[0]
        result = self.pipeline.process_single_post(post)
        
        self.assertIsNotNone(result)
        self.assertEqual(result['post_id'], 'test_001')
        self.assertTrue(result['has_financial_content'])
        self.assertIn('الراجحي', result['companies'])
        self.assertEqual(result['sentiment'], 'bullish')
    
    def test_batch_processing(self):
        """Test batch processing"""
        df = self.pipeline.process_batch(self.test_posts)
        
        self.assertEqual(len(df), 3)
        self.assertTrue(all(df['has_financial_content']))
        
        # Check that we have different sentiments
        sentiments = df['sentiment'].unique()
        self.assertGreater(len(sentiments), 1)
    
    def test_feature_generation(self):
        """Test trading feature generation"""
        df = self.pipeline.process_batch(self.test_posts)
        features_df = self.pipeline.generate_trading_features(df, time_window='1H')
        
        self.assertFalse(features_df.empty)
        
        # Check required columns
        required_columns = [
            'ticker', 'timestamp', 'post_count', 'sentiment_mean',
            'bullish_ratio', 'bearish_ratio'
        ]
        for col in required_columns:
            self.assertIn(col, features_df.columns)
        
        # Check that we have features for multiple tickers
        unique_tickers = features_df['ticker'].nunique()
        self.assertGreater(unique_tickers, 1)
    
    def test_pipeline_evaluation(self):
        """Test pipeline evaluation"""
        metrics = self.pipeline.evaluate_pipeline(self.test_posts)
        
        # Check required metrics
        required_metrics = [
            'total_input_posts', 'valid_processed_posts',
            'processing_success_rate', 'financial_posts_ratio'
        ]
        for metric in required_metrics:
            self.assertIn(metric, metrics)
        
        # Check reasonable values
        self.assertEqual(metrics['total_input_posts'], 3)
        self.assertGreater(metrics['processing_success_rate'], 0.5)

class TestDataQuality(unittest.TestCase):
    """Test data quality and edge cases"""
    
    def setUp(self):
        self.preprocessor = ArabicPreprocessor()
        self.analyzer = SimpleSentimentAnalyzer(use_model=False)
    
    def test_empty_content(self):
        """Test handling of empty content"""
        empty_posts = [
            {'content': ''},
            {'content': '   '},
            {'content': None},
            {}
        ]
        
        for post in empty_posts:
            result = self.preprocessor.process_post(post.get('content', ''))
            self.assertFalse(result['valid'])
    
    def test_non_financial_content(self):
        """Test handling of non-financial content"""
        non_financial_posts = [
            "مرحبا كيف الحال؟",
            "الطقس جميل اليوم",
            "أحب القهوة العربية"
        ]
        
        for content in non_financial_posts:
            result = self.preprocessor.process_post(content)
            self.assertTrue(result['valid'])
            self.assertFalse(result['has_financial_content'])
    
    def test_mixed_content(self):
        """Test handling of mixed Arabic-English content"""
        mixed_content = "الراجحي stock is good للاستثمار"
        result = self.preprocessor.process_post(mixed_content)
        
        self.assertTrue(result['valid'])
        self.assertTrue(result['has_financial_content'])
    
    def test_very_long_content(self):
        """Test handling of very long content"""
        long_content = "الراجحي سهم ممتاز " * 100  # Very long text
        result = self.preprocessor.process_post(long_content)
        
        self.assertTrue(result['valid'])
        self.assertGreater(result['text_length'], 1000)
    
    def test_special_characters(self):
        """Test handling of special characters and emojis"""
        special_content = "الراجحي 📈📈📈 سهم قوي!!! 🚀🚀"
        result = self.preprocessor.process_post(special_content)
        
        self.assertTrue(result['valid'])
        # Clean text should have emojis removed
        self.assertNotIn('📈', result['clean_text'])

def run_performance_test():
    """Run performance test on larger dataset"""
    print("\nRunning performance test...")
    
    # Generate test data
    test_posts = []
    companies = ['الراجحي', 'أرامكو', 'سابك', 'الاتصالات']
    sentiments = ['صاعد', 'هابط', 'مستقر']
    
    for i in range(1000):
        company = companies[i % len(companies)]
        sentiment = sentiments[i % len(sentiments)]
        
        post = {
            'post_id': f'perf_test_{i}',
            'content': f'{company} سهم {sentiment} اليوم',
            'author': f'user_{i % 100}',
            'timestamp': f'2024-08-09T{10 + i % 12}:00:00',
            'source': 'test'
        }
        test_posts.append(post)
    
    # Test pipeline performance
    pipeline = SimpleNLPPipeline(enable_embeddings=False, enable_model_sentiment=False)
    
    import time
    start_time = time.time()
    
    df = pipeline.process_batch(test_posts, batch_size=100)
    features_df = pipeline.generate_trading_features(df)
    
    end_time = time.time()
    
    print(f"Processed {len(test_posts)} posts in {end_time - start_time:.2f} seconds")
    print(f"Average time per post: {(end_time - start_time) / len(test_posts) * 1000:.2f} ms")
    print(f"Generated {len(features_df)} feature rows")
    
    return True

if __name__ == '__main__':
    # Run unit tests
    print("Running unit tests...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run performance test
    run_performance_test()
    
    print("\nAll tests completed!")
