"""
Manifest and Checksum Management for Saudi NLP Trading

Creates manifests with SHA256 checksums for all output files.
Enables idempotent runs and data integrity verification.
"""

import json
import hashlib
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
import os

from .logging import get_logger
from ..config.settings import get_config, RIYADH_TZ

class FileManifest:
    """Represents a file in the manifest with metadata"""
    
    def __init__(self, file_path: Path, base_path: Optional[Path] = None):
        self.file_path = file_path
        self.base_path = base_path or Path.cwd()
        self.logger = get_logger(__name__)
    
    def calculate_checksum(self) -> str:
        """Calculate SHA256 checksum of file"""
        sha256_hash = hashlib.sha256()
        
        try:
            with open(self.file_path, "rb") as f:
                # Read file in chunks to handle large files
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
            
            return sha256_hash.hexdigest()
            
        except Exception as e:
            self.logger.error(
                "Failed to calculate checksum",
                file_path=str(self.file_path),
                exception_class=e.__class__.__name__,
                exc_info=True
            )
            raise
    
    def get_metadata(self) -> Dict[str, Any]:
        """Get file metadata with POSIX path normalization"""
        try:
            stat = self.file_path.stat()

            # Calculate relative path
            try:
                relative_path = self.file_path.relative_to(self.base_path)
            except ValueError:
                relative_path = self.file_path

            # Always use POSIX paths in manifest (cross-platform compatibility)
            posix_path = relative_path.as_posix()
            platform_path = str(relative_path)

            return {
                'path': posix_path,  # Canonical POSIX path
                'platform_path': platform_path if platform_path != posix_path else None,
                'absolute_path': str(self.file_path.absolute()),
                'size_bytes': stat.st_size,
                'created_at': datetime.fromtimestamp(stat.st_ctime, RIYADH_TZ).isoformat(),
                'modified_at': datetime.fromtimestamp(stat.st_mtime, RIYADH_TZ).isoformat(),
                'checksum_sha256': self.calculate_checksum()
            }
            
        except Exception as e:
            self.logger.error(
                "Failed to get file metadata",
                file_path=str(self.file_path),
                exception_class=e.__class__.__name__,
                exc_info=True
            )
            raise

class ManifestManager:
    """Manages manifest creation and validation"""
    
    def __init__(self, run_id: str):
        self.run_id = run_id
        self.config = get_config()
        self.logger = get_logger(__name__)
        self.manifest_dir = Path('manifests')
        self.manifest_dir.mkdir(exist_ok=True)
        self.manifest_file = self.manifest_dir / f'run_{run_id}.json'
        
        # Initialize manifest structure
        self.manifest = {
            'schema_version': '1.0',
            'run_id': run_id,
            'created_at': datetime.now(RIYADH_TZ).isoformat(),
            'git_commit': self._get_git_commit(),
            'runtime': self._get_runtime_info(),
            'config_snapshot': self._get_config_snapshot(),
            'partition_policy': {
                'max_size_mb': 50,
                'max_records': 10000,
                'compression_threshold_mb': 10
            },
            'files': {},
            'stats': {
                'total_files': 0,
                'total_size_bytes': 0,
                'file_types': {}
            }
        }
    
    def add_file(self, file_path: Path, category: str = 'data', 
                 metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        Add file to manifest
        
        Args:
            file_path: Path to file
            category: File category (data, logs, reports, etc.)
            metadata: Additional metadata
        """
        
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        try:
            # Create file manifest
            file_manifest = FileManifest(file_path)
            file_metadata = file_manifest.get_metadata()
            
            # Add category and additional metadata
            file_metadata['category'] = category
            if metadata:
                file_metadata.update(metadata)
            
            # Add to manifest
            file_key = str(file_path.name)
            if file_key in self.manifest['files']:
                # Handle duplicate names by adding suffix
                counter = 1
                base_name = file_path.stem
                suffix = file_path.suffix
                while f"{base_name}_{counter}{suffix}" in self.manifest['files']:
                    counter += 1
                file_key = f"{base_name}_{counter}{suffix}"
            
            self.manifest['files'][file_key] = file_metadata
            
            # Update stats
            self.manifest['stats']['total_files'] += 1
            self.manifest['stats']['total_size_bytes'] += file_metadata['size_bytes']
            
            # Update file type stats
            file_ext = file_path.suffix.lower()
            if file_ext in self.manifest['stats']['file_types']:
                self.manifest['stats']['file_types'][file_ext] += 1
            else:
                self.manifest['stats']['file_types'][file_ext] = 1
            
            self.logger.info(
                "Added file to manifest",
                file_path=str(file_path),
                category=category,
                size_bytes=file_metadata['size_bytes'],
                checksum=file_metadata['checksum_sha256'][:16]  # First 16 chars
            )
            
        except Exception as e:
            self.logger.error(
                "Failed to add file to manifest",
                file_path=str(file_path),
                category=category,
                exception_class=e.__class__.__name__,
                exc_info=True
            )
            raise
    
    def add_directory(self, dir_path: Path, category: str = 'data',
                     pattern: str = '*', recursive: bool = True) -> None:
        """
        Add all files in directory to manifest
        
        Args:
            dir_path: Directory path
            category: File category
            pattern: File pattern to match
            recursive: Whether to search recursively
        """
        
        if not dir_path.exists() or not dir_path.is_dir():
            raise ValueError(f"Directory not found: {dir_path}")
        
        # Find files
        if recursive:
            files = list(dir_path.rglob(pattern))
        else:
            files = list(dir_path.glob(pattern))
        
        # Filter to only files (not directories)
        files = [f for f in files if f.is_file()]
        
        self.logger.info(
            "Adding directory to manifest",
            dir_path=str(dir_path),
            pattern=pattern,
            recursive=recursive,
            file_count=len(files)
        )
        
        for file_path in files:
            try:
                # Calculate relative category based on subdirectory
                relative_path = file_path.relative_to(dir_path)
                if len(relative_path.parts) > 1:
                    subcategory = f"{category}/{relative_path.parts[0]}"
                else:
                    subcategory = category
                
                self.add_file(file_path, subcategory)
                
            except Exception as e:
                self.logger.warning(
                    "Failed to add file from directory",
                    file_path=str(file_path),
                    exception_class=e.__class__.__name__
                )
    
    def save_manifest(self) -> Path:
        """Save manifest to file"""
        try:
            # Update completion timestamp
            self.manifest['completed_at'] = datetime.now(RIYADH_TZ).isoformat()
            
            # Calculate manifest checksum
            manifest_content = json.dumps(self.manifest, sort_keys=True, indent=2)
            manifest_hash = hashlib.sha256(manifest_content.encode('utf-8')).hexdigest()
            self.manifest['manifest_checksum'] = manifest_hash
            
            # Save to file
            with open(self.manifest_file, 'w', encoding='utf-8') as f:
                json.dump(self.manifest, f, indent=2, ensure_ascii=False)
            
            self.logger.info(
                "Saved manifest",
                manifest_file=str(self.manifest_file),
                total_files=self.manifest['stats']['total_files'],
                total_size_mb=self.manifest['stats']['total_size_bytes'] / 1024 / 1024,
                manifest_checksum=manifest_hash[:16]
            )
            
            return self.manifest_file
            
        except Exception as e:
            self.logger.error(
                "Failed to save manifest",
                manifest_file=str(self.manifest_file),
                exception_class=e.__class__.__name__,
                exc_info=True
            )
            raise
    
    def validate_manifest(self, manifest_file: Optional[Path] = None) -> bool:
        """
        Validate manifest by checking file checksums
        
        Args:
            manifest_file: Manifest file to validate (uses current if None)
        
        Returns:
            True if all files match their checksums
        """
        
        if manifest_file is None:
            manifest_file = self.manifest_file
        
        if not manifest_file.exists():
            raise FileNotFoundError(f"Manifest file not found: {manifest_file}")
        
        try:
            # Load manifest
            with open(manifest_file, 'r', encoding='utf-8') as f:
                manifest_data = json.load(f)
            
            validation_results = {
                'total_files': len(manifest_data['files']),
                'valid_files': 0,
                'missing_files': 0,
                'checksum_mismatches': 0,
                'errors': []
            }
            
            # Validate each file
            for file_key, file_metadata in manifest_data['files'].items():
                file_path = Path(file_metadata['absolute_path'])
                
                try:
                    if not file_path.exists():
                        validation_results['missing_files'] += 1
                        validation_results['errors'].append(f"Missing file: {file_path}")
                        continue
                    
                    # Calculate current checksum
                    file_manifest = FileManifest(file_path)
                    current_checksum = file_manifest.calculate_checksum()
                    expected_checksum = file_metadata['checksum_sha256']
                    
                    if current_checksum == expected_checksum:
                        validation_results['valid_files'] += 1
                    else:
                        validation_results['checksum_mismatches'] += 1
                        validation_results['errors'].append(
                            f"Checksum mismatch: {file_path} "
                            f"(expected: {expected_checksum[:16]}, got: {current_checksum[:16]})"
                        )
                
                except Exception as e:
                    validation_results['errors'].append(
                        f"Error validating {file_path}: {e}"
                    )
            
            # Log results
            is_valid = (validation_results['missing_files'] == 0 and 
                       validation_results['checksum_mismatches'] == 0)
            
            if is_valid:
                self.logger.info(
                    "Manifest validation passed",
                    manifest_file=str(manifest_file),
                    **validation_results
                )
            else:
                self.logger.error(
                    "Manifest validation failed",
                    manifest_file=str(manifest_file),
                    **validation_results
                )
            
            return is_valid
            
        except Exception as e:
            self.logger.error(
                "Failed to validate manifest",
                manifest_file=str(manifest_file),
                exception_class=e.__class__.__name__,
                exc_info=True
            )
            return False
    
    def _get_git_commit(self) -> str:
        """Get current git commit hash"""
        try:
            import subprocess
            result = subprocess.run(
                ['git', 'rev-parse', 'HEAD'],
                capture_output=True,
                text=True,
                timeout=5
            )
            if result.returncode == 0:
                return result.stdout.strip()
        except Exception:
            pass
        return 'unknown'

    def _get_runtime_info(self) -> Dict[str, Any]:
        """Get runtime environment information"""
        import platform
        import sys

        return {
            'python_version': sys.version,
            'platform': platform.platform(),
            'hostname': platform.node(),
            'architecture': platform.architecture()[0],
            'processor': platform.processor() or 'unknown'
        }

    def _get_config_snapshot(self) -> Dict[str, Any]:
        """Get snapshot of current configuration"""
        return {
            'scraper_max_rpm': self.config.scraper.max_rpm,
            'scraper_max_concurrency': self.config.scraper.max_concurrency,
            'storage_backend': self.config.storage.backend,
            'mode_scrape_only': self.config.mode.scrape_only,
            'respect_robots': self.config.scraper.respect_robots,
            'captcha_enabled': self.config.captcha.enabled,
            'dedup_enabled': self.config.mode.dedup_enabled
        }
    
    def get_manifest_summary(self) -> Dict[str, Any]:
        """Get summary of current manifest"""
        return {
            'run_id': self.run_id,
            'total_files': self.manifest['stats']['total_files'],
            'total_size_bytes': self.manifest['stats']['total_size_bytes'],
            'total_size_mb': self.manifest['stats']['total_size_bytes'] / 1024 / 1024,
            'file_types': self.manifest['stats']['file_types'],
            'categories': list(set(
                file_data['category'] for file_data in self.manifest['files'].values()
            ))
        }

def create_manifest_for_run(run_id: str) -> ManifestManager:
    """Create manifest manager for a run"""
    return ManifestManager(run_id)

def validate_run_manifest(run_id: str) -> bool:
    """Validate manifest for a specific run"""
    manifest_file = Path('manifests') / f'run_{run_id}.json'
    if not manifest_file.exists():
        return False
    
    manager = ManifestManager(run_id)
    return manager.validate_manifest(manifest_file)
